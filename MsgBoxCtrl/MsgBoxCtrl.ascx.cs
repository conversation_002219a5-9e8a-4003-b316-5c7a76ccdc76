﻿using System;

namespace MsgBoxCtrl
{
    public partial class MsgBoxCtrl : System.Web.UI.UserControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }

        /// <summary>
        /// Popup menü <PERSON>ır
        /// </summary>
        /// <param name="Baslik"></param>
        /// <param name="Mesaj"></param>
        /// <param name="<PERSON>a"></param>
        /// <param name="LinkGoster"></param>
        /// <param name="Link"></param>
        /// <param name="<PERSON>Yazi"></param>
        public void PopupGoster(string Baslik, string Mesaj, bool Hata, bool LinkGoster, string Link, string LinkYazi)
        {
            pnlMesajKutusu.Visible = true;
            pnlMesajKutusu.Attributes.Add("display", "");
            pnlSayfaKutusu.Visible = false;
            pnlSayfaKutusu.Attributes.Add("display", "none");

            lblBilgiMesaj.Text = Mesaj;
            lblBilgiBaslik.Text = Baslik;
            if (Hata)
            {
                imgBilgi.ImageUrl = "~/images/cancel.png";

                bodyDiv.Attributes.Add("class", "warningCss");
            }
            else
            {
                imgBilgi.ImageUrl = "~/images/ok.png";
                bodyDiv.Attributes.Add("class", "okCss");
            }
            if (LinkGoster)
            {
                lnkBilgi.Style.Add("display", "");
                lnkBilgi.NavigateUrl = Link;
                lnkBilgi.Text = LinkYazi;
                btnModalOk.Style.Add("display", "none");
            }
            else
            {
                lnkBilgi.Style.Add("display", "none");
                btnModalOk.Style.Add("display", "");
            }

            mpe.Show();
        }

        /// <summary>
        /// Pupup menü çıkarır (Linksiz)
        /// </summary>
        /// <param name="Baslik"></param>
        /// <param name="Mesaj"></param>
        /// <param name="Hata"></param>
        public void PopupGoster(string Baslik, string Mesaj, bool Hata)
        {
            pnlMesajKutusu.Visible = true;
            pnlMesajKutusu.Attributes.Add("display", "");
            pnlSayfaKutusu.Visible = false;
            pnlSayfaKutusu.Attributes.Add("display", "none");
            lblBilgiMesaj.Text = Mesaj;
            lblBilgiBaslik.Text = Baslik;
            if (Hata)
            {
                imgBilgi.ImageUrl = "~/images/cancel.png";

                bodyDiv.Attributes.Add("class", "warningCss");
            }
            else
            {
                imgBilgi.ImageUrl = "~/images/ok.png";
                bodyDiv.Attributes.Add("class", "okCss");
            }
            lnkBilgi.Style.Add("display", "none");
            mpe.Show();
        }

        /// <summary>
        /// Pupup menü çıkarır (Linksiz)
        /// </summary>
        /// <param name="Baslik"></param>
        /// <param name="Mesaj"></param>
        /// <param name="Hata"></param>
        public void SayfaGoster(string Baslik, string Mesaj, string adres, bool Hata)
        {
            pnlMesajKutusu.Visible = false;
            pnlMesajKutusu.Attributes.Add("display", "none");
            pnlSayfaKutusu.Visible = true;
            pnlSayfaKutusu.Attributes.Add("display", "");

            lblBilgiMesaj.Text = Mesaj;
            lblBilgiBaslik.Text = Baslik;
            if (Hata)
            {
                imgBilgi.ImageUrl = "~/images/cancel.png";

                bodyDiv.Attributes.Add("class", "warningCss");
            }
            else
            {
                imgBilgi.ImageUrl = "~/images/ok.png";
                bodyDiv.Attributes.Add("class", "okCss");
            }
            lnkBilgi.Style.Add("display", "none");
            myIframe.Attributes.Add("src", adres);
            mpe.Show();
        }
    }
}