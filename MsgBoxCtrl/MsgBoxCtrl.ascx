﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="MsgBoxCtrl.ascx.cs" Inherits="MsgBoxCtrl.MsgBoxCtrl" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>

<link href="css/style.css" rel="stylesheet" type="text/css" />

<asp:UpdatePanel ID="UpdatePanel1" runat="server">

    <ContentTemplate>

        <asp:ModalPopupExtender ID="mpe" runat="server" PopupControlID="pnlPopup" TargetControlID="lnkOnayButton"
            OkControlID="btnModalOk" BackgroundCssClass="modalBackground">
        </asp:ModalPopupExtender>
        <asp:Panel ID="pnlPopup" runat="server" CssClass="modalPopup" Style="display: none">
            <div class="header">
                <asp:Label ID="lblBilgiBaslik" runat="server" Text="Bilgi"></asp:Label>
            </div>
            <div id="bodyDiv" align="center" runat="server">
                <asp:Panel ID="pnlMesajKutusu" runat="server">
                    <table style="width: 300px">
                        <tr>
                            <td style="width: 50px" align="left">
                                <asp:Image ID="imgBilgi" Width="40px" runat="server" ImageUrl="~/images/ok.png" />
                            </td>
                            <td>
                                <asp:Label ID="lblBilgiMesaj" runat="server" Text="Mesaj"> </asp:Label>
                            </td>
                        </tr>
                    </table>

                    <br />

                    <br />

                    <hr />
                    <asp:HyperLink ID="lnkBilgi" runat="server"></asp:HyperLink>
                </asp:Panel>
                <asp:Panel ID="pnlSayfaKutusu" runat="server">
                    <iframe name="myIframe" id="myIframe" width="100%" height="100%" runat="server" style="border: 0px"></iframe>
                </asp:Panel>
                <asp:Button ID="btnModalOk" runat="server" Text="Tamam" CssClass="buton" />
            </div>
        </asp:Panel>
        <asp:Button ID="lnkOnayButton" runat="server" Style="display: none" Text="Aç" />
    </ContentTemplate>
</asp:UpdatePanel>
<asp:UpdatePanel runat="server" ID="updaPanelMaster">
    <ContentTemplate>

        <asp:UpdateProgress ID="UpdateProgress2" AssociatedUpdatePanelID="updaPanelMaster" DisplayAfter="1" runat="server" DynamicLayout="true">
            <ProgressTemplate>
                <div id="UpdBackground"></div>
                <div id="UpdProgress">
                    <b>Lütfen bekleyiniz...</b><br />
                    <br />
                    <img src="/images/ajax-loader.gif" style="vertical-align: middle" />&nbsp
                </div>
            </ProgressTemplate>
        </asp:UpdateProgress>
        <table align="center" width="700px">
            <asp:Literal ID="StatusLiteral" runat="server"></asp:Literal>
        </table>
    </ContentTemplate>
</asp:UpdatePanel>