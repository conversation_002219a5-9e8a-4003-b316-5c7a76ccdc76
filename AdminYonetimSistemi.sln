﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.32901.82
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Helpers", "Helpers", "{189B9E33-DAC0-4055-8471-5653A2CFA273}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business Layer", "Business Layer", "{0504180C-C6C4-49D6-A4FC-99BAA0767F9D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UI", "UI", "{D576711A-2654-408D-AAD1-DCADAE5E9C2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Entities", "Entities\Entities.csproj", "{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FormHelper", "FormHelper\FormHelper.csproj", "{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AdminYonetimSistemi", "AdminYönetimSistemi\AdminYonetimSistemi.csproj", "{2D884432-DE60-4F4C-887E-0716CAD5C328}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Debug|x64.ActiveCfg = Debug|x64
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Debug|x64.Build.0 = Debug|x64
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Debug|x86.ActiveCfg = Debug|x86
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Debug|x86.Build.0 = Debug|x86
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Release|Any CPU.Build.0 = Release|Any CPU
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Release|x64.ActiveCfg = Release|x64
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Release|x64.Build.0 = Release|x64
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Release|x86.ActiveCfg = Release|x86
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}.Release|x86.Build.0 = Release|x86
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Debug|x64.ActiveCfg = Debug|x64
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Debug|x64.Build.0 = Debug|x64
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Debug|x86.ActiveCfg = Debug|x86
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Debug|x86.Build.0 = Debug|x86
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Release|x64.ActiveCfg = Release|x64
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Release|x64.Build.0 = Release|x64
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Release|x86.ActiveCfg = Release|x86
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}.Release|x86.Build.0 = Release|x86
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Debug|x64.ActiveCfg = Debug|x64
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Debug|x64.Build.0 = Debug|x64
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Debug|x86.ActiveCfg = Debug|x86
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Debug|x86.Build.0 = Debug|x86
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Release|x64.ActiveCfg = Release|x64
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Release|x64.Build.0 = Release|x64
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Release|x86.ActiveCfg = Release|x86
		{2D884432-DE60-4F4C-887E-0716CAD5C328}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90} = {0504180C-C6C4-49D6-A4FC-99BAA0767F9D}
		{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2} = {189B9E33-DAC0-4055-8471-5653A2CFA273}
		{2D884432-DE60-4F4C-887E-0716CAD5C328} = {D576711A-2654-408D-AAD1-DCADAE5E9C2B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {650B174F-453F-4600-BE36-462D0D13E889}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 4
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://code.digiturk.net:8080/defaultcollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = Entities\\Entities.csproj
		SccProjectTopLevelParentUniqueName1 = AdminYonetimSistemi.sln
		SccProjectName1 = Entities
		SccLocalPath1 = Entities
		SccProjectUniqueName2 = FormHelper\\FormHelper.csproj
		SccProjectTopLevelParentUniqueName2 = AdminYonetimSistemi.sln
		SccProjectName2 = FormHelper
		SccLocalPath2 = FormHelper
		SccProjectUniqueName3 = AdminYönetimSistemi\\AdminYonetimSistemi.csproj
		SccProjectTopLevelParentUniqueName3 = AdminYonetimSistemi.sln
		SccProjectName3 = AdminYönetimSistemi
		SccLocalPath3 = AdminYönetimSistemi
	EndGlobalSection
EndGlobal
