﻿using Entities.Muhaberat;
using FormHelper.KurumsalKirtasiye;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.Muhaberat
{
    public class ItemTypeHelper
    {
        public static DataTable GridListele()
        {
            string SQL = @"select 
X.ID,X.ITEM_TYPE,
case X.OFFICIAL when '1' then 'EVET' else 'HAYIR' end as OFFICIAL,
case X.INOFFICIAL when '1' then 'EVET' else 'HAYIR' end as INOFFICIAL,
case X.IS_ACTIVE when '1' then 'AKTİF' else 'PASİF' end as ACTIVE
from MHBRT_REGISTRY_ITEM_TYPE X order by X.IS_ACTIVE desc,X.OFFICIAL desc,X.ITEM_TYPE";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
        }

        public static bool KayitEklenebilirmi(MHBRT_REGISTRY_ITEM_TYPE entity, ref string uyariMesaji)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<MHBRT_REGISTRY_ITEM_TYPE>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("ITEM_TYPE", true) }, true))
            {
                uyariMesaji = "Bu kayıt daha önce eklenmiştir.Tekrar eklenemez.";
                return false;
            }
            if(entity.INOFFICIAL=="0" && entity.OFFICIAL=="0")
            {
                uyariMesaji = "Resmi/Gayriresmi en az biri seçilmelidir.";
                return false;
            }
            return true;
        }

        public static bool KayitGuncellenebilirmi(MHBRT_REGISTRY_ITEM_TYPE entity, ref string uyariMesaji)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<MHBRT_REGISTRY_ITEM_TYPE>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("ITEM_TYPE", true) }, false))
            {
                uyariMesaji = "Benzer kayıt olduğu için güncelleme yapılamaz.";
                return false;
            }
            if (entity.INOFFICIAL == "0" && entity.OFFICIAL == "0")
            {
                uyariMesaji = "Resmi/Gayriresmi en az biri seçilmelidir.";
                return false;
            }
            return true;
        }
    }
}
