﻿using Entity_Base;
using System;
using System.Collections.Generic;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.KurumsalKirtasiye
{
    public static class KirtasiyeHelper
    {
        public static bool MukerrerKayitVarmi<T>(EntityBase entity, KontrolEdilecekSutun[] kontrolEdilecekSutunlar, bool yeniKayit) where T : EntityBase
        {
            string tabloAdi = TabloAdiGetir(typeof(T));
            Type entityType = entity.GetType();
            List<PropertyInfo> props = new List<PropertyInfo>(entityType.GetProperties()).Where(x => kontrolEdilecekSutunlar.Select(y => y.SutunAdi).Contains(x.Name)).ToList();
            string Andsutunlari = string.Join(" AND ", props.Where(x => kontrolEdilecekSutunlar.Where(y => y.VeIleBagla).Select(z => z.SutunAdi).Contains(x.Name)).Select(x => $"upper({x.Name})=upper(:{x.Name})").ToArray());
            string Orsutunlari = string.Join(" OR ", props.Where(x => kontrolEdilecekSutunlar.Where(y => !y.VeIleBagla).Select(z => z.SutunAdi).Contains(x.Name)).Select(x => $"upper({x.Name})=upper(:{x.Name})").ToArray());
            string sutunlar = Andsutunlari;
            if (Orsutunlari != string.Empty)
            {
                if (Andsutunlari != "")
                    sutunlar = Andsutunlari + " AND (" + Orsutunlari + ")";
                else
                    sutunlar = Orsutunlari;
            }
            string SQL = $"select case when exists(SELECT ID FROM DT_WORKFLOW.{tabloAdi}  WHERE {sutunlar} AND NVL(:ID,0)<>ID) then 'true' else 'false' end as KayitVar from DUAL";
            bool sonuc = false;
            List<OracleParameter> customList = props.Select(x => new OracleParameter(x.Name, (object)x.GetValue(entity, null) ?? DBNull.Value)).ToList();
            customList.Add(new OracleParameter("ID", yeniKayit ? DBNull.Value : (object)entity.GetSet_ID));
            sonuc = DataAccessLayer.DAL.ExecuteScalar_Oracle("DT_WORKFLOW", SQL, customList.ToArray()) == "true";
            return sonuc;
        }

        private static string TabloAdiGetir(Type tip)
        {
            return tip.Name;
        }
    }

    public class KontrolEdilecekSutun
    {
        public KontrolEdilecekSutun(string sutunAdi, bool veIleBagla)
        {
            this.SutunAdi = sutunAdi;
            this.VeIleBagla = veIleBagla;
        }
        public string SutunAdi { get; set; }
        public bool VeIleBagla { get; set; }
    }
}
