﻿using CoreHelpers;
using Entities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace FormHelper.KurumsalKirtasiye
{
    public class StokDetayHelper
    {
        private int sene = 0;
        public int SonMalzemeSenesi
        {
            get
            {
                if (sene == 0)
                {
                    sene = SonMalzemeSenesiGetir();
                    return sene;
                }
                else
                    return sene;
            }
            set
            {
                sene = value;
            }
        }
        private static int SonMalzemeSenesiGetir()
        {
            string SQL = "select  max(X.SENE) as SENE from KRT_STOK_DETAY X where X.AKTIF=1";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            return dt.Rows.Count > 0 ? ConvertionHelper.ConvertValue<int>(dt.Rows[0]["SENE"].ToString()) : DateTime.Today.Year;
        }

        public static void StokSeneDoldur(System.Web.UI.WebControls.DropDownList drp_Sene)
        {
            drp_Sene.Items.Clear();
            for (int i = SonMalzemeSenesiGetir(); i >= DateTime.Today.Year - 10; i--)
            {
                drp_Sene.Items.Add(new ListItem(i.ToString(), i.ToString()));
            }
        }

        public static DataTable GridListele(int sene, int stokGrupID)
        {
            string SQL = "select SD.ID,SG.STOK_GRUP_ADI,SD.STOK_KODU,SD.SENE,SD.STOK_ADI,SD.BIRIM_FIYAT,SD.PARA_BIRIMI,decode(SD.AKTIF,1,'Aktif',0,'Pasif') As AKTIF_DURUM from KRT_STOK_DETAY SD inner join KRT_STOK_GRUP SG  on SG.ID=SD.STOK_GRUP_ID where  SG.AKTIF=1 and SD.SENE=:SENE and SD.STOK_GRUP_ID=NVL(:STOKGRUPID,SD.STOK_GRUP_ID) order by SD.AKTIF desc,SG.STOK_GRUP_ADI,SD.STOK_ADI";
            OracleParameter[] parameters = new OracleParameter[] {
             new OracleParameter("SENE",sene),
             new OracleParameter("STOKGRUPID",stokGrupID!=0?(object)stokGrupID:DBNull.Value),
            };
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, parameters);
        }

        public static bool KayitEklenebilirmi(KRT_STOK_DETAY entity, ref string uyariMesaj)
        {
            if (entity.SENE >= new StokDetayHelper().SonMalzemeSenesi)
            {
                if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_STOK_DETAY>(entity, new KontrolEdilecekSutun[] {
                new KontrolEdilecekSutun("SENE", true),
                new KontrolEdilecekSutun("STOK_GRUP_ID", true),
                new KontrolEdilecekSutun("STOK_KODU", false),
                new KontrolEdilecekSutun("STOK_ADI", false)
            }, true))
                {
                    uyariMesaj = "Seçili sene için bu stok kodu ve/veya stok adı daha önce eklenmiştir.Tekrar eklenemez.";
                    return false;
                }
            }
            else
            {
                uyariMesaj = "Eski senelere ait giriş yapılamaz.";
                return false;
            }
            return true;
        }

        public static bool KayitGuncellenebilirmi(KRT_STOK_DETAY entity, string eskiStokKodu,string eskiStokAdi, ref string uyariMesaj)
        {
            if (entity.SENE >= new StokDetayHelper().SonMalzemeSenesi)
            {
                if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_STOK_DETAY>(entity, new KontrolEdilecekSutun[] {
                new KontrolEdilecekSutun("SENE", true),
                new KontrolEdilecekSutun("STOK_GRUP_ID", true),
                new KontrolEdilecekSutun("STOK_KODU", false),
                new KontrolEdilecekSutun("STOK_ADI", false)
            }, false))
                {
                    uyariMesaj = "Seçili senede benzer stok kodu ve/veya stok adı olduğu için güncelleme yapılamaz.";
                    return false;
                }
            }
            else
            {
                uyariMesaj = "Eski senelere ait güncelleme yapılamaz.";
                return false;
            }
            if ( (entity.STOK_KODU!= eskiStokKodu || entity.STOK_ADI!= eskiStokAdi)  && BuSenedeMalzemeStokCikisiVarmi(entity.GetSet_ID,entity.SENE))
            {
                //Eğer belirtilen senede bir stok çıkışı varsa ve stok kodu veya stok adı değiştiriliyorsa değiştirilemez.
                uyariMesaj = "Seçili senede bu malzemeye ait stok çıkışı olduğu için stok kodu ve stok adı güncelleme yapılamaz.";
                return false;
            }
            return true;
        }

        private static bool BuSenedeMalzemeStokCikisiVarmi(int stokDetayID, decimal sene)
        {
            string SQL = "select ID  from KRT_STOK_DAGITIM X where X.STOK_DETAY_ID=:STOK_DETAY_ID and TO_CHAR(X.CREATED, 'YYYY') =:SENE and X.AKTIF=1";
            OracleParameter[] parameters = new OracleParameter[] {
             new OracleParameter("SENE",sene),
             new OracleParameter("STOK_DETAY_ID",stokDetayID),
            };
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, parameters).Rows.Count>0;
        }

        public static bool KayitSilinebilirmi(int ID, ref string uyariMesaji)
        {
            return true;
            //Şimdilik Bir Kural Yok
        }

        public static void ParaBirimiDoldur(DropDownList drp)
        {
            drp.Items.Clear();
            drp.Items.AddRange(ConfigurationManager.AppSettings["ParaBirimi"].Split(',').Select(x => new ListItem(x, x)).ToArray());
        }
    }
}
