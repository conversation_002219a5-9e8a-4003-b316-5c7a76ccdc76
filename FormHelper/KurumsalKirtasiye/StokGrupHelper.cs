﻿using CoreHelpers;
using Entities;
using Entity_Base;
using System;
using System.Collections.Generic;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace FormHelper.KurumsalKirtasiye
{
    public static class StokGrupHelper
    {
        public static DataTable GridListele()
        {
            string SQL = "select S.ID,S.STOK_GRUP_ADI,S.TALEP_FORMUNDA_GOZUKUR As TALEP_FORMU,OZEL_GRUP,decode(S.AKTIF,1,'Aktif',0,'Pasif') As AKTIF_DURUM from KRT_STOK_GRUP S  order by S.AKTIF desc,S.STOK_GRUP_ADI";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            dt.Columns["TALEP_FORMU"].ReadOnly = false;
            dt.Columns["OZEL_GRUP"].ReadOnly = false;
            dt.Columns["TALEP_FORMU"].MaxLength = Int32.MaxValue;
            dt.Columns["OZEL_GRUP"].MaxLength = Int32.MaxValue;
            dt.Rows.Cast<DataRow>().ToList().ForEach(x =>
            {
                x["TALEP_FORMU"] = x["TALEP_FORMU"].ToString() == "1" ? "Evet" : "Hayır";
                x["OZEL_GRUP"] = x["OZEL_GRUP"].ToString() == "1" ? "Evet" : "Hayır";
            });
            return dt;
        }

        public static bool KayitEklenebilirmi(KRT_STOK_GRUP entity, ref string uyariMesaj)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_STOK_GRUP>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("STOK_GRUP_ADI", true) }, true))
            {
                uyariMesaj = "Bu kayıt daha önce eklenmiştir.Tekrar eklenemez.";
                return false;
            }
            return true;
        }

        public static bool KayitGuncellenebilirmi(KRT_STOK_GRUP entity, ref string uyariMesaj)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_STOK_GRUP>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("STOK_GRUP_ADI", true) }, false))
            {
                uyariMesaj = "Benzer kayıt olduğu için güncelleme yapılamaz.";
                return false;
            }
            return true;
        }
        public static bool KayitSilinebilirmi(int kayitID, ref string uyariMesaji)
        {
            return true;
            //Şimdilik Bir Kural Yok
        }

        public static string StokGrupGetirSQL()
        {
            return @"select R.ID,R.STOK_GRUP_ADI from (select 0 as ID,'Tümü' as STOK_GRUP_ADI from Dual
union
select SG.ID,SG.STOK_GRUP_ADI from KRT_STOK_GRUP SG where SG.AKTIF=1) R order by decode(R.ID,0,0,1),R.STOK_GRUP_ADI";
        }

        public static void StokGrubuDoldur(DropDownList drpStokGrubu)
        {
            GenericIslemler.DropDoldur("DT_WORKFLOW", StokGrupGetirSQL(), "STOK_GRUP_ADI", "ID", drpStokGrubu, false);
        }
    }
}
