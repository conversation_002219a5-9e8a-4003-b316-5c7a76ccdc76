﻿using CoreHelpers;
using Entities;
using Entity_Base;
using System;
using System.Collections.Generic;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace FormHelper.KurumsalKirtasiye
{
    public static class DepoHelper
    {
        public static DataTable GridListele()
        {
            string SQL = "select K.ID,K.DEPO_ADI,decode(K.AKTIF,1,'Aktif',0,'Pasif') As AKTIF_DURUM from KRT_DEPO K order by K.AKTIF desc,K.DEPO_ADI";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
        }

        public static bool KayitEklenebilirmi(KRT_DEPO entity, ref string uyariMesaj)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_DEPO>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("DEPO_ADI", true) }, true))
            {
                uyariMesaj = "Bu kayıt daha önce eklenmiştir.Tekrar eklenemez.";
                return false;
            }
            return true;
        }

        public static bool KayitGuncellenebilirmi(KRT_DEPO entity, ref string uyariMesaj)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_DEPO>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("DEPO_ADI", true) }, false))
            {
                uyariMesaj = "Benzer kayıt olduğu için güncelleme yapılamaz.";
                return false;
            }
            return true;
        }
        public static bool KayitSilinebilirmi(int kayitID, ref string uyariMesaji)
        {
            return true;
            //Şimdilik Bir Kural Yok
        }
    }
}
