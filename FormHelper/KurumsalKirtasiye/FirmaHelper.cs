﻿using CoreHelpers;
using Entities;
using Entity_Base;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace FormHelper.KurumsalKirtasiye
{
    public static class FirmaHelper
    {
        public static DataTable GridListele()
        {
            string SQL = "select F.ID,F.FIRMA_ADI,decode(F.AKTIF,1,'Aktif',0,'Pasif') As AKTIF_DURUM from KRT_FIRMA F order by F.AKTIF desc,F.FIRMA_ADI";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
        }

        public static bool KayitEklenebilirmi(KRT_FIRMA entity, ref string uyariMesaj)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_FIRMA>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("FIRMA_ADI", true) }, true))
            {
                uyariMesaj = "Bu kayıt daha önce eklenmiştir.Tekrar eklenemez.";
                return false;
            }
            return true;
        }

        public static bool KayitGuncellenebilirmi(KRT_FIRMA entity, ref string uyariMesaj)
        {
            if (KirtasiyeHelper.MukerrerKayitVarmi<KRT_FIRMA>(entity, new KontrolEdilecekSutun[] { new KontrolEdilecekSutun("FIRMA_ADI", true) }, false))
            {
                uyariMesaj = "Benzer kayıt olduğu için güncelleme yapılamaz.";
                return false;
            }
            return true;
        }
        public static bool KayitSilinebilirmi(int kayitID, ref string uyariMesaji)
        {
            return true;
            //Şimdilik Bir Kural Yok
        }
    }
}
