﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.TestMate
{
    public class ProjeKullaniciKayitHelper
    {
        public static DataTable GetDomainsList()
        {
            List<string> kullaniciGruplari = new YetkiHelper.YetkiHelper().gruplar();
            bool hepsi = kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechCorp"]);
            List<string> listIzinliDomain = new List<string>();
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]);
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA"])|| kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA2"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_TechQA"]);
            string sql = string.Empty;
            if (hepsi)
                sql = "SELECT 0 AS ID,'--Seçiniz--' as DOMAIN_NAME FROM DUAL UNION SELECT ID,DOMAIN_NAME FROM TMATE_DOMAINS_ADMINS WHERE AKTIF=1 ORDER BY ID ASC";
            else
                sql = "SELECT 0 AS ID,'--Seçiniz--' as DOMAIN_NAME FROM DUAL UNION SELECT ID,DOMAIN_NAME FROM TMATE_DOMAINS_ADMINS WHERE AKTIF=1 and DOMAIN_NAME in ('" + string.Join("','", listIzinliDomain) + "')  ORDER BY ID ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable GetProjectsList(int domainId)
        {
            string sql = "SELECT 0 AS ID,'--Seçiniz--' as PROJECT_NAME FROM DUAL UNION SELECT ID,PROJECT_NAME FROM TMATE_PROJECTS WHERE DOMAIN_ID=:DOMAIN_ID AND AKTIF=1 ORDER BY ID ASC";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("DOMAIN_ID", domainId) };
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
        }
        public static DataTable GetProjectsListAll()
        {
            //string sql = "SELECT 0 AS ID,'--Seçiniz--' as PROJECT_NAME FROM DUAL UNION SELECT ID,PROJECT_NAME FROM TMATE_PROJECTS WHERE AKTIF=1 ORDER BY ID ASC";
            string sql = "SELECT 0 AS ID,'--Seçiniz--' as PROJECT_NAME FROM DUAL ORDER BY ID ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable grdListe()
        {
            List<string> kullaniciGruplari = new YetkiHelper.YetkiHelper().gruplar();
            bool hepsi = kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechCorp"]);
            List<string> listIzinliDomain = new List<string>();
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]);
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA"])|| kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA2"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_TechQA"]);
            string sql = string.Empty;
            if(hepsi)
            sql = @"SELECT USR.ID,
                 DOM.DOMAIN_NAME,
                 PRJ.PROJECT_NAME,
                 USR.AD_GROUP_TYPE,
                 USR.AD_GROUP,
                 CASE WHEN USR.YAZMA_YETKISI='1' THEN 'VAR' ELSE 'YOK' END YAZMA_YETKISI,
                 CASE WHEN USR.RAPORLAMA_YETKISI='1' THEN 'VAR' ELSE 'YOK' END RAPORLAMA_YETKISI                  
                 FROM TMATE_PROJECT_USERS USR
                 INNER JOIN TMATE_PROJECTS PRJ ON USR.PROJECT_ID = PRJ.ID
                 INNER JOIN TMATE_DOMAINS_ADMINS DOM ON PRJ.DOMAIN_ID = DOM.ID
                 ORDER BY DOM.ID, PRJ.ID, USR.AD_GROUP ASC  ";
            else
                sql = @"SELECT USR.ID,
                 DOM.DOMAIN_NAME,
                 PRJ.PROJECT_NAME,
                 USR.AD_GROUP_TYPE,
                 USR.AD_GROUP,
                 CASE WHEN USR.YAZMA_YETKISI='1' THEN 'VAR' ELSE 'YOK' END YAZMA_YETKISI,
                 CASE WHEN USR.RAPORLAMA_YETKISI='1' THEN 'VAR' ELSE 'YOK' END RAPORLAMA_YETKISI                  
                 FROM TMATE_PROJECT_USERS USR
                 INNER JOIN TMATE_PROJECTS PRJ ON USR.PROJECT_ID = PRJ.ID
                 INNER JOIN TMATE_DOMAINS_ADMINS DOM ON PRJ.DOMAIN_ID = DOM.ID
                where DOM.DOMAIN_NAME in ('" + string.Join("','", listIzinliDomain) + @"')
                 ORDER BY DOM.ID, PRJ.ID, USR.AD_GROUP ASC  ";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static bool mukerrerKayitVarMi(string domainId, string projectId, string adGroupType, string adGroup, int ID)
        {
            bool sonuc = false;
            string sql = @"SELECT USR.ID,
             DOM.DOMAIN_NAME,
             PRJ.PROJECT_NAME,
             USR.AD_GROUP_TYPE,
             USR.AD_GROUP,
             CASE WHEN USR.YAZMA_YETKISI = '1' THEN 'VAR' ELSE 'YOK' END
             YAZMA_YETKISI,
             CASE WHEN USR.RAPORLAMA_YETKISI = '1' THEN 'VAR' ELSE 'YOK' END
             RAPORLAMA_YETKISI
             FROM TMATE_PROJECT_USERS USR
             INNER JOIN TMATE_PROJECTS PRJ ON USR.PROJECT_ID = PRJ.ID
             INNER JOIN TMATE_DOMAINS_ADMINS DOM ON PRJ.DOMAIN_ID = DOM.ID
             WHERE DOM.ID =:DOMAIN_ID
             AND PRJ.ID =:PROJECT_ID
             AND USR.AD_GROUP_TYPE =:AD_GROUP_TYPE
             AND USR.AD_GROUP = :AD_GROUP
             AND (:ID=0 OR USR.ID<>:ID)
             ORDER BY DOM.ID, PRJ.ID, USR.AD_GROUP ASC";


            OracleParameter[] dbParameter = new OracleParameter[]
            {
                new OracleParameter("DOMAIN_ID",domainId),
                new OracleParameter("PROJECT_ID",projectId),
                new OracleParameter("AD_GROUP_TYPE",adGroupType),
                new OracleParameter("AD_GROUP",adGroup),
                new OracleParameter("ID",ID)
            };

            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParameter).Rows.Count > 0 ? true : false;
            return sonuc;
        }


    }
}
