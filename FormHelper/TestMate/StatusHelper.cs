﻿using System;
using System.Collections.Generic;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.TestMate
{
    public class StatusHelper
    {
        public static DataTable GetStatusTypes()
        {
            string sql = "SELECT 0 AS ID, '--Seçiniz--' as STATUS_TYPE_NAME FROM DUAL UNION SELECT ID,to_char(STATUS_TYPE_NAME) FROM TMATE_STATUS_TYPE WHERE AKTIF=1";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable GetDomainsList()
        {
            string sql = "SELECT 0 AS ID, '--Seç<PERSON>z--' as DOMAIN_NAME FROM DUAL UNION SELECT ID,DOMAIN_NAME FROM TMATE_DOMAINS_ADMINS WHERE AKTIF = 1 ORDER BY ID ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable GetProjectsList(string domainId)
        {
            string sql = "SELECT 0 AS ID, '--Seç<PERSON>z--' as PROJECT_NAME FROM DUAL UNION SELECT ID,PROJECT_NAME  FROM TMATE_PROJECTS WHERE AKTIF=1 AND DOMAIN_ID=:DOMAIN_ID ORDER BY ID ASC";
            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("DOMAIN_ID",domainId)
            };
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
        }
        public static DataTable GetProjectsListAll()
        {
            string sql = "SELECT 0 AS ID, 0 AS DOMAIN_ID, '--Seçiniz--' as PROJECT_NAME FROM DUAL UNION SELECT ID,DOMAIN_ID,PROJECT_NAME  FROM TMATE_PROJECTS WHERE AKTIF=1 ORDER BY DOMAIN_ID,ID";            
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable GetGridInfos()
        {
            string sql = "SELECT STS.*,PRJ.PROJECT_NAME,PRJ.DOMAIN_ID,TYPE.STATUS_TYPE_NAME  FROM TMATE_STATUS STS JOIN TMATE_STATUS_TYPE TYPE ON STS.STATUS_TYPE=TYPE.ID JOIN TMATE_PROJECTS PRJ ON STS.PROJECT_ID=PRJ.ID";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static bool mukerrerKayitVarMi(string projectId,string statusType, string statusNameTr, string statusNameEn, int ID)
        {
            bool sonuc = false;
            string sql = "SELECT * FROM TMATE_STATUS WHERE PROJECT_ID=:PROJECT_ID AND STATUS_TYPE=:STATUS_TYPE AND (UPPER(STATUS_NAME_TR)=UPPER(:STATUS_NAME_TR) OR UPPER(STATUS_NAME_TR)=UPPER(:STATUS_NAME_EN)) AND (UPPER(STATUS_NAME_EN)=UPPER(:STATUS_NAME_EN) OR UPPER(STATUS_NAME_EN)=UPPER(:STATUS_NAME_TR)) AND (:ID=0 OR ID<>:ID)";

            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("PROJECT_ID",projectId),
                new OracleParameter("STATUS_TYPE",statusType),
                new OracleParameter("STATUS_NAME_TR",statusNameTr),
                new OracleParameter("STATUS_NAME_EN",statusNameEn),
                new OracleParameter("ID",ID)
            };
            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams).Rows.Count > 0 ? true : false;
            return sonuc;
        }


    }
}
