﻿using CoreHelpers;
using Entities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.TestMate
{
    public class ProjeKayitHelper
    {
        public static DataTable GetDomainsList()
        {
            List<string> kullaniciGruplari = new YetkiHelper.YetkiHelper().gruplar();
            bool hepsi = kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechCorp"]);
            List<string> listIzinliDomain = new List<string>();
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]);
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA"])|| kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA2"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_TechQA"]);
            string sql = string.Empty;
            if (hepsi)
                sql = "SELECT 0 AS ID,'--Seçiniz--' as DOMAIN_NAME FROM DUAL UNION SELECT ID,DOMAIN_NAME FROM TMATE_DOMAINS_ADMINS WHERE AKTIF=1 ORDER BY ID ASC";
            else
                sql = "SELECT 0 AS ID,'--Seçiniz--' as DOMAIN_NAME FROM DUAL UNION SELECT ID,DOMAIN_NAME FROM TMATE_DOMAINS_ADMINS WHERE AKTIF=1 and DOMAIN_NAME in ('" + string.Join("','", listIzinliDomain) + "') ORDER BY ID ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable grdListe()
        {
            List<string> kullaniciGruplari = new YetkiHelper.YetkiHelper().gruplar();
            bool hepsi = kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechCorp"]);
            List<string> listIzinliDomain = new List<string>();
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_PDOSTB"]);
            if (kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA"])|| kullaniciGruplari.Select(x => x.ToUpper()).Contains(ConfigurationManager.AppSettings["AdGroup_TechQA2"]))
                listIzinliDomain.Add(ConfigurationManager.AppSettings["AdGroup_TechQA"]);
            string sql = string.Empty;
            if (hepsi)
                sql = @"SELECT PRJ.ID,DOM.DOMAIN_NAME, PRJ.PROJECT_NAME, CASE PRJ.AKTIF WHEN '1' THEN 'AKTIF' WHEN '0' THEN 'PASIF' END AKTIF
                FROM TMATE_PROJECTS PRJ
                INNER JOIN TMATE_DOMAINS_ADMINS DOM ON PRJ.DOMAIN_ID = DOM.ID
                ORDER BY DOM.DOMAIN_NAME, PRJ.PROJECT_NAME ASC";
            else
                sql = @"SELECT PRJ.ID,DOM.DOMAIN_NAME, PRJ.PROJECT_NAME, CASE PRJ.AKTIF WHEN '1' THEN 'AKTIF' WHEN '0' THEN 'PASIF' END AKTIF
                FROM TMATE_PROJECTS PRJ
                INNER JOIN TMATE_DOMAINS_ADMINS DOM ON PRJ.DOMAIN_ID = DOM.ID
                where DOM.DOMAIN_NAME in ('" + string.Join("','", listIzinliDomain) + @"')
                ORDER BY DOM.DOMAIN_NAME, PRJ.PROJECT_NAME ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static bool mukerrerKayitVarMi(string domainId, string proje, int ID)
        {
            bool sonuc = false;
            string sql = @"SELECT * FROM TMATE_PROJECTS WHERE DOMAIN_ID=:DOMAIN_ID AND UPPER(PROJECT_NAME)=UPPER(:PROJECT_NAME) AND (:ID=0 OR ID<>:ID)";
            OracleParameter[] dbParameter = new OracleParameter[]
            {
                new OracleParameter("PROJECT_NAME",proje),
                new OracleParameter("DOMAIN_ID",domainId),
                new OracleParameter("ID",ID)
            };
            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParameter).Rows.Count > 0 ? true : false;
            return sonuc;
        }

        public static void ProjeDurumlariKaydet(int projectId, long userId)
        {
            OracleConnection connection = null;
            OracleTransaction transaction = null;
            try
            {
                List<StatusModel> list = new List<StatusModel>();
                list.Add(new StatusModel((int)StatusTypes.TEST, SpecialStatusNames.Tasarım.ToString(), SpecialStatusNames.Design.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST, SpecialStatusNames.İçeri_Alınmış.ToString().Replace("_", " "), SpecialStatusNames.Imported.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST, SpecialStatusNames.Hazır.ToString(), SpecialStatusNames.Ready.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST, SpecialStatusNames.Onarım.ToString(), SpecialStatusNames.Repair.ToString()));

                list.Add(new StatusModel((int)StatusTypes.TEST_INSTANCE, SpecialStatusNames.Engellenmiş.ToString(), SpecialStatusNames.Blocked.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST_INSTANCE, SpecialStatusNames.Hatalı.ToString(), SpecialStatusNames.Failed.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST_INSTANCE, SpecialStatusNames.Koşulmamış.ToString(), SpecialStatusNames.No_Run.ToString().Replace("_", " ")));
                list.Add(new StatusModel((int)StatusTypes.TEST_INSTANCE, SpecialStatusNames.Tamamlanmamış.ToString(), SpecialStatusNames.Not_Completed.ToString().Replace("_", " ")));
                list.Add(new StatusModel((int)StatusTypes.TEST_INSTANCE, SpecialStatusNames.Geçti.ToString(), SpecialStatusNames.Passed.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST_INSTANCE, SpecialStatusNames.Uygun_Değil.ToString().Replace("_", " "), SpecialStatusNames.N_A.ToString().Replace("_", "/")));

                list.Add(new StatusModel((int)StatusTypes.TEST_STEP_RUN, SpecialStatusNames.Engellenmiş.ToString(), SpecialStatusNames.Blocked.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST_STEP_RUN, SpecialStatusNames.Hatalı.ToString(), SpecialStatusNames.Failed.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST_STEP_RUN, SpecialStatusNames.Koşulmamış.ToString(), SpecialStatusNames.No_Run.ToString().Replace("_", " ")));
                list.Add(new StatusModel((int)StatusTypes.TEST_STEP_RUN, SpecialStatusNames.Tamamlanmamış.ToString(), SpecialStatusNames.Not_Completed.ToString().Replace("_", " ")));
                list.Add(new StatusModel((int)StatusTypes.TEST_STEP_RUN, SpecialStatusNames.Geçti.ToString(), SpecialStatusNames.Passed.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST_STEP_RUN, SpecialStatusNames.Uygun_Değil.ToString().Replace("_", " "), SpecialStatusNames.N_A.ToString().Replace("_", "/")));

                list.Add(new StatusModel((int)StatusTypes.TEST_SET, SpecialStatusNames.Açık.ToString(), SpecialStatusNames.Open.ToString()));
                list.Add(new StatusModel((int)StatusTypes.TEST_SET, SpecialStatusNames.Kapalı.ToString(), SpecialStatusNames.Closed.ToString()));

                using (connection = DataAccessLayer.DAL.GetConnection_Oracle("DT_WORKFLOW", true))
                {
                    using (transaction = DataAccessLayer.DAL.GetTransaction_Oracle(connection))
                    {
                        bool changeExists = false;
                        foreach (var status in list)
                        {
                            TMATE_STATUS entity = PRepository<TMATE_STATUS>.EntityGetir("DT_WORKFLOW", "select * from TMATE_STATUS X where X.PROJECT_ID=:PROJECT_ID and X.STATUS_NAME_TR=:STATUS_NAME_TR and X.STATUS_NAME_EN=:STATUS_NAME_EN and X.AKTIF=1", new OracleParameter[] {
                             new OracleParameter("PROJECT_ID",projectId),
                             new OracleParameter("STATUS_NAME_TR",status.StatusName_Tr),
                             new OracleParameter("STATUS_NAME_EN",status.StatusName_En)
                            });
                            if (entity == null || entity.ID == 0)
                            {
                                TMATE_STATUS newEntity = new TMATE_STATUS()
                                {
                                    AKTIF = "1",
                                    CREATED = DateTime.Now,
                                    CREATED_BY = userId,
                                    STATUS_NAME_EN = status.StatusName_En,
                                    STATUS_NAME_TR = status.StatusName_Tr,
                                    STATUS_TYPE = status.StatusType,
                                    UPDATED = null,
                                    UPDATED_BY = null,
                                    PROJECT_ID = projectId
                                };
                                int insertedStatusId = ConvertionHelper.ConvertValue<int>(PRepository<TMATE_STATUS>.EntityKaydet(connection, transaction, newEntity));
                                if (insertedStatusId <= 0)
                                    throw new Exception();
                                changeExists = true;
                            }
                        }
                        if (changeExists)
                        {
                            DataAccessLayer.DAL.CommitTransaction_Oracle(transaction);
                            DataAccessLayer.DAL.CloseConnection(connection);
                        }
                    }
                }
            }
            catch
            {
                if (transaction != null)
                    DataAccessLayer.DAL.RollbackTransaction_Oracle(transaction);
                if (connection != null)
                    DataAccessLayer.DAL.CloseConnection(connection);
            }
        }
    }
    public class StatusModel
    {
        public StatusModel()
        {

        }
        public StatusModel(int statusType, string statusName_Tr, string statusName_En)
        {
            this.StatusName_En = statusName_En;
            this.StatusName_Tr = statusName_Tr;
            this.StatusType = statusType;
        }
        public int StatusType { get; set; }
        public string StatusName_Tr { get; set; }
        public string StatusName_En { get; set; }
    }
    public enum SpecialStatusNames
    {
        No_Run, Koşulmamış, Not_Completed, Tamamlanmamış, Blocked, Engellenmiş, Failed, Hatalı, Passed, Geçti, N_A, Uygun_Değil, Design, Tasarım, Imported, İçeri_Alınmış, Ready, Hazır, Repair, Onarım, Açık, Kapalı, Open, Closed
    }
    public enum StatusTypes
    {
        TEST = 1, TEST_INSTANCE = 2, TEST_STEP_RUN = 3, TEST_SET = 4
    }
}
