﻿using System;
using System.Collections.Generic;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.TestMate
{
    public class DomainKaydetHelper
    {
        public static DataTable GetKullaniciList()
        {
            string sql = "SELECT * FROM DP_HR_USERS ORDER BY NAME_SURNAME";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable grdListe()
        {
            string sql = @"SELECT ID,DOMAIN_NAME,AD_GROUP_TYPE,AD_GROUP_NAME,
                   CASE WHEN AKTIF = '1' THEN 'AKTIF' ELSE 'PASIF' END AKTIF
              FROM TMATE_DOMAINS_ADMINS
              ORDER BY  DOMAIN_NAME ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }
        public static bool mukerrerKayitVarMi(string domain, int ID)
        {
            bool sonuc = false;
            string sql = @"SELECT * FROM TMATE_DOMAINS_ADMINS WHERE UPPER(DOMAIN_NAME)=UPPER(:DOMAIN_NAME) AND (:ID=0 OR ID<>:ID)";
            OracleParameter[] dbParameter = new OracleParameter[]
            {
                new OracleParameter("DOMAIN_NAME",domain),
                new OracleParameter("ID",ID)
            };
            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParameter).Rows.Count > 0 ? true : false;
            return sonuc;
        }
        public static adportalServiceClient.GroupInformation[] GetAdGroupList(string Domain)
        {
            adportalServiceClient.AdPortalServices client = new adportalServiceClient.AdPortalServices();
            string Username = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.UserName"];
            string Password = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.Password"];
            client.Credentials = new System.Net.NetworkCredential(Username, Password);
            if (Domain == "DIGITURK")
            {
                client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalServices"];
            }
            else if (Domain == "DIGITURKCC")
            {
                client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalCCServices"];
            }
            adportalServiceClient.GroupInformation[] gruplist = client.GetGroupList();
            return gruplist;
        }

        


    }
}
