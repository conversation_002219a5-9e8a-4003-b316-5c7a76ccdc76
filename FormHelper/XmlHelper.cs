﻿using CoreHelpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace FormHelper
{
    public class XmlHelper
    {
        /// <summary>
        /// Yolu verilen XML dosyasını okuyarak XMLStream nesnesine yazar.
        /// </summary>
        /// <param name="fileName">Dosyanın Yolu</param>
        /// <returns>Dönen XML Stream Nesnesi</returns>
        public static System.Xml.XmlDocument GetDocument(string fileName)
        {
            try
            {
                XmlDocument doc = new XmlDocument();

                string tableName = "WF_XML_LOGICAL_GROUP";
                if (tableName != "")
                {
                    try
                    {
                        doc.LoadXml(GenerateXmlFromDatabase(fileName));
                    }
                    catch (System.Exception ex)
                    {
                        throw ex;
                    }
                }
                return doc;
            }
            catch (System.Exception ex)
            {
                throw ex;
            }
        }
        private static string GenerateXmlFromDatabase(string fileName)
        {
            // Determine the table name based on the fileName
            string tableName = "WF_XML_LOGICAL_GROUP";

            // Query the database to get a DataTable
            string cmd = $"Select * from DT_WORKFLOW.{tableName}";

            DataTable dataTable = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", cmd);

            if (dataTable == null)
            {
                throw new Exception("Failed to retrieve data from the database.");
            }

            // Convert the DataTable to XML and return the XML string
            return ConvertDataTableToXmlString(dataTable, fileName);
        }
        private static string ConvertDataTableToXmlString(DataTable dataTable, string formatIdentifier)
        {
            XmlDocument customXmlDoc = new XmlDocument();

            // Determine the root element based on the format identifier
            XmlElement root = customXmlDoc.CreateElement(
                formatIdentifier.EndsWith("StateDefinition.xml") ? "StateDefList" :
                formatIdentifier.EndsWith("Definiton.xml") || formatIdentifier.EndsWith("WorkFlow.xml") ? "WorkFlowList" :
                formatIdentifier.EndsWith("LogicalGroups.xml") ? "LogicalGroupList" : "");
            customXmlDoc.AppendChild(root);

            // Populate the root element with child elements based on the DataTable
            foreach (DataRow row in dataTable.Rows)
            {
                XmlElement childElement = customXmlDoc.CreateElement(root.Name.Replace("List", ""));
                foreach (DataColumn column in dataTable.Columns)
                {
                    childElement.SetAttribute(column.ColumnName, row[column].ToString());
                }
                root.AppendChild(childElement);
            }

            // Convert the XmlDocument to a string and return
            using (StringWriter stringWriter = new StringWriter())
            {
                customXmlDoc.Save(stringWriter);
                return stringWriter.ToString();
            }
        }



        /// <summary>
        /// XML Nodu verilen Fonksiyonun içerisinden attribute değerini okumak için kullanılır.
        /// </summary>
        /// <typeparam name="T">Dönüştürülen Tip</typeparam>
        /// <param name="attributeName">Attr</param>
        /// <param name="node"></param>
        /// <returns></returns>
        public static T getNodeValue<T>(string attributeName, XmlNode node)
        {
            if (node.Attributes[attributeName] == null)
                throw new Exception("XML İçeriğinde Node Bulunmamaktadır");
            return ConvertionHelper.ConvertValue<T>(node.Attributes[attributeName].Value);
        }

        /// <summary>
        /// XML Nodu verilen Fonksiyonun içerisinden attribute değerini okumak için kullanılır.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="attributeName"></param>
        /// <param name="node"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static T getNodeValue<T>(string attributeName, XmlNode node, T defaultValue)
        {
            if (node.Attributes[attributeName] == null)
                return defaultValue;
            return ConvertionHelper.ConvertValue<T>(node.Attributes[attributeName].Value);
        }

        /// <summary>
        /// XML Datayı DataTable a Convert eder
        /// </summary>
        /// <param name="FileName"></param>
        /// <param name="Table"></param>
        public static void XMLConverter(string FileName, DataTable Table)
        {
            Table.WriteXml(FileName);
        }

        /// <summary>
        /// Object Nesneyi Seralize eder.
        /// </summary>
        /// <param name="objToXml"></param>
        /// <param name="includeNameSpace"></param>
        /// <returns></returns>
        public static string ToXml(object objToXml, bool includeNameSpace)
        {
            if (objToXml == null)
            {
                return "";
            }
            StreamWriter stWriter = null;
            XmlSerializer xmlserializer;
            string buffer;
            try
            {
                xmlserializer = new XmlSerializer(objToXml.GetType());
                MemoryStream memStream = new MemoryStream();
                stWriter = new StreamWriter(memStream);
                if (!includeNameSpace)
                {
                    System.Xml.Serialization.XmlSerializerNamespaces xs = new XmlSerializerNamespaces();
                    xs.Add("", "");
                    xmlserializer.Serialize(stWriter, objToXml, xs);
                }
                else
                {
                    xmlserializer.Serialize(stWriter, objToXml);
                }
                buffer = Encoding.GetEncoding("iso-8859-9").GetString(memStream.GetBuffer());
            }
            catch (Exception Ex)
            {
                throw Ex;
            }
            finally
            {
                if (stWriter != null)
                    stWriter.Close();
            }
            return buffer;
        }

        /// <summary>
        /// XML Datayı DeSerialize eder.
        /// </summary>
        /// <param name="xmlString"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static object XmlTo(string xmlString, Type type)
        {
            if (xmlString == "")
            {
                return null;
            }
            XmlSerializer xmlserializer;
            MemoryStream memStream = null;
            try
            {
                xmlserializer = new XmlSerializer(type);
                byte[] bytes = new byte[xmlString.Length];
                Encoding.GetEncoding("iso-8859-9").GetBytes(xmlString, 0, xmlString.Length, bytes, 0);
                memStream = new MemoryStream(bytes);
                object objectFromXml = xmlserializer.Deserialize(memStream);
                return objectFromXml;
            }
            catch (Exception Ex) { throw Ex; }
            finally { if (memStream != null) memStream.Close(); }
        }
    }
}
