﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{68EE2EFE-7BFD-4841-856B-A1A9AE4E91C2}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>FormHelper</RootNamespace>
    <AssemblyName>FormHelper</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AdPortalWeb">
      <HintPath>..\..\..\..\AdportalCC\AdPortalWeb2\bin\AdPortalWeb.dll</HintPath>
    </Reference>
    <Reference Include="CoreHelpers, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="DataAccessLayer, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Entity_Base, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Entity_Base.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\ODPNET\x86\Net4\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="Castle.Core, Version=3.1.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus">
      <HintPath>..\packages\EPPlus.6.0.8\lib\net35\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Outlook, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DigiportAdmin\AnasayfaDuyuruHelper.cs" />
    <Compile Include="DigiportAdmin\AnasayfaSolMenuHelper.cs" />
    <Compile Include="DigiportAdmin\AnasayfaSolSagSlideHelper.cs" />
    <Compile Include="DigiportAdmin\AnasayfaUstSlideHelper.cs" />
    <Compile Include="DigiportAdmin\DigiportAuthorizationService.cs" />
    <Compile Include="DigiportAdmin\DigiportMediaHelper.cs" />
    <Compile Include="DigiportAdmin\DigiportMediaHrAppHelper.cs" />
    <Compile Include="DigiportAdmin\Enums.cs" />
    <Compile Include="DigiportAdmin\HrMediaSlideHelper.cs" />
    <Compile Include="DigiportAdmin\IndirimFirsatiHelper.cs" />
    <Compile Include="DigiportAdmin\LinklerHelper.cs" />
    <Compile Include="DigiportAdmin\NameHelper.cs" />
    <Compile Include="DigiportAdmin\SliderKategoriHelper.cs" />
    <Compile Include="DigiportAdmin\SortHelper.cs" />
    <Compile Include="DigiportAdmin\TypeHelper.cs" />
    <Compile Include="DigiportAdmin\UserAssignmentHelper.cs" />
    <Compile Include="DigiportAnket\DIGIPORT_ANKET_DOMAIN_Helper.cs" />
    <Compile Include="DigiportAnket\DIGIPORT_ANKET_DOMAIN_USERS_Helper.cs" />
    <Compile Include="Muhaberat\ItemTypeHelper.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="TestMate\DomainKaydetHelper.cs" />
    <Compile Include="TestMate\ProjeKayitHelper.cs" />
    <Compile Include="TestMate\ProjeKullaniciKayitHelper.cs" />
    <Compile Include="AracTakipSistemi\AracMarkaHelper.cs" />
    <Compile Include="AracTakipSistemi\AracModelHelper.cs" />
    <Compile Include="AracTakipSistemi\AracPlakaHelper.cs" />
    <Compile Include="AracTakipSistemi\AracRenkHelper.cs" />
    <Compile Include="AracTakipSistemi\AracSınıfHelper.cs" />
    <Compile Include="AracTakipSistemi\AracYılHelper.cs" />
    <Compile Include="CoreHelper.cs" />
    <Compile Include="EkipmanTalepFormu\EkipmanTalepleriRaporuHelper.cs" />
    <Compile Include="EkipmanTalepFormu\ExcelYuklemeHelper.cs" />
    <Compile Include="EkipmanTalepFormu\MalzemeTanimHelper.cs" />
    <Compile Include="EkipmanTalepFormu\YtsTanimlamaHelper.cs" />
    <Compile Include="KurumsalKirtasiye\DepoHelper.cs" />
    <Compile Include="KurumsalKirtasiye\FirmaHelper.cs" />
    <Compile Include="KurumsalKirtasiye\KirtasiyeHelper.cs" />
    <Compile Include="KurumsalKirtasiye\StokDetayHelper.cs" />
    <Compile Include="KurumsalKirtasiye\StokGrupHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TestMate\StatusHelper.cs" />
    <Compile Include="Web References\adportalServiceClient\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="XmlHelper.cs" />
    <Compile Include="YetkiHelper\AuthenticationManager.cs" />
    <Compile Include="YetkiHelper\AuthenticationResult.cs" />
    <Compile Include="YetkiHelper\YetkiHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://adportal/AdPortalServices.asmx%3fWSDL">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\adportalServiceClient\</RelPath>
      <UpdateFromURL>http://adportal/AdPortalServices.asmx%3fWSDL</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>FormHelper_adportalServiceClient_AdPortalServices</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="Web References\adportalServiceClient\AdPortalServices.wsdl" />
    <None Include="Web References\adportalServiceClient\GroupInformation.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\adportalServiceClient\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\adportalServiceClient\UserInformation.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Entities\Entities.csproj">
      <Project>{abc3dda4-168e-4378-84bb-7ca3b8c56d90}</Project>
      <Name>Entities</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>