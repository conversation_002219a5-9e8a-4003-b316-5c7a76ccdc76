﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.DataAccess.Client;

namespace FormHelper
{
    public class AracSınıfHelper
    {
        public static DataTable GridListele()
        {            
            string SQL = @"SELECT DT_WORKFLOW.ATS_ARAC_SINIFI.*, CASE WHEN AKTIF=1 THEN 'Aktif' ELSE 'Pasif' END AS AKTIF_DURUM FROM DT_WORKFLOW.ATS_ARAC_SINIFI order by ARAC_SINIFI";
            DataTable Dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            return Dtbsonuc;            
        }

        public static string AracSınıfGetirSql()
        {
            return @"select ID,ARAC_SINIFI from DT_WORKFLOW.ATS_ARAC_SINIFI ORDER BY ARAC_SINIFI";
        }

        public static bool ValidateAracSinif(string AracSinifAdi, int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_SINIFI WHERE ARAC_SINIFI=:ARAC_SINIFI_PARAM AND ID<>:ID_PARAM ";     
            
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { 
                new OracleParameter("ARAC_SINIFI_PARAM",AracSinifAdi),
                new OracleParameter("ID_PARAM",ID)
            };

            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }

        public static bool ValidateDeletingControl(int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_PLAKASI PLK JOIN DT_WORKFLOW.ATS_ARAC_SINIFI SNF ON SNF.ID=PLK.ARAC_SINIFI_ID 
            WHERE SNF.ID=:ID_PARAM AND PLK.ARAC_SINIFI_ID=:ID_PARAM";
            
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ID_PARAM", ID) };            
            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }
    }
}
