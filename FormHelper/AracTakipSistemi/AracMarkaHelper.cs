﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.DataAccess.Client;

namespace FormHelper
{
    public class AracMarkaHelper
    {
        public static DataTable GridListele()
        {
            string SQL = "SELECT DT_WORKFLOW.ATS_ARAC_MARKASI.*, CASE WHEN AKTIF=1 THEN 'Aktif' ELSE 'Pasif' END AS AKTIF_DURUM FROM DT_WORKFLOW.ATS_ARAC_MARKASI ORDER BY ARAC_MARKASI";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);        
        }

        public static string AracMarkaGetirSql()
        {
            return @"select ID,ARAC_MARKASI from DT_WORKFLOW.ATS_ARAC_MARKASI ORDER BY ARAC_MARKASI";
        }

        public static bool ValidateAracMarka(string AracMarkaAdi, int ID)
        {
            string SQL = "SELECT * FROM DT_WORKFLOW.ATS_ARAC_MARKASI WHERE ARAC_MARKASI=:ARAC_MARKASI_PARAM AND ID<>:ID_PARAM ";

            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] {
                new OracleParameter("ARAC_MARKASI_PARAM", AracMarkaAdi),
                new OracleParameter("ID_PARAM", ID)
            };
            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }
        public static bool ValidateDeletingControl(int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_PLAKASI PLK JOIN DT_WORKFLOW.ATS_ARAC_MARKASI MRK ON MRK.ID=PLK.ARAC_MARKASI_ID JOIN DT_WORKFLOW.ATS_ARAC_MODELI MDL ON MRK.ID=MDL.ARAC_MARKA_ID  
            WHERE MRK.ID=:ID_PARAM AND PLK.ARAC_MARKASI_ID=:ID_PARAM AND MDL.ARAC_MARKA_ID=:ID_PARAM";
            
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ID_PARAM", ID) };
            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }


        public static bool ValidationDeletingControl_Marka(int ID)
        {
            string SQL = @"SELECT MRK.ID,MDL.ARAC_MARKA_ID FROM DT_WORKFLOW.ATS_ARAC_MARKASI MRK JOIN DT_WORKFLOW.ATS_ARAC_MODELI MDL ON MRK.ID = MDL.ARAC_MARKA_ID
            WHERE MRK.ID=:ID_PARAM";

            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] {new OracleParameter("ID_PARAM", ID)};
            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }
    }
}
