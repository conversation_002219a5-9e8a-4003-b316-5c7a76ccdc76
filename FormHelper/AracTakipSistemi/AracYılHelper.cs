﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.DataAccess.Client;

namespace FormHelper
{
    public class Arac<PERSON><PERSON><PERSON>Helper
    {
        public static DataTable GridListele()
        {
            string SQL = "SELECT DT_WORKFLOW.ATS_ARAC_YILI.*, CASE WHEN AKTIF=1 THEN 'Aktif' ELSE 'Pasif' END AKTIF_DURUM FROM DT_WORKFLOW.ATS_ARAC_YILI order by ARAC_YILI";
            DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            return dtbsonuc;            
        }

        public static string AracYılGetirSql()
        {
            return @"select ID,ARAC_YILI from DT_WORKFLOW.ATS_ARAC_YILI ORDER BY ARAC_YILI DESC";
        }

        public static bool ValidateAracYil(string AracYil, int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_YILI WHERE ARAC_YILI=:ARAC_YILI_PARAM AND ID<>:ID_PARAM ";
           
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ARAC_YILI_PARAM",AracYil),
            new OracleParameter("ID_PARAM",ID)};

            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL,customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }

        public static bool ValidateDeletingControl(int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_PLAKASI PLK JOIN DT_WORKFLOW.ATS_ARAC_YILI YIL ON YIL.ID=PLK.ARAC_YILI_ID 
        WHERE YIL.ID=:YIL_ID AND PLK.ARAC_YILI_ID=:YIL_ID";           

            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("YIL_ID", ID) };          
            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }
    }
}
