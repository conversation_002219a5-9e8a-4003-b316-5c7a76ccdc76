﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.DataAccess.Client;

namespace FormHelper
{
    public class AracRenkHelper
    {
        //Test
        public static DataTable GridListele()
        {
            string SQL = "Select DT_WORKFLOW.ATS_ARAC_RENGI.*,(CASE WHEN AKTIF = 1 THEN 'Aktif' ELSE 'Pasif' END) as AKTIF_DURUM from DT_WORKFLOW.ATS_ARAC_RENGI order by ARAC_RENGI";

            DataTable Dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            return Dtbsonuc;            
        }

        public static string AracRenkGetirSql()
        {
            return @"select ID,ARAC_RENGI from DT_WORKFLOW.ATS_ARAC_RENGI ORDER BY ARAC_RENGI";
        }

        public static bool ValidateAracRenk(string AracRenkAdi, int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_RENGI WHERE ARAC_RENGI=:ARAC_RENGI_PARAM AND ID<>:ID_PARAM ";
            
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ARAC_RENGI_PARAM", AracRenkAdi),
            new OracleParameter("ID_PARAM", ID)};

            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }

        public static bool ValidateDeletingControl(int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_PLAKASI PLK JOIN DT_WORKFLOW.ATS_ARAC_RENGI RNG ON RNG.ID=PLK.ARAC_RENGI_ID 
        WHERE RNG.ID=:ID_PARAM AND PLK.ARAC_RENGI_ID=:ID_PARAM";
            
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ID_PARAM", ID)};
            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }
    }
}
