﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.DataAccess.Client;
using DataAccessLayer;

namespace FormHelper
{
    public class AracModelHelper
    {
        public static DataTable GridListele()
        {
            string SQL = "SELECT MOD.ID, MAR.ARAC_MARKASI,MOD.ARAC_MODELI,MOD.AKTIF,MOD.KAYIT_TARIHI,MOD.DEGISTIRME_TARIHI,MOD.KAYDEDEN,MOD.DEGISTIREN, CASE WHEN MOD.AKTIF=1 THEN 'Aktif' ELSE 'Pasif' END AS AKTIF_DURUM FROM DT_WORKFLOW.ATS_ARAC_MODELI MOD JOIN DT_WORKFLOW.ATS_ARAC_MARKASI MAR ON MOD.ARAC_MARKA_ID=MAR.ID order by MAR.ARAC_MARKASI,MOD.ARAC_MODELI";
            DataTable Dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            return Dtbsonuc;
        }

        public static string Arac<PERSON>odelGetirSql()
        {
            return @"select ID,ARAC_MARKA_ID,ARAC_MODELI from DT_WORKFLOW.ATS_ARAC_MODELI";
        }

        public static bool ValidateAracModeli(string AracModelAdi, int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_MODELI WHERE ARAC_MODELI=:ARAC_MODELI_PARAM AND ID<>:ID_PARAM ";

            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ARAC_MODELI_PARAM", AracModelAdi),
            new OracleParameter("ID_PARAM", ID)};

            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }

        public static string AracModelGetirSqlPlk(string marka)
        {
            return string.Format("SELECT ID,ARAC_MODELI FROM DT_WORKFLOW.ATS_ARAC_MODELI WHERE ARAC_MARKA_ID = {0} ORDER BY ARAC_MODELI", marka);
        }

        public static bool ValidateDeletingControl(int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_PLAKASI PLK JOIN DT_WORKFLOW.ATS_ARAC_MODELI MDL ON MDL.ID=PLK.ARAC_MODELI_ID
        WHERE MDL.ID=:ID_PARAM AND PLK.ARAC_MODELI_ID=:ID_PARAM";
            
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ID_PARAM", ID)};
            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }
    }
}
