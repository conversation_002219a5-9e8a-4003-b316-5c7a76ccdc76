﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.DataAccess.Client;

namespace <PERSON><PERSON><PERSON><PERSON>
{
    public class Arac<PERSON>lak<PERSON>H<PERSON>per
    {
        public static DataTable GridListele()
        {            
            string SQL = "SELECT PLK.ID, SNF.ARAC_SINIFI,RNG.ARAC_RENGI,YIL.ARAC_YILI,MRK.ARAC_MARKASI ,MDL.ARAC_MODELI,PLK.ARAC_PLAKASI, " +
                "PLK.AKTIF,PLK.KAYIT_TARIHI,PLK.DEGISTIRME_TARIHI,PLK.KAYDEDEN,PLK.DEGISTIREN, (CASE WHEN PLK.AKTIF=1 THEN 'Aktif' ELSE 'Pasif' END) AS AKTIF_DURUM " +
                "FROM DT_WORKFLOW.ATS_ARAC_PLAKASI PLK JOIN DT_WORKFLOW.ATS_ARAC_SINIFI SNF ON PLK.ARAC_SINIFI_ID = SNF.ID JOIN DT_WORKFLOW.ATS_ARAC_RENGI RNG ON PLK.ARAC_RENGI_ID = RNG.ID " +
                "JOIN ATS_ARAC_YILI YIL ON PLK.ARAC_YILI_ID = YIL.ID JOIN ATS_ARAC_MARKASI MRK ON PLK.ARAC_MARKASI_ID = MRK.ID JOIN ATS_ARAC_MODELI MDL ON PLK.ARAC_MODELI_ID = MDL.ID order by SNF.ARAC_SINIFI,MRK.ARAC_MARKASI ,MDL.ARAC_MODELI,PLK.ARAC_PLAKASI";
            DataTable Dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            return Dtbsonuc;

        }

        public static bool ValidateAracPlaka(string AracPlakaKodu, int ID)
        {
            string SQL = @"SELECT * FROM DT_WORKFLOW.ATS_ARAC_PLAKASI WHERE ARAC_PLAKASI=:ARAC_PLAKASI_PARAM AND ID<>:ID_PARAM ";
            
            bool sonuc = false;
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ARAC_PLAKASI_PARAM", AracPlakaKodu),
            new OracleParameter("ID_PARAM", ID)};

            using (DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, customList))
            {
                sonuc = dtbsonuc.Rows.Count > 0;
            }

            return sonuc;
        }
    }
}
