﻿using System;
using System.Collections.Generic;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Text.RegularExpressions;

namespace FormHelper
{
    public class CoreHelper
    {
        /// <summary>
        /// Gets the current user's login ID from session
        /// </summary>
        public static decimal LoginId
        {
            get
            {
                if (HttpContext.Current != null && HttpContext.Current.Session != null &&
                    HttpContext.Current.Session["LoginId"] != null)
                {
                    return Convert.ToDecimal(HttpContext.Current.Session["LoginId"]);
                }
                return 0;
            }
        }

        /// <summary>
        /// Gets the current user's username from session
        /// </summary>
        public static string Username
        {
            get
            {
                if (HttpContext.Current != null && HttpContext.Current.Session != null &&
                    HttpContext.Current.Session["Username"] != null)
                {
                    return HttpContext.Current.Session["Username"].ToString();
                }
                return string.Empty;
            }
        }

        /// <summary>
        /// Sets the login ID and username in session
        /// </summary>
        public static void SetCurrentUser(decimal loginId, string username)
        {
            if (HttpContext.Current != null && HttpContext.Current.Session != null)
            {
                HttpContext.Current.Session["LoginId"] = loginId;
                HttpContext.Current.Session["Username"] = username;
            }
        }

        /// <summary>
        /// Gets user information by login ID
        /// </summary>
        public static DataTable GetKullaniciInfo(long loginId)
        {
            string sql = "SELECT * FROM DP_HR_USERS WHERE F_LOGIN_ID=:ID ORDER BY NAME_SURNAME";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("ID", loginId) };
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
        }

        /// <summary>
        /// Gets user login ID by username
        /// </summary>
        public static int GetKullaniciLoginId(string username)
        {
            string sql = "SELECT * FROM DP_HR_USERS WHERE USERNAME=:USERNAME ORDER BY NAME_SURNAME";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("USERNAME", username) };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
            return dt.Rows.Count > 0 ? Convert.ToInt32(dt.Rows[0]["F_LOGIN_ID"]) : 0;
        }

        /// <summary>
        /// Gets user login ID by username (decimal version)
        /// </summary>
        public static decimal GetKullaniciLoginIdDecimal(string username)
        {
            string sql = "SELECT * FROM DP_HR_USERS WHERE USERNAME=:USERNAME ORDER BY NAME_SURNAME";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("USERNAME", username) };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
            return dt.Rows.Count > 0 ? Convert.ToDecimal(dt.Rows[0]["F_LOGIN_ID"]) : 0;
        }

        /// <summary>
        /// Gets user name and surname by login ID
        /// </summary>
        public static string GetKullaniciNameSurname(long loginId)
        {
            string sql = "SELECT * FROM DP_HR_USERS WHERE F_LOGIN_ID=:F_LOGIN_ID ORDER BY NAME_SURNAME";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("F_LOGIN_ID", loginId) };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
            return dt.Rows.Count > 0 ? dt.Rows[0]["NAME_SURNAME"].ToString() : string.Empty;
        }

        /// <summary>
        /// Gets list of all users
        /// </summary>
        public static DataTable GetKullaniciList()
        {
            string sql = "SELECT F_LOGIN_ID, USERNAME, NAME_SURNAME FROM DP_HR_USERS ORDER BY NAME_SURNAME ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        /// <summary>
        /// Gets list of active users
        /// </summary>
        public static DataTable GetActiveKullaniciList()
        {
            string sql = "SELECT F_LOGIN_ID, USERNAME, NAME_SURNAME FROM DP_HR_USERS WHERE AKTIF='1' ORDER BY NAME_SURNAME ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        /// <summary>
        /// Gets user by login ID
        /// </summary>
        public static DataRow GetKullaniciByLoginId(decimal loginId)
        {
            string sql = "SELECT * FROM DP_HR_USERS WHERE F_LOGIN_ID=:F_LOGIN_ID";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("F_LOGIN_ID", loginId) };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
            return dt.Rows.Count > 0 ? dt.Rows[0] : null;
        }

        /// <summary>
        /// Gets user by username
        /// </summary>
        public static DataRow GetKullaniciByUsername(string username)
        {
            string sql = "SELECT * FROM DP_HR_USERS WHERE USERNAME=:USERNAME";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("USERNAME", username) };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
            return dt.Rows.Count > 0 ? dt.Rows[0] : null;
        }

        /// <summary>
        /// Checks if user exists in the system
        /// </summary>
        public static bool KullaniciExists(string username)
        {
            string sql = "SELECT COUNT(*) AS CNT FROM DP_HR_USERS WHERE USERNAME=:USERNAME";
            OracleParameter[] customList = new OracleParameter[] { new OracleParameter("USERNAME", username) };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, customList);
            return Convert.ToInt32(dt.Rows[0]["CNT"]) > 0;
        }

        public static string RegexIleHtmlTemizle(string html)
        {
            return Regex.Replace(html, "<.*?>", string.Empty);
        }
        public static bool isEnglish() {
            return System.Globalization.CultureInfo.CurrentCulture.Name == "en-EN" || System.Globalization.CultureInfo.CurrentCulture.Name == "en-US";
        }
    }
}