﻿using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.DigiportAnket
{
    public class DIGIPORT_ANKET_DOMAIN_Helper
    {
        public static DataTable grdListe()
        {
            string sql = "SELECT * FROM DIGIPORT_ANKET_DOMAIN";
            DataTable dtb = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
            return dtb;
              
        }

        public static bool DomainAltindaUserKaydiVarmi(long domainId)
        {
            bool sonuc;
            string sql = @"SELECT DOMAIN_USER_LOGIN_ID FROM DIGIPORT_ANKET_DOMAIN DOM INNER JOIN DIGIPORT_ANKET_DOMAIN_USERS USR ON DOM.ID=USR.DOMAIN_ID WHERE DOM.ID=:DOMAIN_ID";
            OracleParameter[] dbParameter = new OracleParameter[]
            {
                new OracleParameter("DOMAIN_ID",domainId)                
            };
            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParameter).Rows.Count > 0 ? true : false;
            return sonuc;
        }
    }
}
