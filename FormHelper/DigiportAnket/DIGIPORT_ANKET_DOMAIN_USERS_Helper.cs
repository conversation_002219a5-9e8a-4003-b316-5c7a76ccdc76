﻿using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.DigiportAnket
{
    public class DIGIPORT_ANKET_DOMAIN_USERS_Helper
    {
        public static DataTable grdListe()
        {
            string sql = @"SELECT DOM.ID AS DOMAIN_ID, DOM.DOMAIN,DOM_USR.ID AS DOMAIN_USER_ID,DOM_USR.DOMAIN_USER_LOGIN_ID,USR.NAME_SURNAME FROM DIGIPORT_ANKET_DOMAIN DOM INNER JOIN 
            DIGIPORT_ANKET_DOMAIN_USERS DOM_USR ON DOM.ID=DOM_USR.DOMAIN_ID INNER JOIN DP_HR_USERS USR ON DOM_USR.DOMAIN_USER_LOGIN_ID=USR.F_LOGIN_ID";
            DataTable dtb = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
            return dtb;

        }

        public static DataTable DomainDropDoldur()
        {
            string sql = "SELECT * FROM DIGIPORT_ANKET_DOMAIN ORDER BY DOMAIN ASC";
            DataTable dtb = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
            return dtb;
        }

        public static DataTable UsersDropDoldur()
        {
            string sql = "SELECT * FROM DP_HR_USERS ORDER BY NAME_SURNAME ASC";
            DataTable dtb = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
            return dtb;
        }

        //public static bool domainAltindaUserKaydiVarmi(long domainId)
        //{
        //    bool sonuc;
        //    string sql = @"SELECT DOMAIN_USER_LOGIN_ID FROM DIGIPORT_ANKET_DOMAIN DOM INNER JOIN DIGIPORT_ANKET_DOMAIN_USERS USR ON DOM.ID=USR.DOMAIN_ID WHERE DOM.ID=:DOMAIN_ID";
        //    OracleParameter[] dbParameter = new OracleParameter[]
        //    {
        //        new OracleParameter("DOMAIN_ID",domainId)
        //    };
        //    sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParameter).Rows.Count > 0 ? true : false;
        //    return sonuc;
        //}
    }
}
