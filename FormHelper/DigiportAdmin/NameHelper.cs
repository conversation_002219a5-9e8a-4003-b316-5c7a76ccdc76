using CoreHelpers;
using Entities;
using System;
using System.Collections.Generic;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;

namespace FormHelper.DigiportAdmin
{
    public class NameHelper
    {
        public static DataTable GetTypesList()
        {
            string sql = "SELECT ID, TYPE_NAME FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE WHERE AKTIF = 'Y' ORDER BY ID ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable GridListele()
        {
            return GridListele(0, "", "");
        }

        public static DataTable GridListele(int typeId, string name, string status)
        {
            StringBuilder whereClause = new StringBuilder();
            List<OracleParameter> parameters = new List<OracleParameter>();

            if (typeId > 0)
            {
                whereClause.Append(" AND N.TYPE_ID = :TYPE_ID");
                parameters.Add(new OracleParameter("TYPE_ID", typeId));
            }

            if (!string.IsNullOrEmpty(name))
            {
                whereClause.Append(" AND UPPER(N.NAME) LIKE UPPER(:NAME)");
                parameters.Add(new OracleParameter("NAME", "%" + name + "%"));
            }

            if (!string.IsNullOrEmpty(status))
            {
                whereClause.Append(" AND N.AKTIF = :AKTIF");
                parameters.Add(new OracleParameter("AKTIF", status));
            }
            string SQL = $@"SELECT N.ID, T.TYPE_NAME, N.NAME, N.DESCRIPTION, N.NAME_EN, N.DESCRIPTION_EN, N.PAGE_PATH,
                          CASE WHEN N.AKTIF = '1' THEN 'AKTİF' ELSE 'PASİF' END AS AKTIF_DURUM,
                          N.CREATED, N.CREATED_BY
                          FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N
                          INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                          WHERE 1=1 {whereClause}
                          ORDER BY T.TYPE_NAME, N.NAME ASC"; if (parameters.Count > 0)
            {
                return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL, parameters.ToArray());
            }
            else
            {
                return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
            }
        }

        public static bool MukerrerKayitVarMi(int typeId, string name, int ID)
        {
            bool sonuc = false;
            string sql = "SELECT * FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME WHERE TYPE_ID=:TYPE_ID AND UPPER(NAME)=UPPER(:NAME) AND (:ID=0 OR ID<>:ID)";

            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("TYPE_ID", typeId),
                new OracleParameter("NAME", name),
                new OracleParameter("ID", ID)
            };
            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams).Rows.Count > 0;
            return sonuc;
        }

        public static string GetComponentNameOrDescription(int componentId, bool isName, bool isEnglish)
        {
            string nameCol = isEnglish ? "NAME_EN" : "NAME";
            string descriptionCol = isEnglish ? "DESCRIPTION_EN" : "DESCRIPTION";
            string sql = "SELECT " + (isName ? nameCol : descriptionCol) + " as DEGER FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME WHERE ID=:ID";
            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("ID", componentId)
            };
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
            if (dt.Rows.Count > 0)
                return dt.Rows[0]["DEGER"].ToString();
            else
                return string.Empty;
        }

        public static bool NameSil(int id)
        {
            try
            {
                string sql = "DELETE FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME WHERE ID = :ID";
                OracleParameter[] dbParams = new OracleParameter[]
                {
                    new OracleParameter("ID", id)
                };

                DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", sql, dbParams);
                return true;
            }
            catch (Exception ex)
            {
                // Log the error if logging is available
                //throw new Exception("Failed to delete record: " + ex.Message);
                return false;
            }
        }
    }
}