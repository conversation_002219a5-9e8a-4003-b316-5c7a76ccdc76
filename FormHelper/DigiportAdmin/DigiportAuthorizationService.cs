﻿using CoreHelpers;
using Entities;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Security.Principal;
using System.Web;
using FormHelper.YetkiHelper;
using System.Diagnostics;

namespace FormHelper.DigiportAdmin
{
    /// <summary>
    /// Centralized authorization service for Digiport Admin Portal
    /// Handles all permission checks and access control
    /// </summary>
    public class DigiportAuthorizationService
    {
        #region Singleton Implementation

        private static DigiportAuthorizationService _instance;
        private static readonly object _lock = new object();
        private bool _cacheCleared = false;

        public static DigiportAuthorizationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new DigiportAuthorizationService();
                        }
                    }
                }
                return _instance;
            }
        }

        private DigiportAuthorizationService()
        {
            // Private constructor for singleton pattern
        }

        /// <summary>
        /// Clears any cached authorization data to force re-evaluation of permissions
        /// </summary>
        public void ClearCache()
        {
            _cacheCleared = true;
            Debug.WriteLine("DigiportAuthorizationService: Cache cleared, permissions will be re-evaluated");
        }

        #endregion

        #region Constants and Configuration

        // Admin group names from configuration
        private string TechCorpGroup => ConfigurationManager.AppSettings["AdGroup_TechCorp"];

        // Page types that require special permissions
        private readonly string[] AdminOnlyPages = new[] { "Types.aspx", "Names.aspx", "UserAssignment.aspx" };

        #endregion

        #region User Information Methods

        /// <summary>
        /// Gets the current user's login ID
        /// </summary>
        public long GetCurrentLoginId()
        {
            if (HttpContext.Current?.Session?["LoginId"] != null)
            {
                return Convert.ToInt64(HttpContext.Current.Session["LoginId"]);
            }

            string username = HttpContext.Current?.User?.Identity?.Name;
            if (!string.IsNullOrEmpty(username))
            {
                username = username.Replace("DIGITURK\\", "").Replace("DIGITURKCC\\", "").Replace("DIGITURKTEST\\", "");
                return CoreHelper.GetKullaniciLoginId(username);
            }

            return 0;
        }

        /// <summary>
        /// Gets the current user's Windows username
        /// </summary>
        public string GetCurrentUsername()
        {
            string username = HttpContext.Current?.User?.Identity?.Name;
            if (!string.IsNullOrEmpty(username))
            {
                return username.Replace("DIGITURK\\", "").Replace("DIGITURKCC\\", "").Replace("DIGITURKTEST\\", "");
            }
            return string.Empty;
        }

        /// <summary>
        /// Gets the current user's AD groups
        /// </summary>
        public List<string> GetUserGroups()
        {
            try
            {
                return new YetkiHelper.YetkiHelper().gruplar() ?? new List<string>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user groups: {ex.Message}");
                return new List<string>();
            }
        }

        #endregion

        #region Permission Check Methods

        /// <summary>
        /// Checks if the current user is a system administrator
        /// </summary>
        public bool IsSystemAdmin()
        {
            try
            {
                var userGroups = GetUserGroups()
                    .Select(g => g.ToUpperInvariant())
                    .ToList();

                return userGroups.Contains(TechCorpGroup?.ToUpperInvariant()) ||
                       HttpContext.Current?.Session?["YetkiTipDigiportAdmin"]?.ToString() == "True";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in IsSystemAdmin: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if the current user has permission to access a specific menu item
        /// </summary>
        public bool HasMenuPermission(int menuNameId)
        {
            // System admins have access to all menus
            if (IsSystemAdmin())
            {
                Debug.WriteLine($"HasMenuPermission: User is system admin, granting access to menu {menuNameId}");
                return true;
            }

            try
            {
                long loginId = GetCurrentLoginId();
                string username = GetCurrentUsername();
                var userGroups = GetUserGroups();

                Debug.WriteLine($"HasMenuPermission: Checking permissions for menu {menuNameId}");
                Debug.WriteLine($"HasMenuPermission: User LoginId={loginId}, Username={username}");
                Debug.WriteLine($"HasMenuPermission: User belongs to {userGroups.Count} groups: {string.Join(", ", userGroups)}");

                // Check all assignments for this menu ID
                string checkAssignmentsSql = @"
                    SELECT UA.ID, UA.F_LOGIN_ID, UA.AD_GROUP, UA.AKTIF, N.NAME, T.TYPE_NAME
                    FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N ON UA.NAME_ID = N.ID
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                    WHERE UA.NAME_ID = :NAME_ID";

                var checkParams = new List<OracleParameter>
                {
                    new OracleParameter("NAME_ID", OracleDbType.Int32) { Value = menuNameId }
                };

                var assignments = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", checkAssignmentsSql, checkParams.ToArray());
                Debug.WriteLine($"HasMenuPermission: Found {assignments?.Rows.Count ?? 0} assignments for menu {menuNameId}");

                if (assignments != null && assignments.Rows.Count > 0)
                {
                    foreach (DataRow row in assignments.Rows)
                    {
                        Debug.WriteLine($"HasMenuPermission: Assignment - ID={row["ID"]}, F_LOGIN_ID={row["F_LOGIN_ID"]}, AD_GROUP={row["AD_GROUP"]}, AKTIF={row["AKTIF"]}, NAME={row["NAME"]}, TYPE={row["TYPE_NAME"]}");
                    }
                }

                // Query the database to check if the user has permission
                string sql;

                // Create parameters
                var parameters = new List<OracleParameter>
                {
                    new OracleParameter("NAME_ID", OracleDbType.Int32) { Value = menuNameId },
                    new OracleParameter("LOGIN_ID", OracleDbType.Int64) { Value = loginId }
                };

                // Handle user groups differently based on whether they exist
                if (userGroups.Count == 0)
                {
                    // Simplified SQL without AD_GROUP check when user has no groups
                    sql = @"
                        SELECT COUNT(*) AS PERMISSION_COUNT
                        FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                        WHERE UA.NAME_ID = :NAME_ID
                        AND (UA.AKTIF = '1' OR UA.AKTIF = 'Y' OR UA.AKTIF = '1' OR UPPER(UA.AKTIF) = 'Y' OR UPPER(UA.AKTIF) = 'AKTIF')
                        AND (UA.F_LOGIN_ID = :LOGIN_ID AND UA.F_LOGIN_ID > 0)";
                }
                else
                {
                    // For users with groups, use a direct IN clause
                    string groupsList = string.Join("','", userGroups.Select(g => g.Replace("'", "''")));

                    sql = $@"
                        SELECT COUNT(*) AS PERMISSION_COUNT
                        FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                        WHERE UA.NAME_ID = :NAME_ID
                        AND (UA.AKTIF = '1' OR UA.AKTIF = 'Y' OR UA.AKTIF = '1' OR UPPER(UA.AKTIF) = 'Y' OR UPPER(UA.AKTIF) = 'AKTIF')
                        AND (
                            (UA.F_LOGIN_ID = :LOGIN_ID AND UA.F_LOGIN_ID > 0)
                            OR
                            (UA.AD_GROUP IN ('{groupsList}'))
                        )";
                }

                // Log the SQL query for debugging
                Debug.WriteLine($"HasMenuPermission: Executing SQL query: {sql}");
                Debug.WriteLine($"HasMenuPermission: Parameters: NAME_ID={menuNameId}, LOGIN_ID={loginId}");

                // Execute query
                var result = DataAccessLayer.DAL.ExecuteScalar_Oracle("DT_WORKFLOW", sql, parameters.ToArray());
                bool hasPermission = Convert.ToInt32(result) > 0;

                Debug.WriteLine($"HasMenuPermission: Permission check result for menu {menuNameId}: {hasPermission}");

                // If permission granted, log which menu item was granted
                if (hasPermission)
                {
                    string menuInfoSql = @"
                        SELECT N.NAME, T.TYPE_NAME
                        FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N
                        INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                        WHERE N.ID = :NAME_ID";

                    var menuInfoParams = new OracleParameter[] { new OracleParameter("NAME_ID", menuNameId) };
                    var menuInfo = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", menuInfoSql, menuInfoParams);

                    if (menuInfo != null && menuInfo.Rows.Count > 0)
                    {
                        string menuName = menuInfo.Rows[0]["NAME"].ToString();
                        string typeName = menuInfo.Rows[0]["TYPE_NAME"].ToString();
                        Debug.WriteLine($"HasMenuPermission: Granted access to menu '{typeName} - {menuName}' (ID={menuNameId})");
                    }
                }

                // If no permission, try to get more information about why
                if (!hasPermission)
                {
                    // Check if the menu exists
                    string menuCheckSql = "SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME WHERE ID = :ID";
                    var menuCheckParams = new OracleParameter[] { new OracleParameter("ID", menuNameId) };
                    var menuExists = Convert.ToInt32(DataAccessLayer.DAL.ExecuteScalar_Oracle("DT_WORKFLOW", menuCheckSql, menuCheckParams)) > 0;

                    Debug.WriteLine($"HasMenuPermission: Menu {menuNameId} exists in database: {menuExists}");

                    // Check if there are any assignments for this menu
                    string assignmentCheckSql = "SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT WHERE NAME_ID = :NAME_ID";
                    var assignmentCheckParams = new OracleParameter[] { new OracleParameter("NAME_ID", menuNameId) };
                    var assignmentsExist = Convert.ToInt32(DataAccessLayer.DAL.ExecuteScalar_Oracle("DT_WORKFLOW", assignmentCheckSql, assignmentCheckParams)) > 0;

                    Debug.WriteLine($"HasMenuPermission: Assignments exist for menu {menuNameId}: {assignmentsExist}");

                    // Check if there are any assignments for this user
                    string userAssignmentSql = @"
                        SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                        WHERE NAME_ID = :NAME_ID AND F_LOGIN_ID = :LOGIN_ID";
                    var userAssignmentParams = new OracleParameter[] {
                        new OracleParameter("NAME_ID", menuNameId),
                        new OracleParameter("LOGIN_ID", loginId)
                    };
                    var userAssignmentsExist = Convert.ToInt32(DataAccessLayer.DAL.ExecuteScalar_Oracle("DT_WORKFLOW", userAssignmentSql, userAssignmentParams)) > 0;

                    Debug.WriteLine($"HasMenuPermission: User-specific assignments exist for menu {menuNameId}: {userAssignmentsExist}");
                }

                return hasPermission;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in HasMenuPermission: {ex.Message}");
                Debug.WriteLine($"Error details: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// Checks if the current user has permission to access a specific page
        /// </summary>
        public bool HasPagePermission(string pagePath, int? menuId = null)
        {
            // System admins have access to all pages
            if (IsSystemAdmin())
            {
                return true;
            }

            // Admin-only pages are restricted to system admins
            string pageName = System.IO.Path.GetFileName(pagePath);
            if (AdminOnlyPages.Contains(pageName, StringComparer.OrdinalIgnoreCase))
            {
                return false;
            }

            // If menuId is provided, check specific menu permission
            if (menuId.HasValue)
            {
                return HasMenuPermission(menuId.Value);
            }

            // For other pages, try to determine the menu ID from the query string
            if (HttpContext.Current?.Request?.QueryString["MID"] != null &&
                int.TryParse(HttpContext.Current.Request.QueryString["MID"], out int mid))
            {
                return HasMenuPermission(mid);
            }

            // Default to deny if we can't determine permissions
            return false;
        }

        #endregion

        #region User Assignment Methods

        /// <summary>
        /// Gets all menu items that the current user has permission to access
        /// </summary>
        public DataTable GetUserAuthorizedMenus()
        {
            try
            {
                Debug.WriteLine("GetUserAuthorizedMenus: Starting to retrieve authorized menus");

                // Reset cache cleared flag
                bool wasCacheCleared = _cacheCleared;
                if (_cacheCleared)
                {
                    _cacheCleared = false;
                    Debug.WriteLine("GetUserAuthorizedMenus: Cache was cleared, forcing refresh of menu items");
                }

                // System admins have access to all menus
                if (IsSystemAdmin())
                {
                    Debug.WriteLine("GetUserAuthorizedMenus: User is system admin, retrieving all menus");

                    string sql = @"
                        SELECT N.ID, N.NAME,N.NAME_EN, N.DESCRIPTION,N.DESCRIPTION_EN, N.PAGE_PATH, T.TYPE_NAME, '1' AS AKTIF
                        FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N
                        INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                        WHERE (N.AKTIF = '1' OR N.AKTIF = 'Y' OR UPPER(N.AKTIF) = 'Y' OR N.AKTIF IS NULL)
                          AND (T.AKTIF = '1' OR T.AKTIF = 'Y' OR UPPER(T.AKTIF) = 'Y' OR T.AKTIF IS NULL)
                        ORDER BY T.TYPE_NAME, N.NAME";

                    Debug.WriteLine("GetUserAuthorizedMenus: SQL for system admin: " + sql);

                    var result = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
                    Debug.WriteLine($"GetUserAuthorizedMenus: Retrieved {result.Rows.Count} menu items for admin user");
                    HttpContext.Current.Session["UserAuthorizedMenus"] = result;
                    return result;
                }

                // Regular users only have access to assigned menus
                long loginId = GetCurrentLoginId();
                string username = GetCurrentUsername();
                var userGroups = GetUserGroups();

                Debug.WriteLine($"GetUserAuthorizedMenus: Retrieving menus for regular user");
                Debug.WriteLine($"GetUserAuthorizedMenus: User LoginId={loginId}, Username={username}");
                Debug.WriteLine($"GetUserAuthorizedMenus: User belongs to {userGroups.Count} groups: {string.Join(", ", userGroups)}");

                // Check the AKTIF values in the database
                string checkAktifSql = @"
                    SELECT DISTINCT UA.AKTIF
                    FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                    WHERE ROWNUM <= 10";

                var aktifValues = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", checkAktifSql);
                Debug.WriteLine($"GetUserAuthorizedMenus: Found {aktifValues.Rows.Count} different AKTIF values in database");
                foreach (DataRow row in aktifValues.Rows)
                {
                    Debug.WriteLine($"GetUserAuthorizedMenus: AKTIF value: {row[0]}");
                }

                // Debug the AKTIF values in the database
                Debug.WriteLine("GetUserAuthorizedMenus: Checking user assignments for LoginId=" + loginId);
                string checkUserAssignmentsSql = @"
                    SELECT UA.NAME_ID, UA.AKTIF, N.NAME, T.TYPE_NAME
                    FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N ON UA.NAME_ID = N.ID
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                    WHERE UA.F_LOGIN_ID = :LOGIN_ID";
                var checkUserParams = new OracleParameter[] { new OracleParameter("LOGIN_ID", loginId) };
                var userAssignments = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", checkUserAssignmentsSql, checkUserParams);

                Debug.WriteLine($"GetUserAuthorizedMenus: Found {userAssignments?.Rows.Count ?? 0} user assignments");
                if (userAssignments != null && userAssignments.Rows.Count > 0)
                {
                    foreach (DataRow row in userAssignments.Rows)
                    {
                        Debug.WriteLine($"GetUserAuthorizedMenus: User assignment - NAME_ID={row["NAME_ID"]}, NAME={row["NAME"]}, TYPE={row["TYPE_NAME"]}, AKTIF={row["AKTIF"]}");
                    }
                }

                // Also check AD group assignments
                Debug.WriteLine("GetUserAuthorizedMenus: Checking AD group assignments");
                string checkGroupAssignmentsSql = @"
                    SELECT UA.NAME_ID, UA.AKTIF, UA.AD_GROUP, N.NAME, T.TYPE_NAME
                    FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N ON UA.NAME_ID = N.ID
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                    WHERE UA.AD_GROUP IS NOT NULL";
                var groupAssignments = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", checkGroupAssignmentsSql);

                Debug.WriteLine($"GetUserAuthorizedMenus: Found {groupAssignments?.Rows.Count ?? 0} group assignments");
                if (groupAssignments != null && groupAssignments.Rows.Count > 0)
                {
                    foreach (DataRow row in groupAssignments.Rows)
                    {
                        Debug.WriteLine($"GetUserAuthorizedMenus: Group assignment - NAME_ID={row["NAME_ID"]}, NAME={row["NAME"]}, TYPE={row["TYPE_NAME"]}, AD_GROUP={row["AD_GROUP"]}, AKTIF={row["AKTIF"]}");
                    }
                }

                // Modified SQL to be more inclusive with AKTIF values and handle numeric values correctly
                string userSql = @"
                    SELECT N.ID, N.NAME,N.NAME_EN, N.DESCRIPTION,N.DESCRIPTION_EN, N.PAGE_PATH, T.TYPE_NAME, UA.AKTIF
                    FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA ON N.ID = UA.NAME_ID
                    WHERE (N.AKTIF = '1' OR N.AKTIF = 'Y' OR UPPER(N.AKTIF) = 'Y' OR N.AKTIF IS NULL)
                      AND (T.AKTIF = '1' OR T.AKTIF = 'Y' OR UPPER(T.AKTIF) = 'Y' OR T.AKTIF IS NULL)
                      AND (UA.AKTIF = '1' OR UA.AKTIF = 'Y' OR UPPER(UA.AKTIF) = 'Y' OR UPPER(UA.AKTIF) = 'AKTIF' OR UA.AKTIF IS NULL)
                      AND (
                          (UA.F_LOGIN_ID = :LOGIN_ID AND UA.F_LOGIN_ID > 0)
                          OR
                          (UA.AD_GROUP IN (SELECT COLUMN_VALUE FROM TABLE(CAST(:USER_GROUPS AS SYS.ODCIVARCHAR2LIST))))
                      )
                    ORDER BY T.TYPE_NAME, N.NAME";

                Debug.WriteLine("GetUserAuthorizedMenus: SQL for regular user: " + userSql);

                // Create parameters
                var parameters = new List<OracleParameter>
                {
                    new OracleParameter("LOGIN_ID", OracleDbType.Int64) { Value = loginId }
                };

                // If user has no groups, modify the SQL to avoid using the collection parameter
                if (userGroups.Count == 0)
                {
                    // Simplified SQL without AD_GROUP check when user has no groups
                    userSql = @"
                        SELECT N.ID, N.NAME,N.NAME_EN, N.DESCRIPTION,N.DESCRIPTION_EN, N.PAGE_PATH, T.TYPE_NAME, UA.AKTIF
                        FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N
                        INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                        INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA ON N.ID = UA.NAME_ID
                        WHERE (N.AKTIF = '1' OR N.AKTIF = 'Y' OR UPPER(N.AKTIF) = 'Y' OR N.AKTIF IS NULL)
                          AND (T.AKTIF = '1' OR T.AKTIF = 'Y' OR UPPER(T.AKTIF) = 'Y' OR T.AKTIF IS NULL)
                          AND (UA.AKTIF = '1' OR UA.AKTIF = 'Y' OR UPPER(UA.AKTIF) = 'Y' OR UPPER(UA.AKTIF) = 'AKTIF' OR UA.AKTIF IS NULL)
                          AND (UA.F_LOGIN_ID = :LOGIN_ID AND UA.F_LOGIN_ID > 0)
                        ORDER BY T.TYPE_NAME, N.NAME";

                    Debug.WriteLine("GetUserAuthorizedMenus: Simplified SQL for user with no groups: " + userSql);
                }
                else
                {
                    // For users with groups, use a different approach to handle the collection
                    // Create a comma-separated list of groups for an IN clause
                    string groupsList = string.Join("','", userGroups.Select(g => g.Replace("'", "''")));

                    // Modify SQL to use direct IN clause instead of collection
                    userSql = userSql.Replace(
                        "(UA.AD_GROUP IN (SELECT COLUMN_VALUE FROM TABLE(CAST(:USER_GROUPS AS SYS.ODCIVARCHAR2LIST))))",
                        $"(UA.AD_GROUP IN ('{groupsList}'))");
                }

                // Log the SQL query for debugging
                Debug.WriteLine($"GetUserAuthorizedMenus: Executing SQL query: {userSql}");
                Debug.WriteLine($"GetUserAuthorizedMenus: Parameters: LOGIN_ID={loginId}");

                try
                {
                    var result = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", userSql, parameters.ToArray());
                    Debug.WriteLine($"GetUserAuthorizedMenus: Retrieved {result.Rows.Count} menu items for regular user");

                    // Check for specific user ABBAS BULUT
                    if (loginId == 181180740)
                    {
                        Debug.WriteLine("GetUserAuthorizedMenus: This is ABBAS BULUT (ID=181180740)");

                        // Check if menu item 3 (Sağ Galeri) exists in the database
                        string checkMenuSql = "SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME WHERE ID = 3";
                        var menuExists = Convert.ToInt32(DataAccessLayer.DAL.ExecuteScalar_Oracle("DT_WORKFLOW", checkMenuSql)) > 0;
                        Debug.WriteLine($"GetUserAuthorizedMenus: Menu ID 3 (Sağ Galeri) exists in database: {menuExists}");

                        // Check if ABBAS BULUT has an assignment for menu item 3
                        string checkAssignmentSql = "SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT WHERE NAME_ID = 3 AND F_LOGIN_ID = 181180740";
                        var assignmentExists = Convert.ToInt32(DataAccessLayer.DAL.ExecuteScalar_Oracle("DT_WORKFLOW", checkAssignmentSql)) > 0;
                        Debug.WriteLine($"GetUserAuthorizedMenus: ABBAS BULUT has assignment for Menu ID 3: {assignmentExists}");

                        if (assignmentExists)
                        {
                            // Get details of the assignment
                            string assignmentDetailsSql = "SELECT AKTIF FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT WHERE NAME_ID = 3 AND F_LOGIN_ID = 181180740";
                            var assignmentDetails = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", assignmentDetailsSql);
                            if (assignmentDetails.Rows.Count > 0)
                            {
                                Debug.WriteLine($"GetUserAuthorizedMenus: ABBAS BULUT's assignment AKTIF value: {assignmentDetails.Rows[0]["AKTIF"]}");
                            }
                        }
                    }
                    HttpContext.Current.Session["UserAuthorizedMenus"] = result;
                    return result;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"GetUserAuthorizedMenus: Error executing SQL: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetUserAuthorizedMenus: {ex.Message}");
                Debug.WriteLine($"Error details: {ex.StackTrace}");
                HttpContext.Current.Session["UserAuthorizedMenus"] = null;
                return new DataTable();
            }
        }

        #endregion

        #region Diagnostic Methods

        /// <summary>
        /// Logs all menu items in the database for debugging
        /// </summary>
        public void LogAllMenuItems()
        {
            try
            {
                string sql = @"
                    SELECT N.ID, N.NAME, N.DESCRIPTION, N.PAGE_PATH, T.TYPE_NAME, N.AKTIF, T.AKTIF AS T_AKTIF
                    FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N
                    INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                    ORDER BY T.TYPE_NAME, N.NAME";

                var allMenus = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);

                Debug.WriteLine($"LogAllMenuItems: Found {allMenus.Rows.Count} menu items in database");
                foreach (DataRow row in allMenus.Rows)
                {
                    Debug.WriteLine($"Menu: ID={row["ID"]}, NAME={row["NAME"]}, TYPE={row["TYPE_NAME"]}, PAGE_PATH={row["PAGE_PATH"]}, AKTIF={row["AKTIF"]}, T_AKTIF={row["T_AKTIF"]}");
                }

                // Also check user assignments
                string assignmentSql = @"
                    SELECT UA.NAME_ID, UA.F_LOGIN_ID, UA.AD_GROUP, UA.AKTIF
                    FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                    ORDER BY UA.NAME_ID";

                var assignments = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", assignmentSql);

                Debug.WriteLine($"LogAllMenuItems: Found {assignments.Rows.Count} user assignments in database");
                foreach (DataRow row in assignments.Rows)
                {
                    Debug.WriteLine($"Assignment: NAME_ID={row["NAME_ID"]}, F_LOGIN_ID={row["F_LOGIN_ID"]}, AD_GROUP={row["AD_GROUP"]}, AKTIF={row["AKTIF"]}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LogAllMenuItems: {ex.Message}");
            }
        }

        #endregion
    }
}
