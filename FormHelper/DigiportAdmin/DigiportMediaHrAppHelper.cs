﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;

namespace FormHelper.DigiportAdmin
{
    public class DigiportMediaHrAppHelper
    {

        private string componentName;
        public DigiportMediaHrAppHelper(string _componentName)
        {
            componentName = _componentName;
        }
        private List<string> listMediaUploaded;
        public List<string> ListMediaUploaded
        {
            get
            {
                if (HttpContext.Current.Session["ListMediaUploadedHrApp_" + componentName + "_" + HttpContext.Current.Session.SessionID] != null)

                    listMediaUploaded = (List<string>)HttpContext.Current.Session["ListMediaUploadedHrApp_" + componentName + "_" + HttpContext.Current.Session.SessionID];
                else
                    listMediaUploaded = new List<string>();

                return listMediaUploaded;
            }
            set
            {
                HttpContext.Current.Session["ListMediaUploadedHrApp_" + componentName + "_" + HttpContext.Current.Session.SessionID] = value;
            }
        }

        private List<string> listMediaRemoved;
        public List<string> ListMediaRemoved
        {
            get
            {
                if (HttpContext.Current.Session["ListMediaRemovedHrApp_" + componentName + "_" + HttpContext.Current.Session.SessionID] != null)

                    listMediaRemoved = (List<string>)HttpContext.Current.Session["ListMediaRemovedHrApp_" + componentName + "_" + HttpContext.Current.Session.SessionID];
                else
                    listMediaRemoved = new List<string>();

                return listMediaRemoved;
            }
            set
            {
                HttpContext.Current.Session["ListMediaRemovedHrApp_" + componentName + "_" + HttpContext.Current.Session.SessionID] = value;
            }
        }

        public void ClearMediaUploadSession()
        {
            ListMediaUploaded = null;
        }
        public void ClearMediaRemoveSession()
        {
            ListMediaRemoved = null;
        }
        public void AddUploadedMediaToSession(string mediaPath)
        {
            List<string> list = ListMediaUploaded;
            if (!list.Exists(x => x == mediaPath))
            {
                list.Add(mediaPath);
                ListMediaUploaded = list;
            }
        }
        public void AddRemovedMediaToSession(string mediaPath)
        {
            List<string> list = ListMediaRemoved;
            if (!list.Exists(x => x == mediaPath))
            {
                list.Add(mediaPath);
                ListMediaRemoved = list;
            }
        }

        public void MoveUploadedContentToRealFolder(string realFolderPath)
        {
            if (!Directory.Exists(HttpContext.Current.Server.MapPath(realFolderPath)))
                Directory.CreateDirectory(HttpContext.Current.Server.MapPath(realFolderPath));
            foreach (string item in ListMediaUploaded)
            {
                string fileName = Path.GetFileName(item);
                string realFilePath = realFolderPath + "/" + fileName;
                realFilePath = HttpContext.Current.Server.MapPath(realFilePath);
                byte[] bytes = File.ReadAllBytes(item);
                File.WriteAllBytes(realFilePath, bytes);
                try
                {
                    File.Delete(item);
                }
                catch
                {
                }
            }
            ClearMediaUploadSession();
            ClearOldTempFiles();
        }

        private void ClearOldTempFiles()
        {
            string uploadPath = HttpContext.Current.Server.MapPath("~/Content/SummerNoteTempUploads/HrApp/" + componentName + "/");
            string directoryPath = Path.GetDirectoryName(uploadPath);
            if (Directory.Exists(directoryPath))
            {
                foreach (string filePath in Directory.GetFiles(uploadPath,"*",SearchOption.AllDirectories))
                {
                    FileInfo finfo = new FileInfo(filePath);
                    if (Math.Abs(DateTime.Today.Subtract(finfo.CreationTime).TotalDays) > 3)
                    {
                        try
                        {
                            if (File.Exists(filePath))
                                File.Delete(filePath);
                        }
                        catch
                        {
                        }
                    }
                }
            }
        }

        public void DeleteRealFolderContents()
        {
            try
            {
                foreach (string item in ListMediaRemoved)
                {
                    if (File.Exists(item))
                        File.Delete(item);
                }
                ClearMediaRemoveSession();
            }
            catch
            {
            }
        }

        public List<string> ExtractSummerNoteUploadPaths(string html)
        {
            var paths = new List<string>();

            // img ve video src'lerini yakalayan regex
            string mediaPattern = @"<(?:img|video)[^>]*?src\s*=\s*[""'](?<src>/Content/SummerNoteUploads/HrApp/[^""']+)[""'][^>]*>";

            // a taglerinde class="uploadedOtherMedia" ve href değerini yakalayan regex
            string anchorPattern = @"<(?:a)[^>]*?href\s*=\s*[""'](?<href>/Content/SummerNoteUploads/HrApp/[^""']+)[""'][^>]*>";

            // img/video için eşleşmeler
            MatchCollection mediaMatches = Regex.Matches(html, mediaPattern, RegexOptions.IgnoreCase);
            foreach (Match match in mediaMatches)
            {
                string src = match.Groups["src"].Value;
                if (!string.IsNullOrEmpty(src))
                {
                    paths.Add(HttpContext.Current.Server.MapPath(src));
                }
            }

            // a tag için eşleşmeler
            MatchCollection anchorMatches = Regex.Matches(html, anchorPattern, RegexOptions.IgnoreCase);
            foreach (Match match in anchorMatches)
            {
                string href = match.Groups["href"].Value;
                if (!string.IsNullOrEmpty(href))
                {
                    paths.Add(HttpContext.Current.Server.MapPath(href));
                }
            }

            return paths;
        }
    }
}
