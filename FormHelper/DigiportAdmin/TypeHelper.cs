using CoreHelpers;
using Entities;
using System;
using System.Data;
using Oracle.DataAccess.Client;
using System.Collections.Generic;

namespace FormHelper.DigiportAdmin
{
    public class TypeHelper
    {
        public static DataTable GridListele()
        {
            string SQL = @"SELECT ID, TYPE_NAME, DESCRIPTION, CREATED,
                          CASE WHEN AKTIF = 'Y' THEN 'AKTİF' ELSE 'PASİF' END AS AKTIF_DURUM
                          FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE
                          ORDER BY TYPE_NAME ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
        }

        public static bool MukerrerKayitVarMi(string typeName, int ID)
        {
            bool sonuc = false;
            string sql = "SELECT * FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE WHERE UPPER(TYPE_NAME)=UPPER(:TYPE_NAME) AND (:ID=0 OR ID<>:ID)";

            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("TYPE_NAME", typeName),
                new OracleParameter("ID", ID)
            };
            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams).Rows.Count > 0;
            return sonuc;
        }

        public static bool IsDuplicateName(string typeName)
        {
            return IsDuplicateName(typeName, 0); // Call the existing logic with ID = 0 for new records
        }

        public static bool IsDuplicateName(string typeName, int id)
        {
            bool sonuc = false;
            string sql = "SELECT * FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE WHERE UPPER(TYPE_NAME)=UPPER(:TYPE_NAME) AND (:ID=0 OR ID<>:ID)";

            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("TYPE_NAME", typeName),
                new OracleParameter("ID", id)
            };
            sonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams).Rows.Count > 0;
            return sonuc;
        }

        public static DIGIPORT_ADMIN_MENU_TYPE GetTypeById(int id)
        {
            DIGIPORT_ADMIN_MENU_TYPE entity = null;
            string sql = "SELECT ID, TYPE_NAME, DESCRIPTION, AKTIF, CREATED, CREATED_BY, LAST_UPDATED, LAST_UPDATED_BY FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE WHERE ID = :ID";
            OracleParameter[] dbParams = new OracleParameter[] { new OracleParameter("ID", id) };
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);

            if (dt.Rows.Count > 0)
            {
                DataRow row = dt.Rows[0];
                entity = new DIGIPORT_ADMIN_MENU_TYPE
                {
                    ID = Convert.ToDecimal(row["ID"]),
                    TYPE_NAME = row["TYPE_NAME"].ToString(),
                    DESCRIPTION = row["DESCRIPTION"].ToString(),
                    AKTIF = row["AKTIF"].ToString(),
                    CREATED = Convert.ToDateTime(row["CREATED"]),
                    CREATED_BY = Convert.ToDecimal(row["CREATED_BY"]),
                    LAST_UPDATED = row["LAST_UPDATED"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(row["LAST_UPDATED"]),
                    LAST_UPDATED_BY = row["LAST_UPDATED_BY"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(row["LAST_UPDATED_BY"])
                };
            }
            return entity;
        }

        private static decimal? GetLoginIdFromUserInfo(object userInfo)
        {
            if (userInfo == null) return null;

            // Option 1: Check if it's FormHelper.YetkiHelper.UserInfo
            if (userInfo is FormHelper.YetkiHelper.UserInfo yetkiUserInfo)
            {
                return yetkiUserInfo.LoginId;
            }

            // Option 2: Try to get LOGIN_ID property via reflection (if it's an unknown object type)
            try
            {
                var type = userInfo.GetType();
                var propertyInfo = type.GetProperty("LOGIN_ID");
                if (propertyInfo != null && propertyInfo.PropertyType == typeof(decimal))
                {
                    return (decimal)propertyInfo.GetValue(userInfo, null);
                }
                // Try with "LoginId" as well, common naming convention
                propertyInfo = type.GetProperty("LoginId");
                if (propertyInfo != null && propertyInfo.PropertyType == typeof(decimal))
                {
                    return (decimal)propertyInfo.GetValue(userInfo, null);
                }
            }
            catch (Exception ex)
            {
                // Log reflection error if necessary
                System.Diagnostics.Debug.WriteLine($"Error getting LOGIN_ID via reflection: {ex.Message}");
            }

            // Option 3: Check if userInfo itself is a decimal (the LOGIN_ID)
            if (userInfo is decimal loginIdDecimal)
            {
                return loginIdDecimal;
            }

            // Option 4: Check if userInfo is a string that can be parsed to decimal
            if (userInfo is string loginIdString)
            {
                if (decimal.TryParse(loginIdString, out decimal parsedDecimal))
                {
                    return parsedDecimal;
                }
            }

            return null; // Unable to determine LOGIN_ID
        }

        public static bool AddType(DIGIPORT_ADMIN_MENU_TYPE entity, object userInfo)
        {
            decimal? loginId = GetLoginIdFromUserInfo(userInfo);
            if (loginId.HasValue)
            {
                entity.CREATED_BY = loginId.Value;
            }
            // else: CREATED_BY will not be set, or handle error/default if it's mandatory

            entity.CREATED = DateTime.Now;
            string sql = entity.INSERT_SQL();
            string result = DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", sql);

            if (int.TryParse(result, out int affectedRows))
            {
                return affectedRows > 0;
            }
            // If result is not an int (e.g. Oracle specific success/error code as string)
            // Add specific checks here, e.g. if (result == "SUCCESS_CODE_FROM_ORACLE") return true;
            // For now, assuming non-integer means failure if not parsed to > 0 rows.
            return false;
        }

        public static bool UpdateType(DIGIPORT_ADMIN_MENU_TYPE entity, object userInfo)
        {
            decimal? loginId = GetLoginIdFromUserInfo(userInfo);
            if (loginId.HasValue)
            {
                entity.LAST_UPDATED_BY = loginId.Value;
            }
            // else: LAST_UPDATED_BY will not be set

            entity.LAST_UPDATED = DateTime.Now;
            string sql = entity.UPDATE_SQL();
            string result = DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", sql);

            if (int.TryParse(result, out int affectedRows))
            {
                return affectedRows > 0;
            }
            return false;
        }
        public static bool DeleteType(int id, object userInfo)
        {
            DIGIPORT_ADMIN_MENU_TYPE entity = GetTypeById(id);
            if (entity == null) return false;

            // Hard delete - no need for audit fields
            string sql = entity.DELETE_SQL();
            string result = DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", sql);

            if (int.TryParse(result, out int affectedRows))
            {
                return affectedRows > 0;
            }
            return false;
        }
        public static List<DIGIPORT_ADMIN_MENU_TYPE> GetAllTypes()
        {
            var types = new List<DIGIPORT_ADMIN_MENU_TYPE>();
            string sql = @"SELECT ID, TYPE_NAME, DESCRIPTION, AKTIF, CREATED, CREATED_BY, LAST_UPDATED, LAST_UPDATED_BY
                          FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE
                          ORDER BY TYPE_NAME ASC";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);

            foreach (DataRow row in dt.Rows)
            {
                var type = new DIGIPORT_ADMIN_MENU_TYPE
                {
                    ID = Convert.ToDecimal(row["ID"]),
                    TYPE_NAME = row["TYPE_NAME"].ToString(),
                    DESCRIPTION = row["DESCRIPTION"]?.ToString(),
                    AKTIF = row["AKTIF"].ToString(),
                    CREATED = Convert.ToDateTime(row["CREATED"]),
                    CREATED_BY = Convert.ToDecimal(row["CREATED_BY"]),
                    LAST_UPDATED = row["LAST_UPDATED"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(row["LAST_UPDATED"]),
                    LAST_UPDATED_BY = row["LAST_UPDATED_BY"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(row["LAST_UPDATED_BY"])
                };
                types.Add(type);
            }

            return types;
        }
    }
}