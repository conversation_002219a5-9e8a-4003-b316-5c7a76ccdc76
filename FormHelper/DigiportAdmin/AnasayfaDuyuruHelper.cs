﻿using CoreHelpers;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.DigiportAdmin
{
    public class AnasayfaDuyuruHelper
    {
        public static string GetNextOrderNo(int componentType)
        {
            string sql = @"select * from (select X.ORDER_NO from DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_DUYURU X
where X.DELETED='0' and  X.MENU_NAME_ID=:componentType and X.ACTIVE='1'  order by X.ORDER_NO desc) where RowNUm=1";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, new OracleParameter[] {
                new OracleParameter("componentType",componentType)
            });
            if (dt.Rows.Count > 0)
            {
                int lastNo = ConvertionHelper.ConvertValue<int>(dt.Rows[0]["ORDER_NO"].ToString());
                return (lastNo + 1).ToString();
            }
            else
                return "1";
        }

        public static DataTable DuyuruTabloGetir(int componentType, int aktifDurum)
        {
            bool isDebugMode = System.Configuration.ConfigurationManager.AppSettings["debugMode"] == "true";
            string url = isDebugMode ? ConfigurationManager.AppSettings["DigiflowTestDomain"] : ConfigurationManager.AppSettings["DigiflowLiveDomain"];
            string sql = @"select
X.ID,
NVL(X.CONTENT,'') as CONTENT,
X.ACTIVE,
X.ORDER_NO,
X.CLICK_ACTION,
X.TARGET_LINK,
NVL(X.POPUP_WIDTH,0) as POPUP_WIDTH,
NVL(X.POPUP_HEIGHT,0) as POPUP_HEIGHT,
NVL(X.TARGET_CONTENT,'') as TARGET_CONTENT,
NVL(X.TARGET_HEADLINE,'') as TARGET_HEADLINE,
'' as DIGIFLOW_URL,
'' as DIGIPORT_URL,
'' as AJANS_URL

from DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_DUYURU X
where X.DELETED='0' and  X.MENU_NAME_ID=:componentType and (:aktifDurum=2 or (:aktifDurum=1 and X.ACTIVE='1') or (:aktifDurum=0 and X.ACTIVE='0'))
order by X.ACTIVE desc,X.ORDER_NO";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, new OracleParameter[] {
                new OracleParameter("componentType",componentType),
                new OracleParameter("aktifDurum",aktifDurum)
            });
            dt.Columns["DIGIFLOW_URL"].ReadOnly = false;
            dt.Columns["DIGIFLOW_URL"].MaxLength = Int32.MaxValue;
            dt.Columns["DIGIPORT_URL"].ReadOnly = false;
            dt.Columns["DIGIPORT_URL"].MaxLength = Int32.MaxValue;
            dt.Columns["AJANS_URL"].ReadOnly = false;
            dt.Columns["AJANS_URL"].MaxLength = Int32.MaxValue;
            dt.Rows.Cast<DataRow>().ToList().ForEach(
                row =>
                {
                    row["DIGIFLOW_URL"] = url + "Digiport/DisplayContent.aspx?content-id=" + row["ID"].ToString() + "&component-type=anasayfaduyuru";
                    row["DIGIPORT_URL"] = ConfigurationManager.AppSettings["DigiportDisplayPagePath"] + "?content-id=" + row["ID"].ToString() + "&component-type=anasayfaduyuru";
                    row["AJANS_URL"] = ConfigurationManager.AppSettings["AjansDisplayPagePath"] + "?content-id=" + row["ID"].ToString() + "&component-type=anasayfaduyuru";
                });
            return dt;
        }
    }
}
