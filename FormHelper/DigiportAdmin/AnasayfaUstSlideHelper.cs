﻿using CoreHelpers;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.DigiportAdmin
{
    public class AnasayfaUstSlideHelper
    {
        public static DataTable GetSlidesTable(int slideType, int slideAktifDurum)
        {
            bool isDebugMode = System.Configuration.ConfigurationManager.AppSettings["debugMode"] == "true";
            string url = isDebugMode ? ConfigurationManager.AppSettings["DigiflowTestDomain"] : ConfigurationManager.AppSettings["DigiflowLiveDomain"];
            string sql = @"select
X.ID,
X.SLIDE_NAME,
X.SLIDE_IMAGE_PATH,
X.THUMBNAIL_IMAGE_PATH,
X.VALID_DATE_START,
TO_CHAR(X.VALID_DATE_START,'dd.MM.yyyy') as VALID_DATE_START_TEXT,
X.VALID_DATE_END,
TO_CHAR(X.VALID_DATE_END,'dd.MM.yyyy') as VALID_DATE_END_TEXT,
X.ACTIVE,
X.ORDER_NO,
X.SLIDE_CLICK_ACTION,
X.SLIDE_TARGET_LINK,
NVL(X.SLIDE_POPUP_WIDTH,0) as SLIDE_POPUP_WIDTH,
NVL(X.SLIDE_POPUP_HEIGHT,0) as SLIDE_POPUP_HEIGHT,
NVL(X.SLIDE_TARGET_CONTENT,'') as SLIDE_TARGET_CONTENT,
NVL(X.SLIDE_TARGET_HEADLINE,'') as SLIDE_TARGET_HEADLINE,
'' as DIGIFLOW_URL,
'' as DIGIPORT_URL,
'' as AJANS_URL

from DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE X
where X.DELETED='0' and  X.MENU_NAME_ID=:slideType and (:slideAktifDurum=2 or (:slideAktifDurum=1 and X.ACTIVE='1') or (:slideAktifDurum=0 and X.ACTIVE='0'))
order by X.ACTIVE desc,X.ORDER_NO,X.VALID_DATE_END desc,X.SLIDE_NAME";
            DataTable dt= DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, new OracleParameter[] {
                new OracleParameter("slideType",slideType),
                new OracleParameter("slideAktifDurum",slideAktifDurum)
            });
            dt.Columns["DIGIFLOW_URL"].ReadOnly = false;
            dt.Columns["DIGIFLOW_URL"].MaxLength = Int32.MaxValue;
            dt.Columns["DIGIPORT_URL"].ReadOnly = false;
            dt.Columns["DIGIPORT_URL"].MaxLength = Int32.MaxValue;
            dt.Columns["AJANS_URL"].ReadOnly = false;
            dt.Columns["AJANS_URL"].MaxLength = Int32.MaxValue;
            dt.Rows.Cast<DataRow>().ToList().ForEach(
                row =>
                {
                    row["DIGIFLOW_URL"] = url + "Digiport/DisplayContent.aspx?content-id=" + row["ID"].ToString() + "&component-type=anasayfaustslide";
                    row["DIGIPORT_URL"] = ConfigurationManager.AppSettings["DigiportDisplayPagePath"]+"?content-id=" + row["ID"].ToString() + "&component-type=anasayfaustslide";
                    row["AJANS_URL"] = ConfigurationManager.AppSettings["AjansDisplayPagePath"] + "?content-id=" + row["ID"].ToString() + "&component-type=anasayfaustslide";
                });
            return dt;
        }

        public static string GetNextSlideNo(int slideType)
        {
            string sql = @"select * from (select X.ORDER_NO from DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE X
where X.DELETED='0' and  X.MENU_NAME_ID=:slideType and X.ACTIVE='1'  order by X.ORDER_NO desc) where RowNUm=1";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, new OracleParameter[] {
                new OracleParameter("slideType",slideType)
            });
            if (dt.Rows.Count > 0)
            {
                int lastNo = ConvertionHelper.ConvertValue<int>(dt.Rows[0]["ORDER_NO"].ToString());
                return (lastNo + 1).ToString();
            }
            else
                return "1";
        }
    }
}
