﻿using CoreHelpers;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.DigiportAdmin
{
    public class IndirimFirsatiHelper
    {
        public static DataTable IndirimFirsatiKategoriTabloGetir(bool isEnglish, int aktifDurum)
        {
            string sql = @"SELECT 
ID, KATEGORI_ADI,KATEGORI_ADI_EN, AKTIF
FROM DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_KAT
where 
(:aktifDurum=2 or (:aktifDurum=1 and AKTIF='1') or (:aktifDurum=0 and AKTIF='0'))
 order by case :isEnglish when '1' then KATEGORI_ADI_EN else KATEGORI_ADI end";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, new OracleParameter[] {
                new OracleParameter("isEnglish",isEnglish?"1":"0"),
                new OracleParameter("aktifDurum",aktifDurum)
            });
        }

        public static DataTable IndirimFirsatiTabloGetir(bool isEnglish, int aktifDurum)
        {
            bool isDebugMode = System.Configuration.ConfigurationManager.AppSettings["debugMode"] == "true";
            string url = isDebugMode ? ConfigurationManager.AppSettings["DigiflowTestDomain"] : ConfigurationManager.AppSettings["DigiflowLiveDomain"];
            string sql = @"select
X.ID,
X.BASLIK,
decode(:isEnglish,'1',KAT.KATEGORI_ADI_EN,'0',KAT.KATEGORI_ADI) as KATEGORI,
X.BASLANGIC_TARIHI,
X.BITIS_TARIHI,
X.ACTIVE,
X.ORDER_NO,
X.CLICK_ACTION,
NVL(X.TARGET_LINK,'') as TARGET_LINK,
NVL(X.POPUP_WIDTH,0) as POPUP_WIDTH,
NVL(X.POPUP_HEIGHT,0) as POPUP_HEIGHT,
NVL(X.HTML_ICERIK,'') as TARGET_CONTENT,
'' as DIGIFLOW_URL,
'' as DIGIPORT_URL,
'' as AJANS_URL

from DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_FIRST X
inner join DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_KAT KAT on KAT.ID=X.KATEGORI
where X.DELETED='0' and (:aktifDurum=2 or (:aktifDurum=1 and X.ACTIVE='1') or (:aktifDurum=0 and X.ACTIVE='0'))
order by X.ORDER_NO,X.BASLANGIC_TARIHI desc,X.BITIS_TARIHI desc";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, new OracleParameter[] {
                new OracleParameter("isEnglish",isEnglish?"1":"0"),
                new OracleParameter("aktifDurum",aktifDurum)
            });
            dt.Columns["DIGIFLOW_URL"].ReadOnly = false;
            dt.Columns["DIGIFLOW_URL"].MaxLength = Int32.MaxValue;
            dt.Columns["DIGIPORT_URL"].ReadOnly = false;
            dt.Columns["DIGIPORT_URL"].MaxLength = Int32.MaxValue;
            dt.Columns["AJANS_URL"].ReadOnly = false;
            dt.Columns["AJANS_URL"].MaxLength = Int32.MaxValue;
            dt.Rows.Cast<DataRow>().ToList().ForEach(
                row =>
                {
                    row["DIGIFLOW_URL"] = url + "Digiport/DisplayContent.aspx?content-id=" + row["ID"].ToString() + "&component-type=discountopportunity";
                    row["DIGIPORT_URL"] = ConfigurationManager.AppSettings["DigiportDisplayPagePath"]+"?content-id=" + row["ID"].ToString() + "&component-type=discountopportunity";
                    row["AJANS_URL"] = ConfigurationManager.AppSettings["AjansDisplayPagePath"] + "?content-id=" + row["ID"].ToString() + "&component-type=discountopportunity";
                });
            return dt;
        }

        public static string GetNextOrderNo()
        {
            string sql = @"select * from (select X.ORDER_NO from DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_FIRST X
where X.DELETED='0' and X.ACTIVE='1'  order by X.ORDER_NO desc) where RowNUm=1";
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
            if (dt.Rows.Count > 0)
            {
                int lastNo = ConvertionHelper.ConvertValue<int>(dt.Rows[0]["ORDER_NO"].ToString());
                return (lastNo + 1).ToString();
            }
            else
                return "1";
        }
    }
}
