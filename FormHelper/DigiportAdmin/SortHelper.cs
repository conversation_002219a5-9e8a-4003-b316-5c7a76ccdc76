﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.DigiportAdmin
{
    public static class SortHelper
    {
        public static void SiraNoTekrarAyarla(List<ItemOrderInfo> list, decimal changeItemId, decimal newOrderNo, bool changedAsActive)
        {

            if (!changedAsActive)
            {
                decimal maxNo = list.OrderByDescending(x => x.ItemOrder_Old).First().ItemOrder_Old;
                newOrderNo = newOrderNo > maxNo ? maxNo : newOrderNo;
                decimal changedItemOldOrderNo = list.First(x => x.ItemId == changeItemId).ItemOrder_Old;
                bool upperMove = newOrderNo > changedItemOldOrderNo;
                foreach (var item in list.OrderBy(x => x.ItemOrder_Old).ToList())
                {
                    if (upperMove && item.ItemOrder_Old < changedItemOldOrderNo)
                        continue;
                    else if (!upperMove && item.ItemOrder_Old < newOrderNo)
                        continue;
                    else if (item.ItemId == changeItemId && item.ItemOrder_Old != newOrderNo)
                    {
                        item.ItemOrder_New = newOrderNo;
                        item.Changed = true;
                    }
                    else if (upperMove && item.ItemOrder_Old > changedItemOldOrderNo && item.ItemOrder_Old <= newOrderNo)
                    {
                        item.ItemOrder_New = item.ItemOrder_Old - 1;
                        item.Changed = true;
                    }
                    else if (!upperMove && item.ItemOrder_Old < changedItemOldOrderNo && item.ItemOrder_Old >= newOrderNo)
                    {
                        item.ItemOrder_New = item.ItemOrder_Old + 1;
                        item.Changed = true;
                    }
                }
            }
            else
            {
                int siraNo = 1;
                foreach (var item in list.OrderBy(x => x.ItemId == changeItemId ? 1 : 0).ThenBy(x => x.ItemOrder_Old).ToList())
                {
                    item.ItemOrder_New = siraNo;
                    if (item.ItemOrder_Old != item.ItemOrder_New)
                        item.Changed = true;
                    siraNo++;
                }
            }
        }
    }
    public class ItemOrderInfo
    {
        public decimal ItemId { get; set; }
        public decimal ItemOrder_Old { get; set; }
        public decimal ItemOrder_New { get; set; }
        public bool Changed { get; set; }
    }
}
