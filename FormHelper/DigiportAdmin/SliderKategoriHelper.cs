﻿using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.DigiportAdmin
{
    public class SliderKategoriHelper
    {
        public static DataTable KategoriTabloGetir(int tip, int aktifDurum, bool isEnglish)
        {
            string sql = @"SELECT 
ID, KATEGORI_ADI, KATEGORI_ADI_EN, 
   TIP, AKTIF,'' as TIP_ADI
FROM DT_WORKFLOW.DIGIPORT_ADMIN_SLIDER_KAT X
where (TIP=:TIP or :TIP='0') and (:aktifDurum=2 or (:aktifDurum=1 and AKTIF='1') or (:aktifDurum=0 and AKTIF='0'))
order by TIP,case :isEnglish when '1' then KATEGORI_ADI_EN else KATEGORI_ADI end";

            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, new OracleParameter[] {
                new OracleParameter("isEnglish",isEnglish?"1":"0"),
                new OracleParameter("TIP",tip.ToString()),
                new OracleParameter("aktifDurum",aktifDurum)
            });
            dt.Columns["TIP_ADI"].ReadOnly = false;
            dt.Columns["TIP_ADI"].MaxLength = Int32.MaxValue;
            foreach (DataRow row in dt.Rows)
            {
                row["TIP_ADI"] = KategoriTipAdiGetir(tip, isEnglish);
            }
            return dt;
        }

        public static int KategoriTipGetir(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return (int)SliderKategoriTip.IK_DUYURU;
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return (int)SliderKategoriTip.AJANS_DUYURU;
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return (int)SliderKategoriTip.EĞİTİM_DUYURU;
            return 0;
        }
        public static string KategoriTipAdiGetir(int tip, bool isEnglish)
        {
            string donen = string.Empty;
            if (isEnglish)
            {
                if (tip == ((int)SliderKategoriTip_En.HR_ANNOUNCEMENTS))
                    donen = SliderKategoriTip_En.HR_ANNOUNCEMENTS.ToString().Replace("_", " ");
                else if (tip == ((int)SliderKategoriTip_En.AGENCY_ANNOUNCEMENTS))
                    donen = SliderKategoriTip_En.AGENCY_ANNOUNCEMENTS.ToString().Replace("_", " ");
                else if (tip == ((int)SliderKategoriTip_En.EDUCATION_ANNOUNCEMENTS))
                    donen = SliderKategoriTip_En.EDUCATION_ANNOUNCEMENTS.ToString().Replace("_", " ");
            }
            else
            {
                if (tip == ((int)SliderKategoriTip.IK_DUYURU))
                    donen = SliderKategoriTip.IK_DUYURU.ToString().Replace("_", " ");
                else if (tip == ((int)SliderKategoriTip.AJANS_DUYURU))
                    donen = SliderKategoriTip.AJANS_DUYURU.ToString().Replace("_", " ");
                else if (tip == ((int)SliderKategoriTip.EĞİTİM_DUYURU))
                    donen = SliderKategoriTip.EĞİTİM_DUYURU.ToString().Replace("_", " ");
            }
            return donen;
        }
    }
}
