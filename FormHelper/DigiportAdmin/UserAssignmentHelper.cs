using CoreHelpers;
using Entities;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.DirectoryServices;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Configuration;

namespace FormHelper.DigiportAdmin
{
    public class UserAssignmentHelper
    {
        private static readonly string DC1 = ConfigurationManager.AppSettings["DC1"];
        private static readonly string DC2 = ConfigurationManager.AppSettings["DC2"];
        private static readonly string GroupsOU = ConfigurationManager.AppSettings["GroupsOU1"];
        private static readonly string AdPortalServices = ConfigurationManager.AppSettings["AdPortalServices"];
        private static readonly string AdPortalCCServices = ConfigurationManager.AppSettings["AdPortalCCServices"];

        #region Existing Methods

        public static DataTable GetNamesList()
        {
            string sql = @"
                SELECT ID, NAME
                  FROM (
                    SELECT 0 AS ID,
                           N'--Seçiniz--' AS NAME
                      FROM DUAL
                    UNION
                    SELECT ID,
                           NAME
                      FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME
                     WHERE AKTIF = '1'
                  )
                 ORDER BY NAME";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static DataTable GetUsersList()
        {
            return CoreHelper.GetKullaniciList();
        }

        public static DataTable GridListele()
        {
            // Updated to use DT_WORKFLOW.DP_HR_USERS instead of IS_GUCU.KULLANICI
            string SQL = @"SELECT UA.ID, T.TYPE_NAME, N.NAME,
                          CASE
                             WHEN UA.F_LOGIN_ID > 0 THEN (SELECT USERNAME FROM DT_WORKFLOW.DP_HR_USERS WHERE F_LOGIN_ID = UA.F_LOGIN_ID AND DELETED='N')
                             ELSE NULL
                          END AS USERNAME,
                          UA.DISPLAY_NAME, UA.AD_GROUP,
                          CASE WHEN UA.AKTIF = '1' THEN 'AKTİF' ELSE 'PASİF' END AS AKTIF_DURUM
                          FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
                          INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME N ON UA.NAME_ID = N.ID
                          INNER JOIN DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE T ON N.TYPE_ID = T.ID
                          ORDER BY T.TYPE_NAME, N.NAME, UA.DISPLAY_NAME ASC";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);
        }

        /// <summary>
        /// Gets user/group assignments for a specific Digiport Name (Page/Permission) ID,
        /// optionally filtered by a search term on the display name.
        /// </summary>
        /// <param name="nameId">The ID of the Digiport Name (Page/Permission).</param>
        /// <param name="searchTerm">The term to search for in the DISPLAY_NAME (case-insensitive).</param>
        /// <returns>DataTable containing assignments.</returns>
        public static DataTable GetAssignments(int nameId, string searchTerm = "") // Renamed for clarity to match caller
        {
            // Use StringBuilder for cleaner SQL construction
            var sqlBuilder = new StringBuilder();

            sqlBuilder.Append(@"
            SELECT
                UA.ID,
                UA.NAME_ID, -- Include NAME_ID if needed later
                UA.F_LOGIN_ID,
                UA.AD_GROUP,
                UA.DISPLAY_NAME,
                UA.AKTIF, -- Return the raw value ('1' or '0')
                UA.CREATED,
                UA.CREATED_BY,
                UA.LAST_UPDATED,
                UA.LAST_UPDATED_BY,
                CASE
                    WHEN UA.F_LOGIN_ID > 0 THEN 'Kullanıcı'
                    ELSE 'AD Grup'
                END AS ASSIGNMENT_TYPE -- Added for clarity in GridView binding
            FROM
                DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT UA
            WHERE
                UA.NAME_ID = :NAME_ID");

            // List to hold parameters
            var dbParams = new List<OracleParameter>
        {
            new OracleParameter("NAME_ID", OracleDbType.Int32) { Value = nameId }
        };

            // Conditionally add the search term filter using parameters
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                // Use UPPER for case-insensitive comparison if DB collation requires it
                sqlBuilder.Append(" AND UPPER(UA.DISPLAY_NAME) LIKE '%' || UPPER(:SEARCH_TERM) || '%'");
                dbParams.Add(new OracleParameter("SEARCH_TERM", OracleDbType.Varchar2) { Value = searchTerm });
            }

            // Always add ORDER BY at the end
            sqlBuilder.Append(" ORDER BY UA.DISPLAY_NAME ASC");

            // Execute the query
            // Ensure DataAccessLayer.DAL.GetDataTable_Oracle handles List<OracleParameter> or convert to array
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sqlBuilder.ToString(), dbParams.ToArray());
        }

        public static bool MukerrerKayitVarMi(int nameId, string username, int ID)
        {
            // This method is used for checking duplicates by username
            // Get the login ID for the username first
            decimal loginId = GetLoginIdByUsername(username);
            if (loginId <= 0) return false;

            return MukerrerKayitVarMi(nameId, loginId, ID);
        }

        public static DataTable GetAdGroupsList()
        {
            var dt = new DataTable();
            dt.Columns.Add("AD_GROUP", typeof(string));

            try
            {
                // Retrieve groups from Active Directory
                ArrayList groups = GetGroupsList();

                // Add each group to the DataTable
                foreach (string groupName in groups)
                {
                    DataRow row = dt.NewRow();
                    row["AD_GROUP"] = groupName;
                    dt.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in GetAdGroupsList: {ex.Message}");
            }

            return dt;
        }

        /// <summary>
        /// Checks if a user assignment already exists for the given name and login ID
        /// </summary>
        public static bool MukerrerKayitVarMi(int nameId, decimal loginId, int ID)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Checking for duplicate user assignment: NameID={nameId}, LoginID={loginId}, ID={ID}");

                string sql = @"SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                              WHERE NAME_ID=:NAME_ID AND F_LOGIN_ID=:LOGIN_ID AND AD_GROUP IS NULL AND (:ID=0 OR ID<>:ID)";

                OracleParameter[] dbParams = new OracleParameter[]
                {
                    new OracleParameter("NAME_ID", OracleDbType.Int32) { Value = nameId },
                    new OracleParameter("LOGIN_ID", OracleDbType.Decimal) { Value = loginId },
                    new OracleParameter("ID", OracleDbType.Int32) { Value = ID }
                };

                var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
                int count = Convert.ToInt32(dt.Rows[0][0]);

                System.Diagnostics.Debug.WriteLine($"Found {count} duplicate user assignments");
                return count > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in MukerrerKayitVarMi: {ex.Message}");
                // In case of error, assume there's a duplicate to be safe
                return true;
            }
        }

        /// <summary>
        /// Checks if an AD group assignment already exists for the given name and group
        /// </summary>
        public static bool MukerrerAdGroupKayitVarMi(int nameId, string adGroup, int ID)
        {
            try
            {
                if (string.IsNullOrEmpty(adGroup))
                {
                    System.Diagnostics.Debug.WriteLine("Empty AD group provided, cannot check for duplicates");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"Checking for duplicate AD group: NameID={nameId}, Group={adGroup}, ID={ID}");

                // First check the direct way for full safety
                string sql = @"SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                              WHERE NAME_ID=:NAME_ID AND AD_GROUP=:AD_GROUP AND (:ID=0 OR ID<>:ID)";

                OracleParameter[] dbParams = new OracleParameter[]
                {
                    new OracleParameter("NAME_ID", OracleDbType.Int32) { Value = nameId },
                    new OracleParameter("AD_GROUP", OracleDbType.Varchar2) { Value = adGroup },
                    new OracleParameter("ID", OracleDbType.Int32) { Value = ID }
                };

                var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
                int count = Convert.ToInt32(dt.Rows[0][0]);

                if (count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Found {count} duplicate AD group assignments");
                    return true;
                }

                // Also check if any assignment (including NULL F_LOGIN_ID) exists with this NAME_ID and AD_GROUP
                // This is necessary because unique constraint doesn't work with NULL values
                sql = @"SELECT COUNT(*) FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                       WHERE NAME_ID=:NAME_ID AND AD_GROUP=:AD_GROUP AND F_LOGIN_ID IS NULL AND (:ID=0 OR ID<>:ID)";

                var dt2 = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
                count = Convert.ToInt32(dt2.Rows[0][0]);

                System.Diagnostics.Debug.WriteLine($"Found {count} duplicate AD group assignments with NULL F_LOGIN_ID");
                return count > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in MukerrerAdGroupKayitVarMi: {ex.Message}");
                // In case of error, assume there's a duplicate to be safe
                return true;
            }
        }

        public static string GetDisplayNameByLogin(decimal loginId)
        {
            // Updated to use DT_WORKFLOW.DP_HR_USERS instead of IS_GUCU.KULLANICI
            string sql = "SELECT NAME_SURNAME FROM DT_WORKFLOW.DP_HR_USERS WHERE F_LOGIN_ID = :LOGIN_ID AND DELETED='N'";
            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("LOGIN_ID", loginId)
            };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
            return dt.Rows.Count > 0 ? Convert.ToString(dt.Rows[0]["NAME_SURNAME"]) : string.Empty;
        }

        public static decimal GetLoginIdByUsername(string username)
        {
            if (string.IsNullOrEmpty(username)) return 0;

            // Updated to use DT_WORKFLOW.DP_HR_USERS instead of IS_GUCU.KULLANICI
            string sql = "SELECT F_LOGIN_ID FROM DT_WORKFLOW.DP_HR_USERS WHERE USERNAME = :USERNAME AND DELETED='N'";
            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("USERNAME", username)
            };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
            return dt.Rows.Count > 0 ? Convert.ToDecimal(dt.Rows[0]["F_LOGIN_ID"]) : 0;
        }

        public static string GetUsernameByLogin(decimal loginId)
        {
            if (loginId <= 0) return string.Empty;

            // Updated to use DT_WORKFLOW.DP_HR_USERS instead of IS_GUCU.KULLANICI
            string sql = "SELECT USERNAME FROM DT_WORKFLOW.DP_HR_USERS WHERE F_LOGIN_ID = :LOGIN_ID AND DELETED='N'";
            OracleParameter[] dbParams = new OracleParameter[]
            {
                new OracleParameter("LOGIN_ID", loginId)
            };
            var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, dbParams);
            return dt.Rows.Count > 0 ? Convert.ToString(dt.Rows[0]["USERNAME"]) : string.Empty;
        }

        public static bool UpdateAssignmentStatus(int id, bool isActive)
        {
            try
            {
                string sql = @"UPDATE DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                             SET AKTIF = :AKTIF,
                                 LAST_UPDATED = SYSDATE,
                                 LAST_UPDATED_BY = :LAST_UPDATED_BY
                             WHERE ID = :ID";
                OracleParameter[] dbParams = new OracleParameter[]
                {
                    new OracleParameter("AKTIF", isActive ? "1" : "0"),
                    new OracleParameter("LAST_UPDATED_BY", CoreHelper.LoginId),
                    new OracleParameter("ID", id)
                };
                return Convert.ToInt32(DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", sql, dbParams)) > 0;
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in UpdateAssignmentStatus: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region AD Group Methods

        /// <summary>
        /// Gruba eklenebilecek tüm AD gruplarını getirir.
        /// </summary>
        public static DataTable GetAllAdGroups()
        {
            var dt = new DataTable();
            dt.Columns.Add("AD_GROUP", typeof(string));

            try
            {
                // Get the group list from Active Directory
                var groups = GetGroupsList();

                // Add each group to the DataTable
                foreach (string groupName in groups)
                {
                    dt.Rows.Add(groupName);
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in GetAllAdGroups: {ex.Message}");
            }

            return dt;
        }

        /// <summary>
        /// Active Directory'den grup listesini getirir.
        /// </summary>
        public static ArrayList GetGroupsList()
        {
            ArrayList groups = new ArrayList();
            try
            {
                DirectoryEntry entry = new DirectoryEntry($"LDAP://OU={GroupsOU},DC={DC1},DC={DC2}");
                DirectorySearcher search = new DirectorySearcher(entry);
                search.SearchScope = SearchScope.Subtree;
                search.Filter = "(&(objectClass=group)(objectCategory=group))";
                search.PropertiesToLoad.Add("cn");

                SearchResult result;
                SearchResultCollection resultCol = search.FindAll();
                if (resultCol != null)
                {
                    for (int i = 0; i < resultCol.Count; i++)
                    {
                        result = resultCol[i];
                        if (result.Properties.Contains("cn"))
                        {
                            groups.Add((String)result.Properties["cn"][0]);
                        }
                    }
                }

                // Sort the groups alphabetically
                groups.Sort();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetGroupsList: {ex.Message}");
            }

            return groups;
        }

        /// <summary>
        /// Active Directory'den grup listesini getirir with improved error handling for referrals
        /// </summary>
        public static ArrayList GetGroupsList(string domain)
        {
            ArrayList groups = new ArrayList();
            try
            {
                // Log the domain parameter
                System.Diagnostics.Debug.WriteLine($"Getting groups for domain: '{domain}'");

                // Set the domain controller and secondary DC correctly based on the domain
                string domainController, secondaryDC, groupsOU;

                if (!string.IsNullOrEmpty(domain) && domain.ToLower().Trim() == "digiturk cc")
                {
                    // For Digiturk CC domain
                    domainController = ConfigurationManager.AppSettings["OTHERDC"];  // DIGITURKCC
                    secondaryDC = "local";
                    groupsOU = ConfigurationManager.AppSettings["CCGroupsOU"] ?? "CC Groups";
                    System.Diagnostics.Debug.WriteLine($"Using CC domain: DC={domainController},DC={secondaryDC}, OU={groupsOU}");
                }
                else
                {
                    // Default domain (Digiturk)
                    domainController = ConfigurationManager.AppSettings["DC1"];      // DIGITURK
                    secondaryDC = ConfigurationManager.AppSettings["DC2"];           // LOCAL
                    groupsOU = ConfigurationManager.AppSettings["GroupsOU1"] ?? "Digiturk Groups";
                    System.Diagnostics.Debug.WriteLine($"Using primary domain: DC={domainController},DC={secondaryDC}, OU={groupsOU}");
                }

                // Try different LDAP paths
                bool success = false;

                // Try with the correct LDAP format
                string ldapPath = $"LDAP://{domainController}.{secondaryDC}";
                System.Diagnostics.Debug.WriteLine($"Trying simplified LDAP Path: {ldapPath}");
                success = TryGetGroupsFromPath(ldapPath, groups);

                // Try with Groups OU
                if (!success)
                {
                    ldapPath = $"LDAP://OU={groupsOU},DC={domainController},DC={secondaryDC}";
                    System.Diagnostics.Debug.WriteLine($"Trying with Groups OU Path: {ldapPath}");
                    success = TryGetGroupsFromPath(ldapPath, groups);
                }

                // Try with root domain path
                if (!success)
                {
                    ldapPath = $"LDAP://DC={domainController},DC={secondaryDC}";
                    System.Diagnostics.Debug.WriteLine($"Trying with root domain path: {ldapPath}");
                    success = TryGetGroupsFromPath(ldapPath, groups);
                }

                // If all attempts failed, try to get groups from AdPortal service
                if (!success || groups.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("All LDAP attempts failed. Trying AdPortal service...");
                    if (!string.IsNullOrEmpty(domain) && domain.ToLower().Trim() == "digiturk cc")
                    {
                        var serviceUrl = ConfigurationManager.AppSettings["AdPortalCCServices"];
                        System.Diagnostics.Debug.WriteLine($"Using CC AdPortal service: {serviceUrl}");
                        // Call AdPortal service to get groups
                        success = GetGroupsFromAdPortalService(serviceUrl, groups);
                    }
                    else
                    {
                        var serviceUrl = ConfigurationManager.AppSettings["AdPortalServices"];
                        System.Diagnostics.Debug.WriteLine($"Using primary AdPortal service: {serviceUrl}");
                        // Call AdPortal service to get groups
                        success = GetGroupsFromAdPortalService(serviceUrl, groups);
                    }
                }

                // If still no groups, add defaults
                if (!success || groups.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("All attempts failed. Adding default group list as fallback...");
                    if (!string.IsNullOrEmpty(domain) && domain.ToLower().Trim() == "digiturk cc")
                    {
                        // CC domain groups - get from database or hard-code based on known groups
                        AddCCDomainDefaultGroups(groups);
                    }
                    else
                    {
                        // Primary domain groups - get from database or hard-code based on known groups
                        AddPrimaryDomainDefaultGroups(groups);
                    }
                }

                // Sort the groups
                groups.Sort();
                System.Diagnostics.Debug.WriteLine($"Total groups found: {groups.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetGroupsList: {ex.Message}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);

                // Add some default groups for the domain
                if (!string.IsNullOrEmpty(domain) && domain.ToLower().Trim() == "digiturk cc")
                {
                    AddCCDomainDefaultGroups(groups);
                }
                else
                {
                    AddPrimaryDomainDefaultGroups(groups);
                }
            }

            return groups;
        }

        /// <summary>
        /// Try to get groups from a specific LDAP path
        /// </summary>
        private static bool TryGetGroupsFromPath(string ldapPath, ArrayList groups)
        {
            try
            {
                // Get credentials from web.config
                string username = ConfigurationManager.AppSettings["Web.Services.UserName"];
                string password = ConfigurationManager.AppSettings["Web.Services.Password"];
                string domain = ConfigurationManager.AppSettings["Web.Services.Domain"];

                DirectoryEntry entry;

                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    System.Diagnostics.Debug.WriteLine($"Using explicit credentials: {domain}\\{username}");
                    entry = new DirectoryEntry(ldapPath, $"{domain}\\{username}", password);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Using default credentials for AD connection");
                    entry = new DirectoryEntry(ldapPath);
                }

                // Configure proper authentication settings to handle referrals
                entry.AuthenticationType = AuthenticationTypes.Secure | AuthenticationTypes.Sealing | AuthenticationTypes.Signing;

                DirectorySearcher search = new DirectorySearcher(entry)
                {
                    SearchScope = SearchScope.Subtree,
                    Filter = "(&(objectClass=group)(objectCategory=group))",
                    ReferralChasing = ReferralChasingOption.All,
                    PageSize = 1000,
                    SizeLimit = 5000,
                    ClientTimeout = TimeSpan.FromSeconds(30)
                };
                search.PropertiesToLoad.Add("cn");

                System.Diagnostics.Debug.WriteLine("Executing AD search...");

                SearchResultCollection resultCol = search.FindAll();
                int resultCount = resultCol?.Count ?? 0;
                System.Diagnostics.Debug.WriteLine($"Search results count: {resultCount}");

                if (resultCol != null && resultCount > 0)
                {
                    for (int i = 0; i < resultCol.Count; i++)
                    {
                        SearchResult result = resultCol[i];
                        if (result.Properties.Contains("cn"))
                        {
                            string groupName = (String)result.Properties["cn"][0];
                            if (!groups.Contains(groupName))
                            {
                                groups.Add(groupName);
                                // Log a sample of groups
                                if (i % 10 == 0 || groups.Count < 5)
                                    System.Diagnostics.Debug.WriteLine($"Found group: {groupName}");
                            }
                        }
                    }

                    return true; // Success
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error with LDAP path {ldapPath}: {ex.Message}");
                if (ex.InnerException != null)
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }

            return false; // Failed
        }

        /// <summary>
        /// Get groups from AdPortal service
        /// </summary>
        private static bool GetGroupsFromAdPortalService(string serviceUrl, ArrayList groups)
        {
            if (string.IsNullOrEmpty(serviceUrl))
            {
                System.Diagnostics.Debug.WriteLine("AdPortalServices URL not configured in web.config");
                return false;
            }

            try
            {
                // Placeholder for actual web service call
                // In a production environment, you would use the actual service
                // This implementation should be replaced with actual service call

                System.Diagnostics.Debug.WriteLine("This is a placeholder for AdPortal service call");

                // Return false so we'll fall back to default groups
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calling AdPortal service: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Add default groups for CC domain as a fallback
        /// </summary>
        private static void AddCCDomainDefaultGroups(ArrayList groups)
        {
            // Add default CC domain groups that are known to exist
            string[] defaultGroups = {
                "CC_ADMIN",
                "CC_USERS",
                "CC_OPERATORS",
                "CC_SUPPORT",
                "CALL_CENTER_ADMINS",
                "CALL_CENTER_USERS",
                "CC_HELPDESK"
            };

            foreach (string group in defaultGroups)
            {
                if (!groups.Contains(group))
                {
                    groups.Add(group);
                }
            }

            System.Diagnostics.Debug.WriteLine($"Added {defaultGroups.Length} default CC domain groups");
        }

        /// <summary>
        /// Add default groups for primary domain as a fallback
        /// </summary>
        private static void AddPrimaryDomainDefaultGroups(ArrayList groups)
        {
            // Add default primary domain groups that are known to exist
            string[] defaultGroups = {
                "TECH CORP",
                "PDO STB&CA TEST",
                "TECH QA",
                "TECH QA TEST TOD 2",
                "IS MUHABERAT",
                "DIGITURK_ADMIN",
                "DIGITURK_USERS",
                "DT_HQ_MAIN",
                "AD Admins"
            };

            foreach (string group in defaultGroups)
            {
                if (!groups.Contains(group))
                {
                    groups.Add(group);
                }
            }

            System.Diagnostics.Debug.WriteLine($"Added {defaultGroups.Length} default primary domain groups");
        }

        public static DataTable GetAdGroupsList(string domain)
        {
            var dt = new DataTable();
            dt.Columns.Add("AD_GROUP", typeof(string));

            try
            {
                System.Diagnostics.Debug.WriteLine($"Getting groups for domain: '{domain}'");

                // Get the group list from Active Directory based on domain
                var groups = GetGroupsList(domain);
                System.Diagnostics.Debug.WriteLine($"Retrieved {groups.Count} groups from AD");

                // Add each group to the DataTable
                foreach (string groupName in groups)
                {
                    DataRow row = dt.NewRow();
                    row["AD_GROUP"] = groupName;
                    dt.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in GetAdGroupsList: {ex.Message}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);
            }

            return dt;
        }

        /// <summary>
        /// DirectoryEntry nesnesini getirir.
        /// </summary>
        private static DirectoryEntry GetDirectoryEntry(string objectClass, string objectName, string domain = "")
        {
            try
            {
                // Log the call
                System.Diagnostics.Debug.WriteLine($"GetDirectoryEntry for {objectClass} '{objectName}' in domain '{domain}'");

                // Determine which domain controller to use based on the domain parameter
                string domainController = DC1;
                string secondaryDC = DC2;

                if (!string.IsNullOrEmpty(domain) && domain.ToLower().Trim() == "digiturk cc")
                {
                    // For Digiturk CC domain
                    domainController = DC2;
                    secondaryDC = DC1;
                    System.Diagnostics.Debug.WriteLine($"Using CC domain: DC={domainController},DC={secondaryDC}");
                }

                // Construct proper LDAP path with domain controllers
                string ldapPath = $"LDAP://DC={domainController},DC={secondaryDC}";
                System.Diagnostics.Debug.WriteLine($"LDAP Path: {ldapPath}");

                DirectoryEntry entry = new DirectoryEntry(ldapPath);
                DirectorySearcher searcher = new DirectorySearcher(entry);

                // Set filter based on object class
                switch (objectClass)
                {
                    case "user":
                        searcher.Filter = $"(&(objectClass=user)(objectCategory=person)(|(cn={objectName})(displayName={objectName})(sAMAccountName={objectName})))";
                        break;
                    case "group":
                        searcher.Filter = $"(&(objectClass=group)(|(cn={objectName})(dn={objectName})))";
                        break;
                    case "computer":
                        searcher.Filter = $"(&(objectClass=computer)(|(cn={objectName})))";
                        break;
                }

                System.Diagnostics.Debug.WriteLine($"Search filter: {searcher.Filter}");

                SearchResult result = searcher.FindOne();
                if (result != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Found {objectClass} '{objectName}'");
                    DirectoryEntry directoryObject = result.GetDirectoryEntry();
                    entry.Close();
                    entry.Dispose();
                    searcher.Dispose();
                    return directoryObject;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Could not find {objectClass} '{objectName}'");
                }

                entry.Close();
                entry.Dispose();
                searcher.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetDirectoryEntry: {ex.Message}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);
            }

            return null;
        }

        /// <summary>
        /// Belirtilen AD grubunun üyelerini getirir.
        /// </summary>
        public static DataTable GetUsersInAdGroup(string groupName, string domain = "")
        {
            var result = new DataTable();
            result.Columns.Add("F_LOGIN_ID", typeof(decimal));
            result.Columns.Add("USERNAME", typeof(string));
            result.Columns.Add("NAME_SURNAME", typeof(string));
            result.Columns.Add("EMAIL", typeof(string));
            result.Columns.Add("DEPARTMENT", typeof(string));

            try
            {
                System.Diagnostics.Debug.WriteLine($"GetUsersInAdGroup: {groupName}, domain: {domain}");

                // Get the DirectoryEntry for the group with domain parameter
                DirectoryEntry group = GetDirectoryEntry("group", groupName, domain);
                if (group == null)
                {
                    System.Diagnostics.Debug.WriteLine($"Group not found: {groupName}");
                    return result;
                }

                System.Diagnostics.Debug.WriteLine("Group found, getting members...");

                // Get members of the group
                var members = (IEnumerable)group.Invoke("Members", null);
                foreach (object member in members)
                {
                    using (var userEntry = new DirectoryEntry(member))
                    {
                        if (userEntry.SchemaClassName != "user") continue;

                        var row = result.NewRow();
                        string userName = userEntry.Properties["sAMAccountName"][0]?.ToString();
                        row["USERNAME"] = userName;
                        row["F_LOGIN_ID"] = GetLoginIdByUsername(userName);

                        if (userEntry.Properties.Contains("displayName"))
                            row["NAME_SURNAME"] = userEntry.Properties["displayName"][0];
                        else if (userEntry.Properties.Contains("cn"))
                            row["NAME_SURNAME"] = userEntry.Properties["cn"][0];

                        if (userEntry.Properties.Contains("mail"))
                            row["EMAIL"] = userEntry.Properties["mail"][0];

                        if (userEntry.Properties.Contains("department"))
                            row["DEPARTMENT"] = userEntry.Properties["department"][0];

                        result.Rows.Add(row);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Found {result.Rows.Count} users in group {groupName}");

                // Sort by name
                if (result.Rows.Count > 0)
                {
                    result.DefaultView.Sort = "NAME_SURNAME ASC";
                    result = result.DefaultView.ToTable();
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in GetUsersInAdGroup: {ex.Message}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);
            }

            return result;
        }

        public static DataTable GetUsersFromAdPortalService(string domain = "")
        {
            var result = new DataTable();
            result.Columns.Add("F_LOGIN_ID", typeof(decimal));
            result.Columns.Add("USERNAME", typeof(string));
            result.Columns.Add("NAME_SURNAME", typeof(string));
            result.Columns.Add("EMAIL", typeof(string));
            result.Columns.Add("DEPARTMENT", typeof(string));

            try
            {
                // Determine which service URL to use
                string serviceUrl = AdPortalServices; // Default
                if (!string.IsNullOrEmpty(domain) && domain.ToLower().Trim() == "digiturk cc")
                {
                    serviceUrl = AdPortalCCServices;
                }

                System.Diagnostics.Debug.WriteLine($"Using AD Portal service URL: {serviceUrl}");

                if (string.IsNullOrEmpty(serviceUrl))
                {
                    System.Diagnostics.Debug.WriteLine("AdPortalServices URL not configured in web.config");
                    return result;
                }

                // Create the web service client - placeholder implementation
                // This would need to be implemented with the actual service client
                System.Diagnostics.Debug.WriteLine("This is a placeholder for AdPortal service call");

                // In a real implementation, you would call the appropriate service method
                // Example:
                // using (var client = new AdportalServiceClient.AdPortalServices())
                // {
                //     client.Url = serviceUrl;
                //     var users = client.GetUsersList();
                //     // ... process the results ...
                // }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetUsersFromAdPortalService: {ex.Message}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);
            }

            return result;
        }

        /// <summary>
        /// Bir kullanıcının AD gruplarını getirir
        /// </summary>
        public static DataTable GetUserAdGroups(string username)
        {
            var result = new DataTable();
            result.Columns.Add("GROUP_NAME", typeof(string));

            try
            {
                DirectoryEntry userEntry = GetDirectoryEntry("user", username);
                if (userEntry == null) return result;

                // Get group memberships from memberOf property
                if (userEntry.Properties.Contains("memberOf"))
                {
                    foreach (string groupDN in userEntry.Properties["memberOf"])
                    {
                        // Extract group name from DN
                        string groupName = groupDN.Substring(3, groupDN.IndexOf(",") - 3);
                        var row = result.NewRow();
                        row["GROUP_NAME"] = groupName;
                        result.Rows.Add(row);
                    }
                }

                // Sort by group name
                if (result.Rows.Count > 0)
                {
                    result.DefaultView.Sort = "GROUP_NAME ASC";
                    result = result.DefaultView.ToTable();
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in GetUserAdGroups: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Bir kullanıcıyı bir AD grubuna ekler
        /// </summary>
        public static bool AddUserToAdGroup(string username, string groupName)
        {
            try
            {
                DirectoryEntry user = GetDirectoryEntry("user", username);
                if (user == null) return false;

                DirectoryEntry group = GetDirectoryEntry("group", groupName);
                if (group == null) return false;

                // Add user to group
                group.Properties["member"].Add(user.Properties["distinguishedName"].Value);
                group.CommitChanges();
                return true;
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in AddUserToAdGroup: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Bir kullanıcıyı bir AD grubundan çıkarır
        /// </summary>
        public static bool RemoveUserFromAdGroup(string username, string groupName)
        {
            try
            {
                DirectoryEntry user = GetDirectoryEntry("user", username);
                if (user == null) return false;

                DirectoryEntry group = GetDirectoryEntry("group", groupName);
                if (group == null) return false;

                // Remove user from group
                string userDN = user.Properties["distinguishedName"].Value.ToString();
                group.Properties["member"].Remove(userDN);
                group.CommitChanges();
                return true;
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in RemoveUserFromAdGroup: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Bir AD grubunun mevcut olup olmadığını kontrol eder
        /// </summary>
        public static bool CheckAdGroupExists(string groupName)
        {
            try
            {
                DirectoryEntry group = GetDirectoryEntry("group", groupName);
                return group != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Bir kullanıcının belirli bir AD grubuna üye olup olmadığını kontrol eder
        /// </summary>
        public static bool IsUserMemberOfAdGroup(string username, string groupName)
        {
            try
            {
                DirectoryEntry user = GetDirectoryEntry("user", username);
                if (user == null) return false;

                DirectoryEntry group = GetDirectoryEntry("group", groupName);
                if (group == null) return false;

                // Check if user is member of group
                foreach (string memberDN in group.Properties["member"])
                {
                    if (memberDN.Equals(user.Properties["distinguishedName"].Value.ToString()))
                    {
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error in IsUserMemberOfAdGroup: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}