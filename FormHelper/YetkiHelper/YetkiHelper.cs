﻿using CoreHelpers;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Web;

namespace FormHelper.YetkiHelper
{
    public class YetkiHelper
    {
        public static bool AdminlikYetkisiVarmi(string logicalGroupName, long LoginId)
        {
            bool retValue = false;
            if (HttpContext.Current.Session["LoginId"] != null)
                LoginId = Convert.ToInt64(HttpContext.Current.Session["LoginId"]);

            long LogicalGroupId = ConvertionHelper.ConvertValue<int>(LogicalGroupIDBul(logicalGroupName));

            if (LogicalGroupHelper.IsExistLogicalGroup(LogicalGroupId, LoginId))
            {
                retValue = true;
            }
            return retValue;
        }
        public static string LogicalGroupIDBul(string logicalGroupName)
        {
            System.Xml.XmlDocument doc = XmlHelper.GetDocument(ConfigurationManager.AppSettings["LogicalGroupDefinition"]);
            foreach (XmlNode item in doc.SelectNodes("LogicalGroupList/LogicalGroup"))
            {
                if (XmlHelper.getNodeValue<string>("NAME", item) == logicalGroupName)
                {
                    return XmlHelper.getNodeValue<string>("ID", item);
                }
            }
            throw new Exception("", new Exception("Logical Group bulunamadı !" + logicalGroupName));
        }

        public static bool KullaniciAdminMi(decimal loginId)
        {
            try
            {
                // Get admin groups from web.config
                string techCorpGroup = ConfigurationManager.AppSettings["AdGroup_TechCorp"];

                // Get username for the loginId
                string username = null;
                var userDetails = FormHelper.CoreHelper.GetKullaniciByLoginId(loginId);
                if (userDetails != null)
                {
                    username = userDetails["USERNAME"]?.ToString();
                }

                if (string.IsNullOrEmpty(username))
                {
                    return false;
                }

                // Get user's AD groups
                List<string> userGroups = CoreHelpers.UserInformationHelper.KullaniciGruplari(username);

                // Check if user belongs to any admin group
                return userGroups != null &&
                       (userGroups.Contains(techCorpGroup));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error checking admin status: " + ex.Message);
                return false;
            }
        }

        public static string UserName()
        {
            // Check if we're impersonating and use the impersonated user's username instead of Windows identity
            if (HttpContext.Current != null &&
                HttpContext.Current.Session != null &&
                HttpContext.Current.Session["Username"] != null)
            {
                string sessionUsername = HttpContext.Current.Session["Username"].ToString().ToUpper();

                // Apply same character replacements
                sessionUsername = sessionUsername.Replace("İ", "I");
                sessionUsername = sessionUsername.Replace("Ö", "O");
                sessionUsername = sessionUsername.Replace("Ü", "U");

                return sessionUsername;
            }

            // Fall back to Windows identity if no session username
            string User = System.Web.HttpContext.Current.Request.LogonUserIdentity.Name.Replace("DIGITURK\\", "").Replace("DIGITURKCC\\", "").ToUpper();
            User = User.Replace("İ", "I");
            User = User.Replace("Ö", "O");
            User = User.Replace("Ü", "U");
            return User;
        }

        public List<string> gruplar()
        {
            List<string> kullaniciGruplari = null;
            try
            {
                // If we're impersonating, we need to consider the permissions differently
                if (HttpContext.Current != null &&
                    HttpContext.Current.Session != null &&
                    HttpContext.Current.Session["IsImpersonating"] != null &&
                    (bool)HttpContext.Current.Session["IsImpersonating"])
                {
                    // For impersonation, use session username to determine groups
                    string username = UserName();
                    kullaniciGruplari = CoreHelpers.UserInformationHelper.KullaniciGruplari(username);

                    // Store in session for faster access
                    HttpContext.Current.Session["KullaniciGruplari"] = kullaniciGruplari;
                }
                else
                {
                    // Normal user - check if we have cached groups
                    if (HttpContext.Current.Session != null &&
                        HttpContext.Current.Session["KullaniciGruplari"] != null)
                    {
                        kullaniciGruplari = HttpContext.Current.Session["KullaniciGruplari"] as List<string>;
                    }

                    // If not cached, fetch from Active Directory
                    if (kullaniciGruplari == null)
                    {
                        kullaniciGruplari = CoreHelpers.UserInformationHelper.KullaniciGruplari(UserName());

                        // Cache for future use
                        if (HttpContext.Current.Session != null)
                        {
                            HttpContext.Current.Session["KullaniciGruplari"] = kullaniciGruplari;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error getting user groups: " + ex.Message);
                System.Threading.Thread.Sleep(2000);

                // Try one more time
                kullaniciGruplari = CoreHelpers.UserInformationHelper.KullaniciGruplari(UserName());
            }

            return kullaniciGruplari;
        }
    }
}
