﻿using System;
using System.Data;
using System.Web;
using System.Diagnostics;

namespace AracTakipSistemi.Authentication
{
    /// <summary>
    /// Handles user authentication and impersonation
    /// </summary>
    public static class AuthenticationManager
    {
        private const string SESSION_USER_KEY = "__UserInfo";

        /// <summary>
        /// Authenticates the current user, considering impersonation if applicable
        /// </summary>
        public static bool AuthenticateUser(HttpContext context)
        {
            try
            {
                // Clear any existing session if switching users
                string loginIdParam = context.Request.QueryString["LoginId"];
                if (!string.IsNullOrEmpty(loginIdParam))
                {
                    // User switching detected - reset session data
                    ResetUserSession(context);
                }

                // Check if already authenticated
                if (context.Session["LoginId"] != null)
                {
                    Debug.WriteLine($"User already authenticated with LoginId: {context.Session["LoginId"]}");
                    return true;
                }

                // Get Windows identity
                string windowsIdentity = GetWindowsIdentity();
                if (string.IsNullOrEmpty(windowsIdentity))
                {
                    Debug.WriteLine("Could not determine Windows identity");
                    return false;
                }

                // Extract username
                string username = windowsIdentity.Contains("\\") ?
                    windowsIdentity.Split('\\')[1] : windowsIdentity;

                // Get debug mode
                bool isDebugMode = System.Configuration.ConfigurationManager.AppSettings["debugMode"] == "true";

                // Check if we're impersonating another user
                decimal impersonateLoginId = 0;
                if (!string.IsNullOrEmpty(loginIdParam) && decimal.TryParse(loginIdParam, out impersonateLoginId))
                {
                    // Determine if current user can impersonate
                    bool canImpersonate = IsUserAllowedToImpersonate(username, isDebugMode);

                    if (canImpersonate)
                    {
                        Debug.WriteLine($"User {username} is allowed to impersonate. Impersonating LoginId: {impersonateLoginId}");
                        return AuthenticateAsUser(context, impersonateLoginId, username, true);
                    }
                    else
                    {
                        Debug.WriteLine($"User {username} is NOT allowed to impersonate LoginId: {impersonateLoginId}");
                        // Continue with normal authentication
                    }
                }

                // Regular authentication using Windows identity
                decimal loginId = FormHelper.CoreHelper.GetKullaniciLoginIdDecimal(username);

                if (loginId > 0)
                {
                    Debug.WriteLine($"Found loginId {loginId} for username {username}");
                    return AuthenticateAsUser(context, loginId, username, false);
                }
                else
                {
                    // Try direct lookup in database with case-insensitive search
                    string sql = $"SELECT LOGIN_ID FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE UPPER(USERNAME) = UPPER('{username}')";
                    DataTable dtUser = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);

                    if (dtUser != null && dtUser.Rows.Count > 0)
                    {
                        loginId = Convert.ToDecimal(dtUser.Rows[0]["LOGIN_ID"]);
                        Debug.WriteLine($"Found loginId {loginId} via direct database lookup for username {username}");
                        return AuthenticateAsUser(context, loginId, username, false);
                    }
                }

                Debug.WriteLine($"Could not find user information for username {username}");
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in AuthenticateUser: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Authenticates as a specific user and sets up session
        /// </summary>
        private static bool AuthenticateAsUser(HttpContext context, decimal loginId, string originatingUsername, bool isImpersonating)
        {
            try
            {
                // Get user details
                DataRow userDetails = FormHelper.CoreHelper.GetKullaniciByLoginId(loginId);
                if (userDetails == null)
                {
                    Debug.WriteLine($"Could not find user details for loginId {loginId}");
                    return false;
                }

                // Set session variables with correct column names
                context.Session["LoginId"] = loginId;
                context.Session["Username"] = userDetails["USERNAME"]?.ToString();

                // Use NAME_SURNAME instead of AD_SOYAD for user's full name
                if (userDetails.Table.Columns.Contains("NAME_SURNAME"))
                    context.Session["UserFullName"] = userDetails["NAME_SURNAME"]?.ToString();

                // Use E_MAIL instead of EMAIL
                if (userDetails.Table.Columns.Contains("E_MAIL"))
                    context.Session["UserEmail"] = userDetails["E_MAIL"]?.ToString();

                // Use DEPT_NAME instead of DEPARTMENT
                if (userDetails.Table.Columns.Contains("DEPT_NAME"))
                    context.Session["UserDepartment"] = userDetails["DEPT_NAME"]?.ToString();

                // Use POZISYON instead of TITLE
                if (userDetails.Table.Columns.Contains("POZISYON"))
                    context.Session["UserTitle"] = userDetails["POZISYON"]?.ToString();
                else if (userDetails.Table.Columns.Contains("TITLE"))
                    context.Session["UserTitle"] = userDetails["TITLE"]?.ToString();

                // Handle impersonation info
                if (isImpersonating)
                {
                    // Store original Windows identity
                    context.Session["IsImpersonating"] = true;
                    context.Session["OriginalWindowsUsername"] = originatingUsername;

                    // Look up original user's login ID for reference
                    decimal originalLoginId = FormHelper.CoreHelper.GetKullaniciLoginIdDecimal(originatingUsername);
                    if (originalLoginId > 0)
                    {
                        DataRow originalUserDetails = FormHelper.CoreHelper.GetKullaniciByLoginId(originalLoginId);
                        if (originalUserDetails != null)
                        {
                            context.Session["OriginalLoginId"] = originalLoginId;
                            context.Session["OriginalUserName"] = originalUserDetails["USERNAME"]?.ToString();

                            // Use correct column name for original user's full name
                            if (originalUserDetails.Table.Columns.Contains("NAME_SURNAME"))
                                context.Session["OriginalUserFullName"] = originalUserDetails["NAME_SURNAME"]?.ToString();
                            else if (originalUserDetails.Table.Columns.Contains("AD_SOYAD"))
                                context.Session["OriginalUserFullName"] = originalUserDetails["AD_SOYAD"]?.ToString();
                        }
                    }
                }

                // Check if user is admin
                bool isAdmin = false;
                FormHelper.YetkiHelper.YetkiHelper yetkiHelper = new FormHelper.YetkiHelper.YetkiHelper();
                var userGroups = yetkiHelper.gruplar();

                if (userGroups != null && userGroups.Count > 0)
                {
                    isAdmin = userGroups.Contains(System.Configuration.ConfigurationManager.AppSettings["AdGroup_TechCorp"]);
                }

                context.Session["IsAdmin"] = isAdmin;

                // Set up in core helper for application-wide access
                FormHelper.CoreHelper.SetCurrentUser(loginId, context.Session["Username"].ToString());

                Debug.WriteLine($"Successfully authenticated as {context.Session["UserFullName"]} (LoginId: {loginId})");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in AuthenticateAsUser: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if user is allowed to impersonate other users
        /// </summary>
        private static bool IsUserAllowedToImpersonate(string username, bool isDebugMode)
        {
            // List of users allowed to impersonate
            string[] liveImpersonators = new string[] {
                "DTKBAYRAKTAR", "DTZKUCUK", "DIGIFLOW_SA", "SPSMOSS_SA", "DTBGUNAY", "DTUMKORKMAZ", "DTMKASAPOGLU", "DTYAELMAS"
            };

            string[] testImpersonators = new string[] {
                "DTKBAYRAKTAR", "DTZKUCUK", "DIGIFLOW_SA", "SPSMOSS_SA", "DTBGUNAY", "DTUMKORKMAZ", "DTMKASAPOGLU", "DTYAELMAS"
            };

            // Clean up username for comparison
            string cleanUsername = username.Replace("DT", "").ToUpper();

            // Check appropriate list based on environment
            string[] allowedUsers = isDebugMode ? testImpersonators : liveImpersonators;

            return Array.Exists(allowedUsers, u => cleanUsername.Contains(u));
        }

        /// <summary>
        /// Gets Windows identity using multiple methods
        /// </summary>
        public static string GetWindowsIdentity()
        {
            // Try multiple methods to get Windows identity
            try
            {
                if (HttpContext.Current?.Request?.LogonUserIdentity != null)
                {
                    return HttpContext.Current.Request.LogonUserIdentity.Name;
                }
            }
            catch { }

            try
            {
                return System.Security.Principal.WindowsIdentity.GetCurrent().Name;
            }
            catch { }

            try
            {
                return System.Threading.Thread.CurrentPrincipal.Identity.Name;
            }
            catch { }

            return null;
        }

        /// <summary>
        /// Resets user session data
        /// </summary>
        private static void ResetUserSession(HttpContext context)
        {
            try
            {
                // Preserve language settings
                var langSetting = context.Session["AdminYonetimSistemiSecilenDil" + context.Session.SessionID];

                // Clear relevant session variables
                context.Session.Remove("LoginId");
                context.Session.Remove("Username");
                context.Session.Remove("UserFullName");
                context.Session.Remove("UserEmail");
                context.Session.Remove("UserDepartment");
                context.Session.Remove("UserTitle");
                context.Session.Remove("IsAdmin");
                context.Session.Remove("IsImpersonating");
                context.Session.Remove("OriginalLoginId");
                context.Session.Remove("OriginalUserName");
                context.Session.Remove("OriginalUserFullName");
                context.Session.Remove("OriginalWindowsUsername");
                context.Session.Remove("UserInformation");
                context.Session.Remove("FormYetkileri");
                context.Session.Remove("KullaniciYetkiGrupIdleri");
                context.Session.Remove(SESSION_USER_KEY);

                // Restore language settings
                if (langSetting != null)
                {
                    context.Session["AdminYonetimSistemiSecilenDil" + context.Session.SessionID] = langSetting;
                }

                Debug.WriteLine("User session data reset");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error resetting user session: {ex.Message}");
            }
        }
    }
}