﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FormHelper.YetkiHelper
{
    /// <summary>
    /// Holds user authentication state and user information
    /// </summary>
    public class AuthenticationResult
    {
        private static readonly string[] LiveAdminUsers = new string[] {
            "DTKBAYRAKTAR", "DTZKUCUK", "DIGIFLOW_SA", "SPSMOSS_SA", "DTBGUNAY", "DTUMKORKMAZ", "DTMKASAPOGLU", "DTYAELMAS"
        };

        private static readonly string[] TestAdminUsers = new string[] {
            "DTKBAYRAKTAR", "DTZKUCUK", "DIGIFLOW_SA", "SPSMOSS_SA", "DTBGUNAY", "DTUMKORKMAZ", "DTMKASAPOGLU", "DTYAELMAS"
        };

        /// <summary>
        /// Creates a new authentication result
        /// </summary>
        public AuthenticationResult(bool isDebugMode, decimal loginId, string windowsUsername)
        {
            try
            {
                // Clean up Windows username if needed
                string username = windowsUsername;
                if (username.Contains("\\"))
                {
                    username = username.Split('\\')[1];
                }

                // Check if user is allowed to impersonate in the current environment
                string[] allowedUsers = isDebugMode ? TestAdminUsers : LiveAdminUsers;
                bool canImpersonate = Array.Exists(allowedUsers, user =>
                    username.Equals(user, StringComparison.OrdinalIgnoreCase));

                // If trying to impersonate but not allowed, reset login ID
                if (loginId > 0 && !canImpersonate)
                {
                    loginId = 0;
                }

                // If we have a login ID, use it (impersonation). Otherwise, look up by Windows username
                if (loginId > 0)
                {
                    // Look up user by specified login ID (for impersonation)
                    LoginObject = GetUserByLoginId(loginId);
                    if (LoginObject != null)
                    {
                        // Store original user information if this is impersonation
                        OriginalWindowsUsername = windowsUsername;
                        IsImpersonating = true;
                    }
                }
                else
                {
                    // Look up user by Windows username
                    LoginObject = GetUserByWindowsUsername(username);
                }

                // Set authentication state based on whether we found a user
                if (LoginObject != null)
                {
                    IsLogin = true;
                    UserFullName = LoginObject.NameSurname;
                    Username = LoginObject.Username;

                    // Get additional user information
                    UserGroups = GetUserGroups(LoginObject.LoginId);
                    IsAdmin = DetermineIfAdmin(UserGroups);
                }
                else
                {
                    IsLogin = false;
                }
            }
            catch (Exception ex)
            {
                IsLogin = false;
                ErrorMessage = ex.Message;
                System.Diagnostics.Debug.WriteLine($"Error in AuthenticationResult: {ex.Message}");
            }
        }

        // Authentication state properties
        public bool IsLogin { get; set; }
        public bool IsImpersonating { get; set; }
        public string OriginalWindowsUsername { get; set; }
        public string ErrorMessage { get; set; }

        // User information properties
        public UserInfo LoginObject { get; set; }
        public string UserFullName { get; set; }
        public string Username { get; set; }
        public bool IsAdmin { get; set; }
        public List<string> UserGroups { get; set; }

        #region Helper Methods

        private UserInfo GetUserByLoginId(decimal loginId)
        {
            try
            {
                System.Data.DataRow userRow = FormHelper.CoreHelper.GetKullaniciByLoginId(loginId);
                if (userRow != null)
                {
                    return new UserInfo
                    {
                        LoginId = loginId,
                        Username = userRow["USERNAME"]?.ToString(),
                        NameSurname = userRow["AD_SOYAD"]?.ToString(),
                        Email = userRow.Table.Columns.Contains("EMAIL") ? userRow["EMAIL"]?.ToString() : null,
                        Department = userRow.Table.Columns.Contains("DEPARTMENT") ? userRow["DEPARTMENT"]?.ToString() : null,
                        Title = userRow.Table.Columns.Contains("TITLE") ? userRow["TITLE"]?.ToString() : null,
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetUserByLoginId: {ex.Message}");
                return null;
            }
        }

        private UserInfo GetUserByWindowsUsername(string username)
        {
            try
            {
                // Try to get LoginId using CoreHelper
                decimal loginId = FormHelper.CoreHelper.GetKullaniciLoginIdDecimal(username);

                if (loginId > 0)
                {
                    return GetUserByLoginId(loginId);
                }

                // If not found with direct lookup, try case-insensitive lookup in the database
                string sql = $"SELECT LOGIN_ID, USERNAME, AD_SOYAD, EMAIL, DEPARTMENT, TITLE FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE UPPER(USERNAME) = UPPER('{username}')";
                System.Data.DataTable dtUser = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);

                if (dtUser != null && dtUser.Rows.Count > 0)
                {
                    System.Data.DataRow row = dtUser.Rows[0];
                    return new UserInfo
                    {
                        LoginId = Convert.ToDecimal(row["LOGIN_ID"]),
                        Username = row["USERNAME"]?.ToString(),
                        NameSurname = row["AD_SOYAD"]?.ToString(),
                        Email = row.Table.Columns.Contains("EMAIL") ? row["EMAIL"]?.ToString() : null,
                        Department = row.Table.Columns.Contains("DEPARTMENT") ? row["DEPARTMENT"]?.ToString() : null,
                        Title = row.Table.Columns.Contains("TITLE") ? row["TITLE"]?.ToString() : null,
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetUserByWindowsUsername: {ex.Message}");
                return null;
            }
        }

        private List<string> GetUserGroups(decimal loginId)
        {
            try
            {
                FormHelper.YetkiHelper.YetkiHelper yetkiHelper = new FormHelper.YetkiHelper.YetkiHelper();
                return yetkiHelper.gruplar();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetUserGroups: {ex.Message}");
                return new List<string>();
            }
        }

        private bool DetermineIfAdmin(List<string> userGroups)
        {
            try
            {
                if (userGroups == null || userGroups.Count == 0)
                {
                    return false;
                }

                // Check if user is in admin groups
                bool isAdmin = userGroups.Contains(System.Configuration.ConfigurationManager.AppSettings["AdGroup_TechCorp"]);

                return isAdmin;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DetermineIfAdmin: {ex.Message}");
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// User information container
    /// </summary>
    public class UserInfo
    {
        public decimal LoginId { get; set; }
        public string Username { get; set; }
        public string NameSurname { get; set; }
        public string Email { get; set; }
        public string Department { get; set; }
        public string Title { get; set; }
    }
}
