﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Linq;
using System.Text;
using System.Configuration;
using Oracle.DataAccess.Client;


namespace FormHelper.EkipmanTalepFormu
{
    public class MalzemeTanimHelper
    {
        public static DataTable GetGridDataFromDBS()
        {
            string sql = @"SELECT * FROM VW_EKIPMAN_MALZEME_TANIM";
            return DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
        }

        public static void MalzemeEkle(string malzemeId)
        {
            MalzemeSil(malzemeId);

            string Insert_SQL = @"INSERT INTO DF_EKIPMAN_MALZEME_TANIM (MALZEME_ID) values (:MALZEME_ID)";           
            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("MALZEME_ID", malzemeId) };
            DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", Insert_SQL, CustomList);
            


        }

        public static void MalzemeSil(string malzemeId)
        {
            string delete_Sql = @"Delete From  DF_EKIPMAN_MALZEME_TANIM where MALZEME_ID=:MALZEME_ID";
            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("MALZEME_ID", malzemeId) };
            DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", delete_Sql, CustomList);


        }


    }
}
