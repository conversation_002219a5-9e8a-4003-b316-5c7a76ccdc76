﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.DataAccess.Client;
using System.Configuration;

namespace FormHelper.EkipmanTalepFormu
{
    public class YtsTanimlamaHelper
    {
        public static DataTable GetYtsFromDBS()
        {
            string db_link = "";
            if (ConfigurationManager.AppSettings["debugMode"].ToString() == "true")
            {
                db_link = "FLOWDEV";
            }
            else
            {
                db_link = "DSSFLOW";
            }
            string sql = @"SELECT TS_BAYI.*,
                        CASE WHEN DT_YTS.YTS_ID IS NOT NULL THEN 'AKTIF' ELSE 'PASIF' END AS ADMIN_DURUM,
                        CASE WHEN DT_YTS.YTS_ID IS NOT NULL THEN 'PASIFLESTIR' ELSE 'AKTIFLESTIR' END AS COMMAND 
                        FROM DT_WORKFLOW.DF_EKIPMAN_YTS@:DB DT_YTS RIGHT JOIN
                        VW_EKIPMAN_TALEP_ADMIN_BAYILER TS_BAYI 
                        ON TS_BAYI.ID = DT_YTS.YTS_ID ";
            sql = sql.Replace(":DB", db_link);
            return DataAccessLayer.DAL.GetDataTable_Oracle("DBSConnection", sql);

        }

        public static void YTS_Ekle(string ytsId)
        {
            YTS_Sil(ytsId);
            string Insert_SQL = @"INSERT INTO DT_WORKFLOW.DF_EKIPMAN_YTS (YTS_ID) values (:YTS_ID)";
            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("YTS_ID", ytsId) };
            DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", Insert_SQL, CustomList);
        }

        public static void YTS_Sil(string ytsId)
        {
            string delete_Sql = @"Delete From DT_WORKFLOW.DF_EKIPMAN_YTS where YTS_ID=:YTS_ID";
            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("YTS_ID", ytsId) };
            DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", delete_Sql, CustomList);


        }

        public static DataTable StokUygInaktifeCekilmisMi()
        {
            //string db_link = "";
            //if (ConfigurationManager.AppSettings["debugMode"].ToString() == "true")
            //{
            //    db_link = "KURUMSAL";
            //}
            //else
            //{
            //    db_link = "DBSLIVE";
            //}
            string sorgu = @"SELECT SALES_AGENT_CODE, UNVAN, DURUM_KODU
                      FROM VW_EKIPMAN_TALEP_ADMIN_BAYILER@DBS_LNK
                     WHERE DURUM_KODU = 3
                           AND SALES_AGENT_CODE IN(SELECT TS_BAYI.SALES_AGENT_CODE
                                  FROM DT_WORKFLOW.DF_EKIPMAN_YTS DT_YTS
                                       INNER JOIN
                                       VW_EKIPMAN_TALEP_ADMIN_BAYILER@DBS_LNK TS_BAYI
                                          ON TS_BAYI.ID = DT_YTS.YTS_ID --ADMIN EKRANLARDA AKTIF OLANLAR
                                                                       )";
            //sorgu = sorgu.Replace(":DB", db_link);            
            DataTable dtb =  DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sorgu);            
            return dtb;
        }
    }
}
