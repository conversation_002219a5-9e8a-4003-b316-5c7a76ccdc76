﻿using CoreHelpers;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;
using System.Web.Configuration;
using System.Web.UI;

namespace FormHelper.EkipmanTalepFormu
{
    public class EkipmanTalepleriRaporuHelper
    {
        public static DataTable GetMalzemeDropInfo()
        {
            string sql = "SELECT '--SEÇİNİZ--' AS EKIPMAN_SECIMI FROM DUAL UNION SELECT DISTINCT TO_CHAR(EKIPMAN_SECIMI) AS EKIPMAN_SECIMI FROM VW_EKIPMAN_TALEPLERI_RAPOR";
            DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
            return dtbsonuc;
        }

        public static DataTable GetBayiDropInfo()
        {           
            string sql = @"SELECT '--SEÇİNİZ--' AS YTS_ADI,'0' AS YTS_KODU,'--SEÇİNİZ--' AS YTS_ADI_2 FROM DUAL 
            UNION 
            SELECT DISTINCT TO_CHAR(YTS_ADI) AS YTS_ADI,
            YTS_KODU,
            TO_CHAR(YTS_KODU || '--' || YTS_ADI) AS YTS_ADI_2
            FROM VW_EKIPMAN_TALEPLERI_RAPOR             
            ORDER BY YTS_KODU ASC ";

            DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);
            return dtbsonuc;
        }


        public static DataTable GetMalzemeGridInfo(string baslangicTarihi, string bitisTarihi, string malzeme, string bayiKodu, string baslangicSaati, string bitisSaati)
        {

            string sql = @"SELECT RPR.WF_WORKFLOW_INSTANCE_ID AS AKIS_ID,
                   RPR.YTS_KODU AS TS_KODU,
                   RPR.YTS_ID,
                   DP.NAME_SURNAME,
                   RPR.DEPO_KODU,
                   TO_CHAR (RPR.CREATED, 'DD/MM/YYYY HH24:MI:SS') AS TARIH,
                   RPR.BOLGE,
                   RPR.IL,
                   RPR.YTS_ADI AS TS_ADI,
                   RPR.EKIPMAN_SECIMI AS MALZEME_ADI,
                   RPR.MALZEME_KODU,
                   RPR.MALZEME_ID,
                   RPR.EKIPMAN_ADET,
                   RPR.ACIKLAMA,
                   EXC.DEPO_ADET,
                   EXC.ORTALAMA
                   FROM VW_EKIPMAN_TALEPLERI_RAPOR RPR
                   JOIN DP_HR_USERS DP ON RPR.CREATED_BY = DP.F_LOGIN_ID
                   LEFT JOIN DF_EKIPMAN_EXCEL_DOSYA_VERI EXC ON RPR.MALZEME_ID = EXC.MALZEME_ID AND RPR.YTS_ID = EXC.BAYI_ID
                   and EXC.ID = (Select max(Id) from DF_EKIPMAN_EXCEL_DOSYA_VERI veri2 where VERI2.CREATED < RPR.CREATED AND veri2.MALZEME_ID = EXC.MALZEME_ID AND veri2.BAYI_ID = EXC.BAYI_ID)
                   WHERE 1 = 1";


            #region TARİHLERİN AYARLANMASI


            if (!String.IsNullOrEmpty(baslangicTarihi) && !String.IsNullOrEmpty(bitisTarihi)) //baslangıc tarıhı ve bitis tarihi ikisi de dolu oldugu zaman 
            {
                DateTime dateBaslangicTarihi = ConvertionHelper.ConvertValue<DateTime>(baslangicTarihi);
                string baslangicTarihiShortDate = dateBaslangicTarihi.ToShortDateString();
                DateTime dateBitisTarihi = ConvertionHelper.ConvertValue<DateTime>(bitisTarihi);
                string bitisTarihiShortDate = dateBitisTarihi.ToShortDateString();

                sql = sql + "AND RPR.CREATED BETWEEN TO_DATE(':BASLANGIC_TARIHI','DD.MM.YYYY HH24:MI:SS') AND TO_DATE(':BITIS_TARIHI','DD.MM.YYYY HH24:MI:SS')";
                sql = sql.Replace(":BASLANGIC_TARIHI", baslangicTarihiShortDate + " " + baslangicSaati);
                sql = sql.Replace(":BITIS_TARIHI", bitisTarihiShortDate + " " + bitisSaati);

            }
            else // tarih bos olamaz diye değişti
            {
                throw new ArgumentException();
            }

            #endregion TARİHLERİN AYARLANMASI


            if (!String.IsNullOrEmpty(malzeme) && malzeme != "--SEÇİNİZ--") // malzeme doluysa
            {
                sql = sql + " AND RPR.EKIPMAN_SECIMI =':EKIPMAN_SECIMI'";
                sql = sql.Replace(":EKIPMAN_SECIMI", malzeme);
            }

            if (!String.IsNullOrEmpty(bayiKodu) && bayiKodu != "0") // bayi doluysa
            {
                sql = sql + " AND RPR.YTS_KODU =':YTS_KODU'";
                sql = sql.Replace(":YTS_KODU", bayiKodu);
            }

            sql = sql + " ORDER BY RPR.CREATED DESC ";
            DataTable dtbsonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);

            for (int i = 0; i < dtbsonuc.Rows.Count; i++)
            {
                for (int j = 0; j < dtbsonuc.Columns.Count; j++)
                {
                    if (dtbsonuc.Columns[j].DataType == typeof(string) && !dtbsonuc.Columns[j].ReadOnly)
                    {
                        dtbsonuc.Rows[i][j] = dtbsonuc.Rows[i][j].ToString().Replace("\r", "").Replace("\n", "");
                    }
                    //if (dtbsonuc.Rows[i][j] is string)
                    //{
                    //    dtbsonuc.Rows[i][j] = dtbsonuc.Rows[i][j].ToString().Replace("\r", "").Replace("\n", "");
                    //}
                }
            }

            return dtbsonuc;
        }
               
    }
}
