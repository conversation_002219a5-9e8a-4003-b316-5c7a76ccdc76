﻿using CoreHelpers;
using Entities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Text;

namespace FormHelper.EkipmanTalepFormu
{
     public class ExcelYuklemeHelper
    {
        public static string InsertToExcelDosya(Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA nesnem)
        {
            Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA nesnem2 = new Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA();
            nesnem2.DOSYA_ADI_LINK = nesnem.DOSYA_ADI_LINK;
            nesnem2.CREATED_BY = nesnem.CREATED_BY;
            nesnem2.CREATED = nesnem.CREATED;
            return  PRepository<Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA>.EntityKaydet("DT_WORKFLOW", nesnem2); // yeni ID dönüyor.
        }

        public static string InsertToExcelDosyaVeri(Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA_VERI nesnem)
        {
            
            Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA_VERI nesnem2 = new Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA_VERI();
            nesnem2.DOSYA_ID = nesnem.DOSYA_ID;
            nesnem2.ORTALAMA = nesnem.ORTALAMA;
            nesnem2.DEPO_ADET = nesnem.DEPO_ADET;
            nesnem2.CREATED_BY = nesnem.CREATED_BY;
            nesnem2.CREATED = nesnem.CREATED;
            return PRepository<Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA_VERI>.EntityKaydet("DT_WORKFLOW", nesnem2); // yeni ID dönüyor.
        }
        public static string InsertToExcelDosyaVeri2(Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA_VERI nesnem)
        {
            return string.Format("insert into DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA_VERI (DOSYA_ID,BAYI_ID,MALZEME_ID,ORTALAMA,DEPO_ADET,CREATED_BY,CREATED) values ({0},{1},{2},{3},{4},'{5}',to_date('{6}','DD.MM.YYYY hh24:MI:SS'))", nesnem.DOSYA_ID,nesnem.BAYI_ID,nesnem.MALZEME_ID, nesnem.ORTALAMA.ToString().Replace(',', '.'), nesnem.DEPO_ADET, nesnem.CREATED_BY, nesnem.CREATED);

        }



        public static DataTable BayiKoduIsExist(string bayiKodu)
        {
            bool sonuc = false;
            string sql = @"SELECT TS_BAYI.*,
                        CASE WHEN DT_YTS.YTS_ID IS NOT NULL THEN 'AKTIF' ELSE 'PASIF' END AS ADMIN_DURUM,
                        CASE WHEN DT_YTS.YTS_ID IS NOT NULL THEN 'PASIFLESTIR' ELSE 'AKTIFLESTIR' END AS COMMAND 
                        FROM DT_WORKFLOW.DF_EKIPMAN_YTS DT_YTS RIGHT JOIN
                        VW_EKIPMAN_TALEP_ADMIN_BAYILER@DBS_LNK TS_BAYI 
                        ON TS_BAYI.ID = DT_YTS.YTS_ID
                        WHERE SALES_AGENT_CODE=:SALES_AGENT_CODE";

            //string db;

            //if (ConfigurationManager.AppSettings["debugMode"].ToString() == "true")
            //{
            //    db = "KURUMSAL";
            //}
            //else
            //{
            //    db = "DBSLIVE";
            //}

            //sql = sql.Replace(":DB", db);

            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("SALES_AGENT_CODE", bayiKodu) };                        
            DataTable dtbSonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, CustomList);
            return dtbSonuc;
        }     

        public static DataTable MalzemeKoduIsExist(string malzemeKodu)
        {
            string sql = @"SELECT STOCK_SPEC_ID,STOCK_SPEC_CD,NAME,RESOURCE_SPEC_TYPE_CD,STOK_UYG_DURUM,ADMIN_DURUM FROM DF_EKIPMAN_MALZEME_TANIM DF_MALZ RIGHT JOIN
                            VW_EKIPMAN_MALZEME_TANIM VW_MALZ 
                            ON VW_MALZ.STOCK_SPEC_ID = DF_MALZ.MALZEME_ID
                            WHERE STOCK_SPEC_CD=:STOCK_SPEC_CD";

            
            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("STOCK_SPEC_CD", malzemeKodu) };
            DataTable dtbSonuc = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql, CustomList);            
            return dtbSonuc;

        }

        public static bool IsConvertibleToDecimal(string deger)
        {
            try
            {
                decimal decimalValue = ConvertionHelper.ConvertValue<decimal>(deger);
                return true;
            }
            catch (Exception)
            {

                return false;
            }
        }

        public static bool IsNumeric(string deger)
        {
            int numericValue;
            bool isnumber = int.TryParse(deger, out numericValue);
            return isnumber;
        }

        public static void MalzemeAktiflestir(string malzemeKodu)
        {
            int malzemeId = GetMalzemeId(malzemeKodu);

            FormHelper.EkipmanTalepFormu.MalzemeTanimHelper.MalzemeSil(malzemeId.ToString());
            string Insert_SQL = @"INSERT INTO DF_EKIPMAN_MALZEME_TANIM (MALZEME_ID) values (:MALZEME_ID)";
            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("MALZEME_ID", malzemeId) };
            DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", Insert_SQL, CustomList);

        }

        public static void BayiAktiflestir(string bayiKodu)
        {
            int bayiId = GetBayiId(bayiKodu);

            FormHelper.EkipmanTalepFormu.YtsTanimlamaHelper.YTS_Sil(bayiId.ToString());
            string Insert_SQL = @"INSERT INTO DT_WORKFLOW.DF_EKIPMAN_YTS (YTS_ID) values (:YTS_ID)";
            OracleParameter[] CustomList = new OracleParameter[] { new OracleParameter("YTS_ID", bayiId) };
            DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", Insert_SQL, CustomList);           
        }

        public static int GetBayiId(string salesAgentCode)
        {
            string sql = string.Format(@"SELECT ID FROM VW_EKIPMAN_TALEP_ADMIN_BAYILER WHERE SALES_AGENT_CODE='{0}'",salesAgentCode);
            string sonuc = "";
            int bayiID = 0;
            sonuc = DataAccessLayer.DAL.GetQueryResult_Oracle("DBSConnection", sql);
            if (sonuc!=DataAccessLayer.DAL.kayit_yok)
            {
                bayiID = Convert.ToInt32(sonuc);
            }
            else
            {
                bayiID = 0;
            }
            
            return bayiID;
        }

        public static int GetMalzemeId(string stockSpecCd)
        {
            string sql = string.Format(@"SELECT STOCK_SPEC_ID FROM VW_EKIPMAN_MALZEME_TANIM WHERE STOCK_SPEC_CD='{0}'",stockSpecCd);
            int malzemeId = 0;
            string sonuc = "";
            sonuc = DataAccessLayer.DAL.GetQueryResult_Oracle("DT_WORKFLOW", sql);
            if (sonuc != DataAccessLayer.DAL.kayit_yok)
            {
                malzemeId = Convert.ToInt32(sonuc);
            }
            else
            {
                malzemeId = 0;
            }

            return malzemeId;
        }



    }
}
