<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="ClosePersonel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="OperationUser" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="CloseUserName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ClosePersonelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ClosePersonelResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetGroupOfUserList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GroupName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetGroupOfUserListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetGroupOfUserListResult" type="tns:ArrayOfUserInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfUserInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="UserInformation" nillable="true" type="tns:UserInformation" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="UserInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NameSurName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DisplayName" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetGroupList">
        <s:complexType />
      </s:element>
      <s:element name="GetGroupListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetGroupListResult" type="tns:ArrayOfGroupInformation" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfGroupInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="GroupInformation" nillable="true" type="tns:GroupInformation" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="GroupInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="GroupName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GroupEmail" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DisplayName" type="s:string" />
        </s:sequence>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="ClosePersonelSoapIn">
    <wsdl:part name="parameters" element="tns:ClosePersonel" />
  </wsdl:message>
  <wsdl:message name="ClosePersonelSoapOut">
    <wsdl:part name="parameters" element="tns:ClosePersonelResponse" />
  </wsdl:message>
  <wsdl:message name="GetGroupOfUserListSoapIn">
    <wsdl:part name="parameters" element="tns:GetGroupOfUserList" />
  </wsdl:message>
  <wsdl:message name="GetGroupOfUserListSoapOut">
    <wsdl:part name="parameters" element="tns:GetGroupOfUserListResponse" />
  </wsdl:message>
  <wsdl:message name="GetGroupListSoapIn">
    <wsdl:part name="parameters" element="tns:GetGroupList" />
  </wsdl:message>
  <wsdl:message name="GetGroupListSoapOut">
    <wsdl:part name="parameters" element="tns:GetGroupListResponse" />
  </wsdl:message>
  <wsdl:portType name="AdPortalServicesSoap">
    <wsdl:operation name="ClosePersonel">
      <wsdl:input message="tns:ClosePersonelSoapIn" />
      <wsdl:output message="tns:ClosePersonelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetGroupOfUserList">
      <wsdl:input message="tns:GetGroupOfUserListSoapIn" />
      <wsdl:output message="tns:GetGroupOfUserListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetGroupList">
      <wsdl:input message="tns:GetGroupListSoapIn" />
      <wsdl:output message="tns:GetGroupListSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="AdPortalServicesSoap" type="tns:AdPortalServicesSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="ClosePersonel">
      <soap:operation soapAction="http://tempuri.org/ClosePersonel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGroupOfUserList">
      <soap:operation soapAction="http://tempuri.org/GetGroupOfUserList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGroupList">
      <soap:operation soapAction="http://tempuri.org/GetGroupList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="AdPortalServicesSoap12" type="tns:AdPortalServicesSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="ClosePersonel">
      <soap12:operation soapAction="http://tempuri.org/ClosePersonel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGroupOfUserList">
      <soap12:operation soapAction="http://tempuri.org/GetGroupOfUserList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGroupList">
      <soap12:operation soapAction="http://tempuri.org/GetGroupList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="AdPortalServices">
    <wsdl:port name="AdPortalServicesSoap" binding="tns:AdPortalServicesSoap">
      <soap:address location="http://adportal/AdPortalServices.asmx" />
    </wsdl:port>
    <wsdl:port name="AdPortalServicesSoap12" binding="tns:AdPortalServicesSoap12">
      <soap12:address location="http://adportal/AdPortalServices.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>