yarn run v1.22.22
$ yarn lint && yarn type-check && vite build
$ eslint src --ext .ts,.tsx --fix && eslint *.ts *.js --fix

/Users/<USER>/Projects/digiflow/DigiflowReact/src/app/providers/AnalyticsProvider.tsx
  346:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  358:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  368:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  382:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  382:91  error    '_event' is defined but never used                                                                                              no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/app/providers/ErrorBoundaryProvider.tsx
   26:15  error  '_error' is defined but never used       no-unused-vars
   26:30  error  '_errorInfo' is defined but never used   no-unused-vars
   26:53  error  '_resetError' is defined but never used  no-unused-vars
   27:14  error  '_error' is defined but never used       no-unused-vars
   27:29  error  '_errorInfo' is defined but never used   no-unused-vars
  149:15  error  '_error' is defined but never used       no-unused-vars
  149:30  error  '_errorInfo' is defined but never used   no-unused-vars
  149:53  error  '_resetError' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/app/providers/FeatureFlagProvider.tsx
   30:15  error    '_key' is defined but never used                                                                                                no-unused-vars
   31:36  error    '_key' is defined but never used                                                                                                no-unused-vars
   31:50  error    '_defaultValue' is defined but never used                                                                                       no-unused-vars
   32:16  error    '_key' is defined but never used                                                                                                no-unused-vars
   33:13  error    '_key' is defined but never used                                                                                                no-unused-vars
   33:27  error    '_flag' is defined but never used                                                                                               no-unused-vars
   34:14  error    '_flags' is defined but never used                                                                                              no-unused-vars
   48:19  error    '_key' is defined but never used                                                                                                no-unused-vars
   48:33  error    '_flag' is defined but never used                                                                                               no-unused-vars
  294:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  307:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  318:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  328:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/app/providers/QueryProvider.tsx
   13:14  error    '_error' is defined but never used                                                                                              no-unused-vars
   14:16  error    '_data' is defined but never used                                                                                               no-unused-vars
   20:39  error    '_error' is defined but never used                                                                                              no-unused-vars
   20:78  error    '_data' is defined but never used                                                                                               no-unused-vars
   86:43  error    '_error' is defined but never used                                                                                              no-unused-vars
   86:82  error    '_data' is defined but never used                                                                                               no-unused-vars
  108:98  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  117:10  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  123:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  124:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  125:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  126:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  127:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  128:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  129:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  130:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  131:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  132:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  133:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  134:3   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  142:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  151:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  160:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  169:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/app/providers/ThemeProvider.tsx
   63:13  error    '_mode' is defined but never used                                                                                               no-unused-vars
   65:14  error    '_theme' is defined but never used                                                                                              no-unused-vars
  322:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  335:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/app/providers/index.tsx
   98:23   error    '_data' is defined but never used                                                                                               no-unused-vars
  115:28   error    '_key' is defined but never used                                                                                                no-unused-vars
  115:34   error    '_flag' is defined but never used                                                                                               no-unused-vars
  150:33   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  150:51   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  151:25   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  151:35   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  152:25   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  152:41   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  152:51   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  152:64   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  152:82   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  152:101  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  155:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  156:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  157:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  158:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  165:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  166:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  167:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  168:3    warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Error/__tests__/Error.test.tsx
   63:3   error  '_shouldError' is assigned a value but never used  no-unused-vars
  108:43  error  'error' is defined but never used                  no-unused-vars
  111:62  error  'error' is defined but never used                  no-unused-vars
  120:35  error  '_errorInfo' is defined but never used             no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Error/__tests__/ErrorBoundary.test.tsx
  12:21  error  '_message' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/LazyScreenWrapper/LazyScreenWrapper.tsx
   85:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  137:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Survey/AnketTextBox.tsx
  16:72  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiColumn/__tests__/DigiColumn.test.tsx
  530:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/DigiTable.test.tsx
  273:13  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/DigiTable.tsx
   39:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components                          react-refresh/only-export-components
   58:34  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   59:36  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   60:36  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   61:37  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   62:37  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   63:42  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   64:35  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   65:37  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   66:37  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   67:40  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   68:39  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   69:39  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   70:43  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   71:42  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   72:40  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   73:41  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
   91:53  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  104:18  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  145:84  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  153:63  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  190:17  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  273:37  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  281:93  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  311:36  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  363:8   warning  React Hook useMemo has a missing dependency: 'filterTable'. Either include it or remove the dependency array                                            react-hooks/exhaustive-deps
  378:32  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  381:30  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  393:8   warning  React Hook useCallback has a missing dependency: 'getFieldValue'. Either include it or remove the dependency array                                      react-hooks/exhaustive-deps
  398:43  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  399:32  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  434:31  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  443:25  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  463:33  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  465:48  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  468:34  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  487:39  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  488:36  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  573:33  warning  Unexpected any. Specify a different type                                                                                                                @typescript-eslint/no-explicit-any
  638:8   warning  React Hook useMemo has missing dependencies: 'getFieldValue', 'pageNumberOptions', and 'selection'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/MobileTable.tsx
   21:19  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   22:17  error    '_row' is defined but never used                                                                                no-unused-vars
   22:23  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   24:18  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   27:33  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   27:69  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   61:47  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   75:13  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   78:7   warning  React Hook useCallback has a missing dependency: 'mapFields'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
   83:13  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   86:7   warning  React Hook useCallback has a missing dependency: 'mapFields'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
   91:13  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   94:7   warning  React Hook useCallback has a missing dependency: 'mapFields'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  103:38  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  113:38  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  181:8   warning  React Hook useMemo has a missing dependency: 'columns'. Either include it or remove the dependency array        react-hooks/exhaustive-deps
  276:36  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  281:39  warning  Do not use Array index in keys                                                                                  react/no-array-index-key
  304:47  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  306:34  warning  Do not use Array index in keys                                                                                  react/no-array-index-key

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/TablePaginationWrapper.tsx
  9:40  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/__tests__/MobileTable.test.tsx
  672:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/__tests__/StatusBadge.test.tsx
  413:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/errors/ApiErrorBoundary.tsx
  58:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/errors/ErrorHandler.tsx
  55:0  error  Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/filters/FilterButton/FilterButton.tsx
  533:19  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/filters/FilterButton/__tests__/FilterButton.test.tsx
  532:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/filters/MobileFilterButton/MobileFilterButton.tsx
  460:11  error  Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/filters/MobileFilterButton/__tests__/MobileFilterButton.test.tsx
  682:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/filters/MultiSelectFilter/__tests__/MultiSelectFilter.test.tsx
  548:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/CharacterLimitContainer/CharacterLimitContainer.tsx
  12:15  error  '_e' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/DigiDatePicker/DigiDatePicker.tsx
  24:15  error  'date' is defined but never used   no-unused-vars
  44:24  error  'date' is defined but never used   no-unused-vars
  47:14  error  'error' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/DigiRadio/__tests__/DigiRadio.test.tsx
  274:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/DigiRadio/__tests__/DigiRadioGroup.test.tsx
  2:26  error  'fireEvent' is defined but never used  no-unused-vars
  2:26  error  'fireEvent' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/DigiTextField/DigiTextField.tsx
  182:8  error  Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/FileUpload/FileUpload.tsx
  347:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/LanguageSelect/LanguageSelect.tsx
  61:22  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/PartyListBox/PartyListBox.tsx
   9:19  error    '_party' is defined but never used  no-unused-vars
  12:17  error    '_party' is defined but never used  no-unused-vars
  38:29  warning  Do not use Array index in keys      react/no-array-index-key

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/SelectBox/SelectBox.tsx
  331:9  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/__tests__/CharacterLimitContainer.test.tsx
  212:6  error  Parsing error: Property assignment expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/__tests__/DigiDatePicker.test.tsx
  45:5  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/__tests__/DigiTextField.test.tsx
  328:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/__tests__/FileUpload.test.tsx
  10:17  error  'data' is defined but never used    no-unused-vars
  28:13  error  '_event' is defined but never used  no-unused-vars
  29:14  error  '_event' is defined but never used  no-unused-vars
  30:17  error  '_event' is defined but never used  no-unused-vars
  48:51  error  'btoa' is not defined               no-undef
  56:1   error  'global' is not defined             no-undef

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/__tests__/LanguageSelect.test.tsx
  241:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/__tests__/PartyListBox.test.tsx
  51:17  error  Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/formElements/__tests__/SelectBox.test.tsx
  33:82  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/layout/CustomTopbar/CustomTopbar.tsx
  32:14  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/layout/HeaderComponent/HeaderComponent.tsx
  12:83  warning  React Hook useMemo has an unnecessary dependency: 'window.location.search'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.search' aren't valid dependencies because mutating them doesn't re-render the component  react-hooks/exhaustive-deps
  16:37  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                        @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/layout/HeaderComponent/NavigateButtons.tsx
  193:20  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/layout/HeaderComponent/TopButtons.tsx
  71:0  error  Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/layout/HeaderComponent/__tests__/HeaderComponent.test.tsx
  505:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/layout/ScrollToTopBottom/ScrollToTopBottom.tsx
  80:3  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/ActionPanelComponent/ActionPanelComponent.tsx
  3:0  error  Parsing error: Identifier expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/DigiflowButtons.tsx
  70:0  error  Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/HistoryComponent/HistoryComponent.tsx
  53:0  error  Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/HistoryComponent/HistoryNotificationModal/HistoryNotificationModal.tsx
  36:34  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/OrganizationTree/OrganizationTree.tsx
  124:5  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/UnauthorizedComponent.tsx
  17:7  error  Parsing error: JSX element 'div' has no corresponding closing tag

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/WorkflowFormWrapper.tsx
   9:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  10:27  error    '_methods' is defined but never used      no-unused-vars
  10:51  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  11:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  13:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/WorkflowListSelector.tsx
  62:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/__tests__/ActionPanelComponent.test.tsx
  39:6  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/__tests__/OrganizationTree.test.tsx
  62:10  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/__tests__/WorkflowFormWrapper.test.tsx
  249:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/workflowComponents/__tests__/WorkflowTopInfoComponent.test.tsx
  209:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/configs/digiflow.config.tsx
  213:0  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/configs/workflowRegistry.tsx
  186:2  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/AccessibilityContext.tsx
  94:55  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/DigiflowContext.tsx
   9:22  error    '_isSysAdmin' is defined but never used                                                                                         no-unused-vars
  10:20  error    '_isMobile' is defined but never used                                                                                           no-unused-vars
  11:18  error    '_isTest' is defined but never used                                                                                             no-unused-vars
  12:22  error    '_user' is defined but never used                                                                                               no-unused-vars
  34:14  warning  Fast refresh only works when a file only exports components. Move your React context(s) to a separate file                      react-refresh/only-export-components
  36:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/ModalContext.tsx
   6:15  error    '_open' is defined but never used                                                                                               no-unused-vars
   7:21  error    '_processing' is defined but never used                                                                                         no-unused-vars
  19:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/SurveyContext.tsx
  43:17  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/WebViewContext.tsx
  123:23  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/WorkflowContext.tsx
  2:28   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  2:41   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  2:60   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  2:77   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  2:92   warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  2:109  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/__tests__/DigiflowContext.test.tsx
  248:44  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/__tests__/ModalContext.test.tsx
  143:11  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/__tests__/WebViewContext.test.tsx
  505:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/workflow/WorkflowActionsContext.tsx
  130:38  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/workflow/WorkflowAuthContext.tsx
  60:3  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/workflow/WorkflowConfigContext.tsx
   8:12  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  12:21  error    '_name' is defined but never used                                                                                               no-unused-vars
  13:21  error    '_id' is defined but never used                                                                                                 no-unused-vars
  14:18  error    '_id' is defined but never used                                                                                                 no-unused-vars
  15:17  error    '_readOnly' is defined but never used                                                                                           no-unused-vars
  20:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  32:20  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/workflow/WorkflowContext.test.tsx
  404:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/workflow/WorkflowDataContext.tsx
   6:48  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
   7:16  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
   8:55  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  11:20  error    '_attachments' is defined but never used                                                                                        no-unused-vars
  12:18  error    '_field' is defined but never used                                                                                              no-unused-vars
  12:34  error    '_value' is defined but never used                                                                                              no-unused-vars
  12:42  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  13:56  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  20:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  27:36  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  28:50  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  29:50  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  51:59  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  52:24  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/workflow/WorkflowProvider.tsx
  66:3  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/contexts/workflow/WorkflowUIContext.tsx
   9:18  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  10:16  error    '_loading' is defined but never used                                                                                            no-unused-vars
  11:19  error    'submitting' is defined but never used                                                                                          no-unused-vars
  12:14  error    'error' is defined but never used                                                                                               no-unused-vars
  13:23  error    'message' is defined but never used                                                                                             no-unused-vars
  15:32  error    'authData' is defined but never used                                                                                            no-unused-vars
  15:42  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any
  22:7   error    'WorkflowUIContext' is already defined                                                                                          no-redeclare
  24:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  40:61  warning  Unexpected any. Specify a different type                                                                                        @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/FileHooks/FileHooks.ts
  41:14  error  '_error' is defined but never used  no-unused-vars
  41:14  error  '_error' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/PermissionHooks/PermissionProcessHooks.ts
  6:2  error  Parsing error: Property or signature expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/WorkflowHooks/WorkflowHooks.ts
  50:30  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/WorkflowHooks/__tests__/useWorkflowFormSetup.test.ts
  164:4  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/WorkflowHooks/useWorkflowFormSetup.ts
  3:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useApiErrorHandler.test.ts
  555:6  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useAppInitialization.test.ts
  755:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useDebounce.test.ts
  125:4  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useEventListener.test.ts
  133:11  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useLocalStorage.test.ts
  155:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useMediaQuery.test.ts
  382:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useMobileAuth.test.ts
  259:11  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/__tests__/useWindowSize.test.ts
  133:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useApiErrorHandler.ts
  27:32  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useAppInitialization.ts
  43:34  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useAsync.ts
  40:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useEventListener.ts
  28:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useGetParams.ts
  8:14  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useInterval.ts
  23:3  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useLocalStorage.ts
  43:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useMainPageModifier.ts
  39:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useMediaQuery.ts
  16:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useMobileAuth.ts
  24:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useModal.ts
  22:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useNavigationHistory.ts
  115:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useOnClickOutside.ts
  17:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/usePerformanceMonitor.ts
  55:3  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/usePrevious.ts
  27:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/usePromise.ts
  42:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useQueryHook.ts
  60:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useResponsive.ts
  37:3  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useScript.ts
  41:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useSelectOption.ts
  24:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useSetStateWithPromise.ts
  18:58  error  'newState' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useUpdateEffect.ts
  15:6  warning  React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies                                                         react-hooks/exhaustive-deps
  15:6  warning  React Hook useEffect has a missing dependency: 'effect'. Either include it or remove the dependency array. If 'effect' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps

/Users/<USER>/Projects/digiflow/DigiflowReact/src/hooks/useWebViewModifier.ts
  8:27  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/layouts/WorkflowLayout.tsx
  22:14  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/schemas/workflows/validation/biFikrimVarSchema.ts
  125:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/schemas/workflows/validation/contractRequestSchema.ts
  78:44  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/schemas/workflows/validation/delegationSchema.ts
  98:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/schemas/workflows/validation/jumpToStateSchema.ts
  78:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/schemas/workflows/validation/malzemeCikisFormuSchema.ts
  81:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/schemas/workflows/validation/monitoringRequestSchema.ts
  55:2  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/schemas/workflows/validation/stationeryRequestSchema.ts
  51:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Error/UnauthorizedScreen.tsx
  6:14  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Error/__tests__/UnauthorizedScreen.test.tsx
  180:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/EndDelegationScreen.tsx
  108:4  error  Parsing error: Property assignment expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/EndMonitoringScreen.tsx
  54:4  error  Parsing error: Property assignment expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/HistoryScreen.tsx
  33:14  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/InboxScreen.tsx
  117:4  error  Parsing error: Property assignment expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/SuspendedWorkflowsScreen.tsx
  65:6  error  Parsing error: Property assignment expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/TestScreen.tsx
  194:0  error  Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/WorkflowManagementScreen.tsx
  38:34  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Header/__tests__/InboxScreen.test.tsx
  387:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/NotFound.tsx
  79:2  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Report/FlowAdminHistoryScreen.tsx
  84:34  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/BiFikrimVarScreen.tsx
  44:43  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/ContractRequestScreen.tsx
  511:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/DelegationScreen.tsx
  71:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/JumpToStateScreen.tsx
  29:5   error    'setDefinitionId' is not defined                                                                                                                                                                                                                    no-undef
  44:6   warning  React Hook useEffect has an unnecessary dependency: 'setDefinitionId'. Either exclude it or remove the dependency array. Outer scope values like 'setDefinitionId' aren't valid dependencies because mutating them doesn't re-render the component  react-hooks/exhaustive-deps
  44:20  error    'setDefinitionId' is not defined                                                                                                                                                                                                                    no-undef
  64:6   warning  React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array                                                                                                                                         react-hooks/exhaustive-deps
  71:36  warning  Do not use Array index in keys                                                                                                                                                                                                                      react/no-array-index-key

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/LeaveScreen.tsx
  433:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/MalzemeCikisFormu.tsx
  123:52  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/MonitoringScreen.tsx
  62:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/StationeryRequestScreen.tsx
  177:3  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/__tests__/BiFikrimVarScreen.test.tsx
  35:12  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/__tests__/ContractRequestScreen.test.tsx
  51:6  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/__tests__/DelegationScreen.test.tsx
  271:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/__tests__/LeaveScreen.test.tsx
  187:6  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/__tests__/MalzemeCikisFormu.test.tsx
  76:0  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/__tests__/MonitoringScreen.test.tsx
  290:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/Screens/__tests__/StationeryRequestScreen.test.tsx
  185:8  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/WorkflowSelectorScreen.tsx
  18:14  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/Workflow/__tests__/WorkflowLifecycle.integration.test.tsx
  83:9  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/screens/__tests__/NotFound.test.tsx
  479:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/FileService.ts
  18:17  error  Unnecessary escape character: \/                                            no-useless-escape
  49:7   error  Unexpected constant nullishness on the left-hand side of a `??` expression  no-constant-binary-expression
  54:37  error  Unnecessary escape character: \/                                            no-useless-escape

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/UserService.ts
  33:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/WorkflowService.test.ts
  122:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/WorkflowService.ts
  27:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  33:68  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  38:52  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  38:88  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  43:76  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/FileService.test.ts
  352:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/InboxServices.test.ts
  59:10  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/OrganizationService.test.ts
  443:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/UserService.test.ts
  671:6  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/WorkflowService.test.ts
  116:10  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/authService.test.ts
  413:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/authService.windows.test.ts
  101:11  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/csrfService.test.ts
  256:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/delegationServices.test.ts
  350:6  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/leaveServices.test.ts
  385:6  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/monitoringServices.test.ts
  277:8  error  Parsing error: Property assignment expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/secureAuthService.test.ts
  389:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/securityHeadersService.test.ts
  18:8  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/surveyService.test.ts
  242:7  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/__tests__/workflowOperations.test.ts
  69:1  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/authService.ts
  23:32  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/csrfService.ts
   22:36  warning  Unexpected any. Specify a different type                                    @typescript-eslint/no-explicit-any
   24:7   error    Unexpected constant nullishness on the left-hand side of a `??` expression  no-constant-binary-expression
   24:7   error    Unexpected constant nullishness on the left-hand side of a `??` expression  no-constant-binary-expression
  141:35  warning  Unexpected any. Specify a different type                                    @typescript-eslint/no-explicit-any
  141:49  warning  Unexpected any. Specify a different type                                    @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/secureTokenService.ts
  73:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/securityHeadersService.ts
  191:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error           no-console
  207:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error           no-console
  273:22  warning  Unexpected any. Specify a different type                                                    @typescript-eslint/no-explicit-any
  274:37  warning  Unexpected any. Specify a different type                                                    @typescript-eslint/no-explicit-any
  275:21  warning  Unexpected any. Specify a different type                                                    @typescript-eslint/no-explicit-any
  432:14  error    Prefer using an optional chain expression instead, as it's more concise and easier to read  @typescript-eslint/prefer-optional-chain

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/surveyService.ts
  19:67  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/wface/appHelper.tsx
  75:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/wface/appHooks.ts
  4:16  error  Parsing error: '(' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/wface/appWrapper.tsx
  4:24  error  Parsing error: '(' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/services/wface/authService.tsx
  20:6  warning  React Hook useCallback has an unnecessary dependency: 'storedValue'. Either exclude it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/Projects/digiflow/DigiflowReact/src/stores/__tests__/userStore.test.ts
  211:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/stores/userStore.ts
   7:21  error  'user' is defined but never used      no-unused-vars
  16:13  error  'userData' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/factories.ts
    4:48  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   16:52  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   29:56  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   43:57  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   61:53  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   76:56  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   88:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  102:66  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  112:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  124:56  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  136:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  154:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/i18n-test.ts
  5:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/mocks/handlers.ts
   24:33  error    'password' is not defined                                                                                  no-undef
   42:54  error    '_w' is defined but never used                                                                             no-unused-vars
   42:61  error    'w' is not defined                                                                                         no-undef
   62:59  warning  Unexpected any. Specify a different type                                                                   @typescript-eslint/no-explicit-any
   77:59  warning  Unexpected any. Specify a different type                                                                   @typescript-eslint/no-explicit-any
  222:59  warning  Unexpected any. Specify a different type                                                                   @typescript-eslint/no-explicit-any
  282:9   error    Unexpected constant nullishness on the left-hand side of a `??` expression                                 no-constant-binary-expression
  289:25  error    'btoa' is not defined                                                                                      no-undef
  289:93  error    Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator  @typescript-eslint/prefer-nullish-coalescing

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/mocks/windows-auth.ts
   15:12  error  'Buffer' is not defined                 no-undef
   33:12  error  'Buffer' is not defined                 no-undef
   37:60  error  '_challenge' is defined but never used  no-unused-vars
   47:12  error  'Buffer' is not defined                 no-undef
   69:25  error  'Buffer' is not defined                 no-undef
   78:13  error  '_n' is defined but never used          no-unused-vars
   78:20  error  'n' is not defined                      no-undef
   78:48  error  'n' is not defined                      no-undef
  151:23  error  'Buffer' is not defined                 no-undef
  172:20  error  'Buffer' is not defined                 no-undef
  182:16  error  '_error' is defined but never used      no-unused-vars
  182:16  error  '_error' is defined but never used      @typescript-eslint/no-unused-vars
  193:25  error  'Buffer' is not defined                 no-undef
  210:18  error  'Buffer' is not defined                 no-undef
  220:16  error  '_error' is defined but never used      no-unused-vars
  220:16  error  '_error' is defined but never used      @typescript-eslint/no-unused-vars
  238:34  error  'Buffer' is not defined                 no-undef
  253:14  error  '_error' is defined but never used      no-unused-vars
  253:14  error  '_error' is defined but never used      @typescript-eslint/no-unused-vars
  270:34  error  'Buffer' is not defined                 no-undef
  288:9   error  'reason' is not defined                 no-undef
  289:16  error  'Buffer' is not defined                 no-undef
  300:14  error  '_error' is defined but never used      no-unused-vars
  300:14  error  '_error' is defined but never used      @typescript-eslint/no-unused-vars
  315:34  error  'Buffer' is not defined                 no-undef
  322:24  error  'Buffer' is not defined                 no-undef
  335:14  error  '_error' is defined but never used      no-unused-vars
  335:14  error  '_error' is defined but never used      @typescript-eslint/no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/setup-minimal.tsx
    9:1   error    'vitest' import is duplicated             no-duplicate-imports
    9:10  error    'afterEach' is already defined            no-redeclare
   10:1   error    'vitest' import is duplicated             no-duplicate-imports
   10:10  error    'vi' is already defined                   no-redeclare
  133:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  155:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  156:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  157:42  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  160:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  161:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/setup.tsx
  115:31  error  Parsing error: Expression expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/test-env-loader.ts
   29:29  error  '__dirname' is not defined                                                  no-undef
  149:12  error  Unexpected constant nullishness on the left-hand side of a `??` expression  no-constant-binary-expression
  149:12  error  Unexpected constant nullishness on the left-hand side of a `??` expression  no-constant-binary-expression

/Users/<USER>/Projects/digiflow/DigiflowReact/src/test-utils/test-utils.tsx
  300:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/theme/designTokens.ts
  113:0  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/CommonTypes.ts
  25:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/FilterTypes.ts
   6:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  28:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  42:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  43:14  error    'value' is defined but never used         no-unused-vars
  43:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  44:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/InboxTypes.ts
  54:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  64:15  error    'value' is defined but never used         no-unused-vars
  64:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  70:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  72:15  error    'value' is defined but never used         no-unused-vars
  72:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/TableTypes.ts
  3:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/UserActionType.ts
   2:3  error  'STARTED' is defined but never used        no-unused-vars
   3:3  error  'ACCEPTED' is defined but never used       no-unused-vars
   4:3  error  'REJECTED' is defined but never used       no-unused-vars
   5:3  error  'SENDBACK' is defined but never used       no-unused-vars
   6:3  error  'ROLLBACK' is defined but never used       no-unused-vars
   7:3  error  'FORWARD' is defined but never used        no-unused-vars
   8:3  error  'SENDTOCOMMENT' is defined but never used  no-unused-vars
   9:3  error  'COMMENTED' is defined but never used      no-unused-vars
  10:3  error  'ADDTOCOMMEND' is defined but never used   no-unused-vars
  11:3  error  'CANCEL' is defined but never used         no-unused-vars
  12:3  error  'SUSPEND' is defined but never used        no-unused-vars
  13:3  error  'RESUME' is defined but never used         no-unused-vars
  14:3  error  'ASSIGN' is defined but never used         no-unused-vars
  15:3  error  'FLOWREJECTED' is defined but never used   no-unused-vars
  16:3  error  'FLOWACCEPTED' is defined but never used   no-unused-vars
  17:3  error  'SENDTASK' is defined but never used       no-unused-vars
  18:3  error  'CNDACCCEPT' is defined but never used     no-unused-vars
  19:3  error  'CORRECTION' is defined but never used     no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/WFaceTypes.ts
  127:0  error  Parsing error: Property or signature expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/WorkflowTypes.ts
  148:0  error  Parsing error: Property or signature expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/node-globals.d.ts
  11:10  error  'str' is defined but never used       no-unused-vars
  11:23  error  'encoding' is defined but never used  no-unused-vars
  12:11  error  'size' is defined but never used      no-unused-vars
  13:12  error  'buffers' is defined but never used   no-unused-vars
  17:16  error  'str' is defined but never used       no-unused-vars
  18:16  error  'str' is defined but never used       no-unused-vars
  21:14  error  'encoding' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/wface.d.ts
  223:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/types/workflow.ts
   6:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   7:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  17:17  error    'name' is defined but never used          no-unused-vars
  19:22  error    'name' is defined but never used          no-unused-vars
  19:36  error    'config' is defined but never used        no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/__tests__/helpers.test.ts
  3:0  error  Parsing error: Identifier expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/crypto.ts
   2:7   error    'BufferPolyfill' is assigned a value but never used  no-unused-vars
   2:7   error    'BufferPolyfill' is assigned a value but never used  @typescript-eslint/no-unused-vars
  18:48  warning  Unexpected any. Specify a different type             @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/fileValidation.ts
  364:0  error  Parsing error: ',' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/arrays/paginateArray.ts
  3:10  error  'it' is defined but never used  no-unused-vars
  3:10  error  'it' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/arrays/uniqueArray.ts
  5:42  error  '_item' is defined but never used  no-unused-vars
  5:53  error  'item' is not defined              no-undef
  5:64  error  'item' is not defined              no-undef

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/async/asyncEvery.ts
  4:54  error  'item' is defined but never used   no-unused-vars
  4:63  error  'index' is defined but never used  no-unused-vars
  4:78  error  'array' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/async/asyncFilter.ts
  4:77   error  'item' is defined but never used   no-unused-vars
  4:86   error  'index' is defined but never used  no-unused-vars
  4:101  error  'array' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/async/asyncMap.ts
  1:54  error  'item' is defined but never used   no-unused-vars
  1:63  error  'index' is defined but never used  no-unused-vars
  1:78  error  'array' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/async/asyncReduce.ts
  3:13  error  'accumulator' is defined but never used  no-unused-vars
  3:29  error  'item' is defined but never used         no-unused-vars
  3:38  error  'index' is defined but never used        no-unused-vars
  3:53  error  'array' is defined but never used        no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/async/waitFor.ts
  15:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/common/Search.ts
  4:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/navigation/navigationHelpers.ts
   8:41  error    'to' is defined but never used            no-unused-vars
   8:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   8:50  error    'options' is defined but never used       no-unused-vars
   8:90  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  18:58  error    'args' is defined but never used          no-unused-vars
  18:78  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/object/getNestedValue.ts
  1:113  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  6:15   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/promises/__tests__/promiseSequence.test.ts
  310:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/promises/__tests__/runPromiseWithTimeout.test.ts
  373:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/promises/promiseAllSettled.ts
  4:98  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/validation/__tests__/isValidInteger.test.ts
  274:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/validation/__tests__/validateEmail.test.ts
  193:0  error  Parsing error: '}' expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/validation/isValidInteger.ts
  7:39  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/wface/WTableHelpers/exportTableToExcel.ts
   12:19   error    'key' is defined but never used                                                                                 no-unused-vars
   17:47   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   56:49   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   64:51   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   71:46   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   82:29   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   95:35   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   97:5    error    Prefer using nullish coalescing operator (`??=`) instead of an assignment expression, as it is simpler to read  @typescript-eslint/prefer-nullish-coalescing
  103:34   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  103:47   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  124:40   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  148:58   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  148:107  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  155:34   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  158:54   warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/wface/WTableHelpers/recursiveTableTitleGenerator.ts
  14:57  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  16:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  18:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  23:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/helpers/workflow/tableHelpers.ts
   4:13  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   4:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   8:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  21:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/index.ts
  118:40  error    'args' is defined but never used          no-unused-vars
  118:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  118:95  error    'args' is defined but never used          no-unused-vars
  126:40  error    'args' is defined but never used          no-unused-vars
  126:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  126:95  error    'args' is defined but never used          no-unused-vars
  142:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  163:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/mobileAuth.ts
  5:21  error  '_message' is defined but never used  no-unused-vars

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/mobileBridge.ts
  63:2  error  Parsing error: Declaration or statement expected

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/performance.ts
   70:36  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  143:10  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  156:10  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/securityAudit.ts
  238:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  248:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/securityInitializer.ts
  173:43  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  174:38  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  192:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  387:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  415:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  444:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  445:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/webViewDetection.ts
  13:21  error    '_message' is defined but never used        no-unused-vars
  56:59  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any
  95:18  error    '_authenticated' is defined but never used  no-unused-vars
  96:17  error    '_userData' is defined but never used       no-unused-vars
  96:28  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any
  98:16  error    '_type' is defined but never used           no-unused-vars
  98:31  error    '_data' is defined but never used           no-unused-vars
  98:39  warning  Unexpected any. Specify a different type    @typescript-eslint/no-explicit-any

/Users/<USER>/Projects/digiflow/DigiflowReact/src/utils/webViewNonce.ts
  116:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  121:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  133:48  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

✖ 596 problems (343 errors, 253 warnings)

info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
