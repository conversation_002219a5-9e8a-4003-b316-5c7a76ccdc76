﻿using System;
using System.Configuration;

public partial class WFPages_PrfHedefDeger : Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Request["wfInstanceId"] != null)
        {           
            if (ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                Response.Redirect(ConfigurationManager.AppSettings["HedefDegerTest"] + Request["wfInstanceId"].ToString());
            }
            else
            {
                Response.Redirect(ConfigurationManager.AppSettings["HedefDegerCanli"] + Request["wfInstanceId"].ToString());
            }

            //if (CurrentWfIns != null)
            //{
            //    if (CurrentWfIns.WfCurrentState != null)
            //    {
            //        if (CurrentStateDef.WfStateDefId == 2595 || CurrentStateDef.WfStateDefId == 2597 || CurrentStateDef.WfStateDefId == 2596) // yonetici onayı
            //        {
            //        }
            //    }
            //}
        }
        else
        {
            Response.Redirect("Bu sayfaya girme yetkiniz bulunmamaktadır.");
            Response.End();
        }
    }

    public override void CreateWorkFlow()
    {
        //  throw new NotImplementedException();
    }

    public override void ApprovalWorkFlow()
    {
        //   throw new NotImplementedException();
    }

    public override void RejectWorkFlow()
    {
        //  throw new NotImplementedException();
    }

    public override void DisabledControl()
    {
        //  throw new NotImplementedException();
    }

    public override void NewWorkFlowLoading()
    {
        //   throw new NotImplementedException();
    }

    public override void LoadEntityToControls()
    {
        //   throw new NotImplementedException();
    }

    public override void LoadDataBinding()
    {
        //  throw new NotImplementedException();
    }

    public override void ChangeVisibilityOfTabs(bool newRequestTabPanelVisible, bool approveRejectTabPanelVisible, bool forwardTabPanelVisible, bool suspendResumeTabPanelVisible, bool abortTabPanelVisible, bool addCommentTabPanelVisible, bool rollbackTabPanelVisible, bool fileUploadTabPanelVisible, bool sendToCommentTabPanelVisible)
    {
        //  throw new NotImplementedException();
    }

    public override void ShowInformation(string Title, string Message)
    {
        //  throw new NotImplementedException();
    }
}