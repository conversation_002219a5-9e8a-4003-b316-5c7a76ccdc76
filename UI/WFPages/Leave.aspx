﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="Leave.aspx.cs" Inherits="WfPages_Leave" %>


<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<%@ Register Src="../UserControls/OrganizationTreeWebUserControl.ascx" TagName="OrganizationTreeWebUserControl"
    TagPrefix="uc2" %>
<%@ Register Src="~/UserControls/WorkflowHistory.ascx" TagName="WorkflowHistory"
    TagPrefix="uc3" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style type="text/css">
        .style3 {
            width: 212px;
        }

        .auto-style4 {
            height: 22px;
        }
    </style>

    <script type="text/javascript" language="javascript">

        function HidePopUp() {
            window.close();
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:Panel runat="server" ID="ContentPanel">
        <table width="100%">
            <tr>
                <td align="right">
                    <a href="http://digiflowdocs.digiturk.com.tr/DIGITURK%20Formlar/Prosedurler/%C4%B0NSAN%20KAYNAKLARINDAN%20SORUMLU%20BA%C5%9EKANLIK/Y%C4%B1ll%C4%B1k%20%C4%B0zin%20Prosed%C3%BCr%C3%BC%202025.pdf" target="_blank" style="font-size: large; font-weight: bold">
                        <asp:Literal ID="Literal10" runat="server" Text="<%$Resources:Resource, izin_prosedur%>"></asp:Literal>
                    </a>
                </td>
            </tr>
        </table>
        <table border="0" align="center" cellpadding="0" cellspacing="3px" width="60%">

            <tr align="left" valign="middle">
                <td colspan="4">
                    <uc2:OrganizationTreeWebUserControl ID="OrgTreeOwnerLoginId" runat="server" />
                </td>
            </tr>
            <tr align="left" valign="middle">
                <td>*</td>
                <td>
                    <asp:Literal ID="Literal3" runat="server" Text="<%$Resources:Resource, izin_bakiye%>"></asp:Literal>
                </td>
                <td align="left">
                    <asp:Label ID="lblLeaveDayCount" runat="server" Text=""></asp:Label>
                  <%--  <asp:Label ID="lblLeaveDayCountMessage" runat="server" Text="" ForeColor="Red" Font-Bold="true"></asp:Label>--%>
                </td>
                <td></td>
            </tr>
            <tr align="left" valign="middle" class="style3">
                <td>*</td>
                <td>
                    <asp:Literal ID="Literal1" runat="server" Text="<%$Resources:Resource, izin_turu%>"></asp:Literal>
                </td>
                <td>
                    <asp:DropDownList ID="drpLeaveType" runat="server" DataTextField="leaveType" DataValueField="leaveCode" Width="70%" OnSelectedIndexChanged="drpLeaveType_SelectedIndexChanged" AutoPostBack="true"></asp:DropDownList>
                </td>
                <td>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="drpLeaveType" Text="*" ErrorMessage="Lütfen İzin Türünü Seçiniz." ValidationGroup="vG0"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr align="left" valign="middle">
                <td>*</td>
                <td>
                    <asp:Literal ID="Literal2" runat="server" Text="<%$Resources:Resource, izin_basla%>"></asp:Literal>
                </td>
                <td>
                    <asp:TextBox ID="DtpStartTime" runat="server" class="DropDown" MaxLength="50" onkeypress="return DateControl(this);" autocomplete="off" AutoPostBack="True" OnTextChanged="DtpStartTime_TextChanged"></asp:TextBox>
                    <asp:CheckBox ID="ChkIsStartHalfDay" Text="<%$Resources:Resource, izin_yarim_gun%>" AutoPostBack="true" runat="server" OnCheckedChanged="DtpStartTime_TextChanged" />
                    <asp:RangeValidator ID="rvDtpStartTime" runat="server" ControlToValidate="DtpStartTime" ErrorMessage="<%$Resources:Resource, Main_tarih_format%>" Type="Date" ValidationGroup="vG0"></asp:RangeValidator>
                    <asp:ValidatorCalloutExtender ID="rvDtpStartTime_ValidatorCalloutExtender" runat="server" Enabled="True" TargetControlID="rvDtpStartTime"></asp:ValidatorCalloutExtender>
                    <asp:CalendarExtender ID="ClnExtStartTime" runat="server" CssClass="ajax__calendar" Format="d.MM.yyyy" TargetControlID="DtpStartTime"></asp:CalendarExtender>
                </td>
                <td>
                    <asp:RequiredFieldValidator ID="RFVStartTime" runat="server" ControlToValidate="DtpStartTime" Text="*" ErrorMessage="<%$Resources:Resource, izin_basla_uyari%>" ValidationGroup="vG0"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr align="left" valign="middle">
                <td>*
                </td>
                <td>
                    <asp:Literal ID="Literal4" runat="server" Text="<%$Resources:Resource, izin_bitis%>"></asp:Literal>
                </td>
                <td>
                    <asp:TextBox ID="DtpEndTime" runat="server" class="DropDown" MaxLength="10" onkeypress="return DateControl(this);" autocomplete="off" AutoPostBack="True" OnTextChanged="DtpEndTime_TextChanged"></asp:TextBox>
                    <asp:CheckBox ID="ChkIsFinishHalfDay" Text="<%$Resources:Resource, izin_yarim_gun%>" AutoPostBack="true" runat="server" OnCheckedChanged="DtpEndTime_TextChanged" />
                    <asp:RangeValidator ID="rvDtpEndTime" runat="server" ControlToValidate="DtpEndTime" ErrorMessage="<%$Resources:Resource, Main_tarih_format%>" Type="Date" ValidationGroup="vG0"></asp:RangeValidator>
                    <asp:ValidatorCalloutExtender ID="rvDtpEndTime_ValidatorCalloutExtender" runat="server" Enabled="True" TargetControlID="rvDtpEndTime"></asp:ValidatorCalloutExtender>
                    <asp:CalendarExtender ID="ClnExtEndTime" runat="server" CssClass="ajax__calendar" Format="d.MM.yyyy" TargetControlID="DtpEndTime"></asp:CalendarExtender>
    
                </td>
                <td></td>
            </tr>
            <tr>
                <td colspan="4">
                    <br />
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <div>
                        <asp:Repeater runat="server" ID="RepeaterVardiya" OnItemDataBound="RepeaterVardiya_ItemDataBound">
                            <HeaderTemplate>
                                <table align="center" border="1" bordercolor="black" style="border-color: Black; width: 750px;" cellpadding="1" cellspacing="1">
                            </HeaderTemplate>
                            <ItemTemplate>
                                <asp:Literal ID="litRowStart" runat="server"></asp:Literal>
                                <td>
                                    <asp:Label ID="lblTarih" runat="server" Text='<%# Eval("VDATE") %>'></asp:Label>
                                    <asp:Label ID="lblGun" runat="server" Text='<%# Eval("VDAY") %>'></asp:Label>
                                    <br />
                                    <asp:DropDownList ID="drpLeaveTypeR" SelectedValue='<%# Eval("VTYPE") %>' runat="server" OnSelectedIndexChanged="drpLeaveTypeR_SelectedIndexChanged" OnLoad="drpLeaveTypeR_Load" OnDataBound="drpLeaveTypeR_Load" AutoPostBack="true">
                                        <asp:ListItem Text="İzin" Value="1"></asp:ListItem>
                                        <asp:ListItem Text="Resmi Tatil" Value="2"></asp:ListItem>
                                        <asp:ListItem Text="Haftalık" Value="3"></asp:ListItem>
                                    </asp:DropDownList>
                                    <br />
                                    <asp:CheckBox ID="ChkIsVardiyaHD" Text="<%$Resources:Resource, izin_yarim_gun%>" Checked='<%# Convert.ToBoolean(Eval("ISHALFDAY")) %>' AutoPostBack="true" runat="server" OnCheckedChanged="ChkIsVardiyaHD_CheckedChanged" />
                                </td>
                                <asp:Literal ID="litRowEnd" runat="server"></asp:Literal>
                            </ItemTemplate>
                            <FooterTemplate>
                                </table>
                            </FooterTemplate>
                        </asp:Repeater>
                        <b>
                            <asp:Label ID="lblMazeret" runat="server" Visible="false" ForeColor="Red" Font-Bold="true" Text="Hangi saatler arası şirkette olmayacağınızı İZİN NEDENİ alanına yazınız.Saat aralığı belirtilmeyen akışlar onaylanmayacaktır!!!"></asp:Label>
                        </b>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <br />
                       <asp:Label ID="lblIzinDurum" runat="server" Visible="false" ForeColor="Red" Font-Bold="true" Text=""></asp:Label>
                </td>
            </tr>
            <tr align="left" valign="middle">
                <td>*
                </td>
                <td>
                    <asp:Literal ID="Literal5" runat="server" Text="<%$Resources:Resource, izin_sure%>"></asp:Literal>
                    (
                    <asp:Label ID="lblQuoate" runat="server" Text="<%$Resources:Resource, izin_gun%>"></asp:Label>
                    )
                </td>
                <td>
                    <asp:TextBox ID="txtQuoate" Width="119px" runat="server" ReadOnly="true"></asp:TextBox>
                    <br />
                    <asp:DropDownList ID="drpLeaveTimeHour" runat="server" Visible="false" Width="150px">
                        <asp:ListItem Text="<%$Resources:Resource, Main_Seciniz%>" Value="0"></asp:ListItem>
                        <asp:ListItem Text="0.5" Value="0,50"></asp:ListItem>
                        <asp:ListItem Text="1" Value="1"></asp:ListItem>
                        <asp:ListItem Text="1.5" Value="1,50"></asp:ListItem>
                        <asp:ListItem Text="2" Value="2"></asp:ListItem>
                        <asp:ListItem Text="2.5" Value="2,50"></asp:ListItem>
                        <asp:ListItem Text="3" Value="3"></asp:ListItem>
                    </asp:DropDownList>
                </td>
                <td></td>
            </tr>
            <tr align="left" valign="middle">
                <td>*
                </td>
                <td>
                    <asp:Literal ID="Literal6" runat="server" Text="<%$Resources:Resource, izin_nedeni%>"></asp:Literal>
                </td>
                <td>
                    <asp:TextBox ID="txtDecription" runat="server" Height="125px" Width="70%" MaxLength="1024"
                        Style="font-family: Microsoft Sans Serif, Sans-Serif;" TextMode="MultiLine" onkeyup="return maxLengthControl(this,'200');"
                        onChange="return maxLengthControl(this,'200');"></asp:TextBox>
                </td>
                <td>
                    <asp:RequiredFieldValidator ID="RFVDecription" runat="server" ControlToValidate="txtDecription"
                        Text="*" ErrorMessage="<%$Resources:Resource, Main_Aciklama_Giriniz%>" ValidationGroup="vG0"></asp:RequiredFieldValidator>
                </td>
            </tr>
        </table>
        <br />
        <table align="center" border="0" bordercolor="black" style="border-color: Black; width: 750px;"
            cellpadding="1" cellspacing="1">
            <asp:Repeater runat="server" ID="RptVacationDetail" Visible="false">
                <HeaderTemplate>
                    <tr>
                        <td>İzin Tipi
                        </td>
                        <td>Başlangıç Tarihi
                        </td>
                        <td>Bitiş Tarihi
                        </td>
                        <td>İzin Süresi
                        </td>
                    </tr>
                </HeaderTemplate>
                <ItemTemplate>
                    <tr>
                        <td width="25%">
                            <asp:CheckBox ID="ChkLeaveType" runat="server" Text='<%#DataBinder.Eval(Container.DataItem, "leaveType")%>'
                                AutoPostBack="true" OnCheckedChanged="ChkLeaveType_OnCheckedChanged" />
                            <asp:HiddenField ID="HdnTurKodu" runat="server" Value='<%#DataBinder.Eval(Container.DataItem, "leaveCode")%>' />
                        </td>
                        <td>
                            <asp:TextBox ID="DtpStartTimeDetail" runat="server" class="DropDown" Text='<%#DataBinder.Eval(Container.DataItem, "StartDate")%>' autocomplete="off"
                                AutoPostBack="true" OnTextChanged="DtpStartTimeDetail_OnTextChanged" MaxLength="50"></asp:TextBox>
                            <asp:CheckBox ID="ChkIsStartTimeDetail" Text="<%$Resources:Resource, izin_yarim_gun%>" runat="server" OnCheckedChanged="DtpStartTimeDetail_OnTextChanged"
                                AutoPostBack="true" />
                            <asp:RangeValidator ID="rvStartTimeDetail" runat="server" ControlToValidate="DtpStartTimeDetail"
                                ErrorMessage="<%$Resources:Resource, Main_tarih_format%>" Type="Date" MaximumValue="31.12.9999"
                                MinimumValue="01.01.1900">
                            </asp:RangeValidator>
                            <asp:ValidatorCalloutExtender ID="ValidatorCalloutExtender1" runat="server" Enabled="True"
                                TargetControlID="rvStartTimeDetail">
                            </asp:ValidatorCalloutExtender>
                            <asp:CalendarExtender ID="ClnExtStartTimeDetail" runat="server" CssClass="ajax__calendar"
                                Format="d.MM.yyyy" TargetControlID="DtpStartTimeDetail"></asp:CalendarExtender>
                        </td>
                        <td>
                            <asp:TextBox ID="DtpEndTimeDetail" runat="server" class="DropDown" Text='<%#DataBinder.Eval(Container.DataItem, "EndDate")%>' autocomplete="off"
                                AutoPostBack="true" OnTextChanged="DtpStartTimeDetail_OnTextChanged" MaxLength="50"></asp:TextBox>
                            <asp:CheckBox ID="ChkEndTimeDetail" Text="<%$Resources:Resource, izin_yarim_gun%>" runat="server" OnCheckedChanged="DtpStartTimeDetail_OnTextChanged"
                                AutoPostBack="true" />
                            <asp:RangeValidator ID="rvDtpEndTimeDetail" runat="server" ControlToValidate="DtpEndTimeDetail"
                                ErrorMessage="<%$Resources:Resource, Main_tarih_format%>" Type="Date" MaximumValue="31.12.9999"
                                MinimumValue="01.01.1900">
                            </asp:RangeValidator>
                            <asp:ValidatorCalloutExtender ID="rvStartDate_ValidatorCalloutExtender" runat="server"
                                Enabled="True" TargetControlID="rvDtpEndTimeDetail">
                            </asp:ValidatorCalloutExtender>
                            <asp:CalendarExtender ID="ClnEndTimeDetail" runat="server" CssClass="ajax__calendar"
                                Format="d.MM.yyyy" TargetControlID="DtpEndTimeDetail"></asp:CalendarExtender>
                        </td>
                        <td>
                            <asp:TextBox ID="txtQuoate" Width="50px" Text='<%#DataBinder.Eval(Container.DataItem, "Quoate")%>'
                                runat="server"></asp:TextBox>
                        </td>
                    </tr>
                </ItemTemplate>
            </asp:Repeater>
            <tr>
                <td class="style3" colspan="4"></td>
            </tr>
        </table>
        <br />

        <asp:Panel runat="server" ID="VacationDelegationInformation">

            <asp:Literal ID="Literal7" runat="server" Text="<%$Resources:Resource, izin_delege%>"></asp:Literal>

            <a href="NDelegation.aspx?IsFromLeave=1" target="_blank">
                <asp:Literal ID="Literal8" runat="server" Text="<%$Resources:Resource, izin_delege_form%>"></asp:Literal>
            </a>

            <asp:Literal ID="Literal9" runat="server" Text="<%$Resources:Resource, izin_delege_uyari%>"></asp:Literal>
            <br />

            <asp:CheckBox ID="chkNotDegelation" runat="server"
                Text="<%$Resources:Resource, izin_delegasyon%>"
                Width="100%" /><br />
            <asp:Label ID="lblError" runat="server"
                Text="Üstteki kutucuğu işaretleyiniz" ForeColor="Red" Visible="False"></asp:Label>
            <br />

            <br />
            <b></b>
        </asp:Panel>
    </asp:Panel>
    <asp:Panel ID="HistoryPanel" runat="server">
        <hr size="1" color="#ebebeb" />
        <asp:ValidationSummary ID="WfValidationSummary" runat="server" ShowMessageBox="True"
            ShowSummary="False" ValidationGroup="vG0" />
    </asp:Panel>
</asp:Content>