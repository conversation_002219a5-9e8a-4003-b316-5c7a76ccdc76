using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;

public partial class WfPages_JumpToState : Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage
{
    /// <summary>
    /// Referans InstanceId si
    /// </summary>
    public long RefInstanceId
    {
        get
        {
            if (Request.QueryString["RefInstanceId"] != null)
            {
                return ConvertionHelper.ConvertValue<long>(Request.QueryString["RefInstanceId"]);
            }
            else
            {
                return 0;
            }
        }
    }

    /// <summary>
    /// Say<PERSON>ın <PERSON>
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
    }

    /// <summary>
    /// Yeni akış başlatırılır.
    /// </summary>
    public override void CreateWorkFlow()
    {
        Digiturk.Workflow.Digiflow.Entities.JumpToStateRequest RequestObject = new Digiturk.Workflow.Digiflow.Entities.JumpToStateRequest();
        RequestObject.FlowName = ConvertionHelper.ConvertValue<string>(lblFlowName.Text);
        RequestObject.FlowInstanceID = ConvertionHelper.ConvertValue<long>(txtFlowInstanceId.Text);
        RequestObject.StateName = ConvertionHelper.ConvertValue<string>(lblStateName.Text);
        RequestObject.StateInstanceID = ConvertionHelper.ConvertValue<long>(txtStateInstanceId.Text);
        RequestObject.Description = txtDescription.Text;
        RequestObject.Link = GetJumpToStateFlowScreenLink(System.Configuration.ConfigurationManager.AppSettings["DomainName"].ToString(), UserInformation.LoginObject.LoginId, RequestObject.FlowInstanceID, RequestObject.StateInstanceID);
        RequestObject.Created = DateTime.Now;
        RequestObject.CreatedBy = UserInformation.LoginObject.LoginId;
        RequestObject.LastUpdated = DateTime.Now;
        RequestObject.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        ActionHelpers.CreateWorkFlow(RequestObject, ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(Request.Url.Segments[Request.Url.Segments.Length - 1])), UserInformation.LoginObject.LoginId);
        DisabledControl();
    }

    /// <summary>
    /// Onay aşaması çalışır.
    /// </summary>
    public override void ApprovalWorkFlow()
    {
        if (string.IsNullOrEmpty(Commend))
        {
            throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
        }
        JumpToStateRequest RequestObj = WFRepository<JumpToStateRequest>.GetEntity(CurrentWfIns.EntityRefId);
        FWfWorkflowInstance wfIns = WFRepository<FWfWorkflowInstance>.GetEntity(RequestObj.FlowInstanceID);
        if (wfIns.WfCurrentState != null)
        {
            if (wfIns.WfCurrentState.WfStateInstanceId == RequestObj.StateInstanceID)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.JumpToStateMessage());
            }
        }
        List<FLogin> toList = new List<FLogin>();
        FlowAdminOprObject FlowAdminOprs;
        using (UnitOfWork.Start())
        {
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

            if (AssignedUser == null)
            {
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(InstanceId, CurrentActionTaskInstance, UserInformation.LoginObject, CurrentWFContext, WFRepository<FLogin>.GetEntity(UserInformation.LoginObject.LoginId), true, Commend);
            }
            else
            {
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(InstanceId, CurrentActionTaskInstance, UserInformation.LoginObject, CurrentWFContext, AssignedUser, true, Commend);
            }
        }
        FlowAdminOperationChecking(FlowAdminOprs);
        DisabledControl();
    }

    /// <summary>
    /// Red Aşaması Çalışır
    /// </summary>
    public override void RejectWorkFlow()
    {
        if (string.IsNullOrEmpty(Commend))
        {
            throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
        }
        FlowAdminOprObject FlowAdminOprs;
        using (UnitOfWork.Start())
        {
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

            #region Akış Reddedilir

            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.RejectWorkFlow(InstanceId, CurrentActionTaskInstance, UserInformation.LoginObject, CurrentWFContext, AssignedUser, WorkflowHistoryActionType.REJECTED, Commend);

            #endregion Akış Reddedilir
        }
        FlowAdminOperationChecking(FlowAdminOprs);
        DisabledControl();
    }

    /// <summary>
    /// Kontroller Disable edilir
    /// </summary>
    public override void DisabledControl()
    {
        txtDescription.Enabled = false;
        if (CurrentWfIns != null)
        {
            if (AssignToLoginIdCheck(UserInformation.LoginObject.LoginId) || IsFlowAdmin || FormDelegation)
            {
                this.HprLink.Visible = true;
            }
            else
            {
                this.HprLink.Visible = false;
            }
        }
        else
        {
            this.HprLink.Visible = false;
        }
        //ContentPanel.Enabled = false;
        //CommentPanel.Enabled = false;
    }

    ///// <summary>
    ///// Açıklama alanı dönülür
    ///// </summary>
    ///// <returns></returns>
    //public override string GetCommendInfo()
    //{
    //    return CommentsTextBox.Text;
    //}
    /// <summary>
    /// Yeni Boş bir iş akışı yüklenir.
    /// </summary>
    public override void NewWorkFlowLoading()
    {
        using (UnitOfWork.Start())
        {
            //  CommentPanel.Visible = false;
            long RefInsId = 0;
            FWfWorkflowInstance RefWFInstance = WFRepository<FWfWorkflowInstance>.GetEntity(RefInstanceId);
            long StateInstanceId = RefWFInstance.WfCurrentState.WfStateInstanceId; // bunu Sakla ve Kaydederken kullan
            long WfInstanceId = RefWFInstance.WfWorkflowInstanceId; // Bunu Sakla ve kaydederken kullan
            lblFlowName.Text = RefWFInstance.WfWorkflowDef.Name;
            lblStateName.Text = RefWFInstance.WfCurrentState.WfStateDef.Name;
            lblWfInstanceID.Text = RefInstanceId.ToString();
            txtFlowInstanceId.Text = WfInstanceId.ToString();
            txtStateInstanceId.Text = StateInstanceId.ToString();
            lblStarterUser.Text = WfDataHelpers.GetLoginNameSurname(RefWFInstance.OwnerLogin.LoginId);
            trLinkRowId.Visible = false;
            List<FLogin> AssignToIdList = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(RefWFInstance.WfWorkflowInstanceId);
            lblAssigneruser.Text = "";
            for (int i = 0; i < AssignToIdList.Count; i++)
            {
                lblAssigneruser.Text += WfDataHelpers.GetLoginNameSurname(AssignToIdList[i]) + "<br>";
            }
        }
    }

    /// <summary>
    /// Mevcut iş akışı yüklenir
    /// </summary>
    public override void LoadEntityToControls()
    {
        JumpToStateRequest RequestObject = WFRepository<Digiturk.Workflow.Digiflow.Entities.JumpToStateRequest>.GetEntity(CurrentWfIns.EntityRefId);
        txtStateInstanceId.Text = ConvertionHelper.ConvertValue<string>(RequestObject.StateInstanceID);
        txtFlowInstanceId.Text = ConvertionHelper.ConvertValue<string>(RequestObject.FlowInstanceID);
        lblWfInstanceID.Text = ConvertionHelper.ConvertValue<string>(RequestObject.FlowInstanceID);
        lblFlowName.Text = ConvertionHelper.ConvertValue<string>(RequestObject.FlowName);
        lblStateName.Text = ConvertionHelper.ConvertValue<string>(RequestObject.StateName);
        txtDescription.Text = ConvertionHelper.ConvertValue<string>(RequestObject.Description);
        //HprLink.NavigateUrl = ConvertionHelper.ConvertValue<string>(RequestObject.Link).Replace("#LoginId#", UserInformation.LoginObject.LoginId.ToString());
        HprLink.NavigateUrl = GetJumpToStateFlowScreenLink(System.Configuration.ConfigurationManager.AppSettings["DomainName"].ToString(), UserInformation.LoginObject.LoginId, RequestObject.FlowInstanceID, RequestObject.StateInstanceID);
        FWfWorkflowInstance RefWFInstance = WFRepository<FWfWorkflowInstance>.GetEntity(RequestObject.FlowInstanceID);
        trLinkRowId.Visible = true;
        if (RefWFInstance.WfCurrentState != null)
        {
            if (FlowIsSuspend || CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd == "CANCELED")
            {
                HprLink.Enabled = false;
                lblLinkMessage.Visible = true;
                if (CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd == "CANCELED")
                {
                    lblLinkMessage.Text = "";
                }
                else
                {
                    lblLinkMessage.Text = Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.FlowJumpMessage();
                }
            }
            else if (RequestObject.StateInstanceID == RefWFInstance.WfCurrentState.WfStateInstanceId || RefWFInstance.WfWorkflowStatusType.WfWorkflowStatusTypeCd != "STARTED")
            {
                /// Bu Akış Atlatılabilir
                HprLink.Enabled = true;
                lblLinkMessage.Visible = false;
            }
            else
            {
                HprLink.Enabled = false;
                lblLinkMessage.Visible = true;
                lblLinkMessage.Text = Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.FlowJumpMessageDiff();
            }
        }
        else
        {
            HprLink.Enabled = false;
            lblLinkMessage.Visible = true;
            lblLinkMessage.Text = Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.FlowJumpMessageDiff();
        }

        lblStarterUser.Text = WfDataHelpers.GetLoginNameSurname(RefWFInstance.OwnerLogin.LoginId);
        List<FLogin> AssignToIdList = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(RefWFInstance.WfWorkflowInstanceId);
        lblAssigneruser.Text = "";
        for (int i = 0; i < AssignToIdList.Count; i++)
        {
            lblAssigneruser.Text += WfDataHelpers.GetLoginNameSurname(AssignToIdList[i]) + "<br>";
        }
        txtDescription.Enabled = false;

        WorkFlowDiagramResult result = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDiagramHelper.GetContentOfFlowDiagram(InstanceId, System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US");
        ((MasterPage)this.Master).SetContentOfFlowDiagram(result);
    }

    /// <summary>
    /// Atlatılmak istenen akışın ekranını açacak linki döndüren fonksiyon
    /// </summary>
    /// <param name="DomainName"> Uygulamamızın çalıştığı Root Path </param>
    /// <param name="LoginId"> Ekranı Açıcak kullanıcının loginId si</param>
    /// <param name="WfInstanceId"> Atlatılacak Akışın InstanceId si </param>
    /// <param name="WfStateInstanceId"> Akış Atlatma talebi sırasında depolanan stateId</param>
    /// <returns>Atlatılmak istenen akışın ekranını açacak link</returns>
    /// Burda link boş ise Akış Mevcut State de değil demektir
    public static string GetJumpToStateFlowScreenLink(string DomainName, long LoginId, long WfInstanceId, long WfStateInstanceId)
    {
        FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfInstanceId);
        FWfStateInstance WfStateIns = WFRepository<FWfStateInstance>.GetEntity(WfStateInstanceId);
        if (WfIns.WfCurrentState != null) // Akış devam ediyor mu diye kontrol ettirilir
        {
            if (WfIns.WfCurrentState.WfStateInstanceId == WfStateInstanceId) // akış talep yapılan state de mi diye kontrol ettirilir
            {
                FWfActionTaskInstance TaskIns = WFRepository<FWfActionTaskInstance>.GetEntity(WfStateIns.WfCurrentActionInstanceId);
                FWfActionTaskDef TaskDef = WFRepository<FWfActionTaskDef>.GetEntity(TaskIns.WfActionTaskDef.WfActionDefId);
                string TaskScreen = TaskDef.TaskScreen; // Akışın Ekranı bulunur.
                return string.Format("{0}{1}?LoginId={2}&wfInstanceId={3}", DomainName, TaskScreen, LoginId, WfInstanceId.ToString());
            }
            else
            {
                return ""; // Akış atlatılmasını istediğim State de değil demektir.
            }
        }
        else
        {
            return ""; // Akış bitmiş Demektir.
        }
    }

    /// <summary>
    /// Kontrollerin içerisindeki veriler yüklenir.
    /// </summary>
    public override void LoadDataBinding()
    {
        // TODO WF Instance Id alinacal
        // TODO WF STATE Id
        //JumpToStateRequest Req=
        //lblStarterUser.Text = WfDataHelpers.GetLoginNameSurname(RefWFInstance.OwnerLogin.LoginId);
        //List<FLogin> AssignToIdList = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(RefWFInstance.WfWorkflowInstanceId);
        //lblAssigneruser.Text = "";
        //for (int i = 0; i < AssignToIdList.Count; i++)
        //{
        //    lblAssigneruser.Text += WfDataHelpers.GetLoginNameSurname(AssignToIdList[i]) + "<br>";
        //}

        ((MasterPage)this.Master).ShowMenu(true);
        ((MasterPage)this.Master).PageTitle = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetDefinitionName(Request.Url.Segments[Request.Url.Segments.Length - 1]);
    }

    /// <summary>
    /// Tab Panelin Durumu değiştirilir
    /// </summary>
    /// <param name="newRequestTabPanelVisible"></param>
    /// <param name="approveRejectTabPanelVisible"></param>
    /// <param name="forwardTabPanelVisible"></param>
    /// <param name="suspendResumeTabPanelVisible"></param>
    /// <param name="abortTabPanelVisible"></param>
    /// <param name="addCommentTabPanelVisible"></param>
    /// <param name="rollbackTabPanelVisible"></param>
    public override void ChangeVisibilityOfTabs(bool newRequestTabPanelVisible, bool approveRejectTabPanelVisible, bool forwardTabPanelVisible, bool suspendResumeTabPanelVisible, bool abortTabPanelVisible, bool addCommentTabPanelVisible, bool rollbackTabPanelVisible, bool fileUploadTabPanelVisible, bool sendToCommentTabPanelVisible)
    {
        int ActiveTabIndex = 0;
        if (newRequestTabPanelVisible) ActiveTabIndex = 0;
        else if (approveRejectTabPanelVisible) ActiveTabIndex = 1;
        else if (forwardTabPanelVisible) ActiveTabIndex = 2;
        else if (sendToCommentTabPanelVisible) ActiveTabIndex = 3;
        else if (suspendResumeTabPanelVisible) ActiveTabIndex = 4;
        else if (abortTabPanelVisible) ActiveTabIndex = 5;
        else if (addCommentTabPanelVisible) ActiveTabIndex = 6;
        else if (rollbackTabPanelVisible) ActiveTabIndex = 7;
        else if (fileUploadTabPanelVisible) ActiveTabIndex = 8;
        ((MasterPage)this.Master).SetActiveTabIndex(ActiveTabIndex);
        ((MasterPage)this.Master).ShowActionsPanel(true);
        ((MasterPage)this.Master).ChangeVisibilityOfTabs
        (newRequestTabPanelVisible,
        approveRejectTabPanelVisible,
        forwardTabPanelVisible,
        suspendResumeTabPanelVisible,
        abortTabPanelVisible,
        addCommentTabPanelVisible,
        rollbackTabPanelVisible,
        fileUploadTabPanelVisible, 
        sendToCommentTabPanelVisible);
    }

    /// <summary>
    /// Mesaj kutusu çıkartılır
    /// </summary>
    /// <param name="Title"></param>
    /// <param name="Message"></param>
    public override void ShowInformation(string Title, string Message)
    {
        ((MasterPage)this.Master).ShowInformation(Title, Message);
    }
}