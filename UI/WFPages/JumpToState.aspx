﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="JumpToState.aspx.cs" Inherits="WfPages_JumpToState" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<%@ Register Src="../UserControls/OrganizationTreeWebUserControl.ascx" TagName="OrganizationTreeWebUserControl"
    TagPrefix="uc2" %>
<%@ Register Src="~/UserControls/WorkflowHistory.ascx" TagName="WorkflowHistory"
    TagPrefix="uc3" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style type="text/css">
        .style2 {
            width: 130px;
        }

        .style4 {
            width: 25px;
        }

        .style5 {
            width: 113px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:Panel runat="server" ID="ContentPanel">
        <br />
        <br />
        <br />
        <table align="center" width="80%" valign="top">
            <tr>
                <td>
                    <asp:Panel ID="PnlJumptoStateGroup" runat="server" GroupingText="AKIŞ ATLATMA TALEBİ"
                        Font-Bold="true" align="center" BorderColor="Black" Width="80%">
                        <table border="0" align="center" cellpadding="0" cellspacing="0" height="150px" style="width: 94%">
                            <tr align="center" valign="middle">
                                <td>&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr align="center" valign="middle">
                                <td></td>
                                <td align="left" class="style5">Akış No
                                </td>
                                <td align="left">
                                    <asp:Label ID="lblWfInstanceID" runat="server" Width="250px"></asp:Label>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr align="center" valign="middle">
                                <td></td>
                                <td align="left" class="style5">Akışın Adı
                                </td>
                                <td align="left">
                                    <asp:Label ID="lblFlowName" runat="server" Width="250px"></asp:Label>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr align="center" valign="middle">
                                <td>&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr align="center" valign="middle">
                                <td></td>
                                <td align="left" class="style5">Adım Adı
                                </td>
                                <td align="left">
                                    <asp:Label ID="lblStateName" Width="250px" runat="server"></asp:Label>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr align="center" valign="middle">
                                <td>&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" id="trWorkflowInstanceId" align="center" valign="middle" visible="false">
                                <td></td>
                                <td align="left" class="style5">Akış Instance Id
                                </td>
                                <td align="left">
                                    <asp:TextBox ID="txtFlowInstanceId" runat="server" Enabled="False" MaxLength="1024"></asp:TextBox>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" align="center" valign="middle" visible="false">
                                <td>&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" id="trStarterUser" align="center" valign="middle" visible="true">
                                <td></td>
                                <td align="left" class="style5">Akışı Başlatan
                                </td>
                                <td align="left">
                                    <asp:Label ID="lblStarterUser" Width="250px" runat="server"></asp:Label>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" align="center" valign="middle" visible="true">
                                <td>&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" id="trAssignerUser" align="center" valign="middle" visible="true">
                                <td></td>
                                <td align="left" class="style5">Üzerinde Bekleyenler
                                </td>
                                <td align="left">
                                    <asp:Label ID="lblAssigneruser" Width="250px" runat="server"></asp:Label>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" align="center" valign="middle" visible="true">
                                <td>&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" id="trStateInstanceId" align="center" valign="middle" visible="false">
                                <td class="style4">&nbsp;
                                </td>
                                <td align="left" class="style5">Akış Adım Id
                                </td>
                                <td align="left">
                                    <asp:TextBox ID="txtStateInstanceId" runat="server" Enabled="False" MaxLength="1024"></asp:TextBox>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" align="center" valign="middle" visible="false">
                                <td class="style4">&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr id="trDescription" runat="server" align="center" valign="middle" visible="true">
                                <td class="style4">&nbsp;
                                </td>
                                <td align="left" class="style5">Açıklama
                                </td>
                                <td align="left">
                                    <asp:TextBox ID="txtDescription" runat="server" Height="71px" MaxLength="200" TextMode="MultiLine" Style="font-family: Microsoft Sans Serif, Sans-Serif;"
                                        Width="247px" onkeyup="return maxLengthControl(this,'200');" onChange="return maxLengthControl(this,'200');"></asp:TextBox>
                                </td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr runat="server" align="center" valign="middle" visible="true">
                                <td class="style4">&nbsp;
                                </td>
                                <td align="left" class="style5">&nbsp;
                                </td>
                                <td align="left">&nbsp;
                                    <asp:RequiredFieldValidator ID="rqDescription" runat="server"
                                        ControlToValidate="txtDescription" ErrorMessage="Lütfen bir açıklama giriniz"
                                        ValidationGroup="vG0"></asp:RequiredFieldValidator>
                                </td>
                                <td>&nbsp;
                                </td>
                            </tr>
                            <tr runat="server" id="trLinkRowId" align="center" valign="middle" visible="true">
                                <td></td>
                                <td align="left" class="style5">Akış Atlatma Linki
                                </td>
                                <td align="left">
                                    <asp:HyperLink ID="HprLink" Target="_self" runat="server">Akış Atlatmak İçin Lütfen Tıklayınız</asp:HyperLink>
                                    <asp:Label ID="lblLinkMessage" Width="250px" runat="server"></asp:Label>
                                </td>
                                <td></td>
                            </tr>
                        </table>
                    </asp:Panel>
                </td>
            </tr>
        </table>
    </asp:Panel>
    <asp:Panel ID="HistoryPanel" runat="server">
        <hr size="1" color="#ebebeb" />
        <asp:ValidationSummary ID="wfValidationSummary" runat="server" ShowMessageBox="True" ShowSummary="False" ValidationGroup="vG0" />
    </asp:Panel>
</asp:Content>