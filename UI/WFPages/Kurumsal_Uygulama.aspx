﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="Kurumsal_Uygulama.aspx.cs" Inherits="Kurumsal_Uygulama" %>

<%@ Register Src="../UserControls/OrganizationTreeWebUserControl.ascx" TagName="OrganizationTreeWebUserControl"
    TagPrefix="uc1" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <style>
        .soladayali {
            text-align: left;
        }

        .solbaslikust {
            width: 100px !important;
        }

        .solbaslik {
            width: 150px !important;
        }
        #btnekle {
            height: 22px!important;
            padding: 0 7px!important;
            margin-top: 3px!important;
        }
    </style>
    <table width="100%" border="0">
        <tr align="center">
            <td align="center" colspan="2">
                <uc1:OrganizationTreeWebUserControl ID="OrgTreeOwnerLoginId" runat="server" />
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <hr />
            </td>
        </tr>
    </table>
    <asp:Panel ID="ContentPanel" runat="server" Visible="true">
        <table width="100%">
            <tr>
                <td class="solbaslikust">
                    <b>Talep </b>
                </td>
                <td>
                    <asp:TextBox ID="txtTip" runat="server" Style="width: 150px" Enabled="false" value="Kurumsal Uygulama"
                        ReadOnly="true"></asp:TextBox>
                </td>
            </tr>
            <tr>
                <td class="solbaslikust">
                    <b>Türü</b>
                </td>
                <td>
                    <asp:RadioButtonList ID="rdpTuru" runat="server" RepeatDirection="Horizontal" AutoPostBack="True"
                        OnSelectedIndexChanged="rdpTuru_SelectedIndexChanged">
                        <asp:ListItem Text="Yeni" Value="Yeni" Selected="True"></asp:ListItem>
                        <asp:ListItem Text="Hata" Value="Hata"></asp:ListItem>
                        <asp:ListItem Text="Revizyon" Value="Revizyon"></asp:ListItem>
                    </asp:RadioButtonList>
                </td>
            </tr>
            <tr>
                <td class="solbaslikust">
                    <b>İlgili Uygulama</b>
                </td>
                <td>
                    <asp:UpdatePanel runat="server" ID="UpdatePanel1">
                        <ContentTemplate>
                            <asp:DropDownList ID="drpUygulamalar" Style="width: 200px" runat="server" AutoPostBack="True">
                            </asp:DropDownList>
                            *
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="drpUygulamalar"
                                ErrorMessage="Lütfen ilgili uygulamayı seçiniz !" ValidationGroup="vG0"></asp:RequiredFieldValidator>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <hr />
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <b>Detaylı Açıklama </b>
                </td>
            </tr>
            <tr>
                <td colspan="2">Kurumsal uygulamanın hangi amaca yönelik olarak kullanılacağının açıklamasıdır.
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <asp:TextBox ID="txtaciklama" runat="server" MaxLength="2000" Style="width: 500px; height: 100px"
                        TextMode="MultiLine"></asp:TextBox>
                    *
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" ControlToValidate="txtaciklama"
                        runat="server" ValidationGroup="vG0" ErrorMessage="Lütfen detaylı açıklamayı giriniz !"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <hr />
                </td>
            </tr>
        </table>
    </asp:Panel>
    <asp:Panel ID="PanelOncelik" runat="server" Visible="true">
        <table width="100%">
            <tr>
                <td class="solbaslik">
                    <b>Öncelik</b>
                </td>
                <td>
                    <asp:DropDownList ID="drpOncelik" runat="server" Style="width: 200px">
                        <asp:ListItem Text="Seçiniz" Value="0" Selected="True" />
                        <asp:ListItem Text="Düşük" Value="3" />
                        <asp:ListItem Text="Orta" Value="2" />
                        <asp:ListItem Text="Yüksek" Value="1" />
                    </asp:DropDownList>
                </td>
            </tr>
        </table>
    </asp:Panel>
    <asp:Panel ID="ContentPanel2" runat="server" Visible="true">
        <table width="100%">
            <tr>
                <td class="solbaslik">
                    <b>Teslimi Talep Edilen Tarih</b>
                </td>
                <td>
                    <asp:TextBox ID="txttarih" runat="server" ReadOnly="true" autocomplete="off"></asp:TextBox>
                    <asp:CalendarExtender ID="ClnExtStartTime" runat="server" CssClass="ajax__calendar"
                        Format="dd.MM.yyyy" TargetControlID="txttarih" Enabled="True"></asp:CalendarExtender>
                    *
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ValidationGroup="vG0"
                        ErrorMessage="Lütfen teslimi talep ettiğiniz tarihi giriniz !" ControlToValidate="txttarih"></asp:RequiredFieldValidator><br />
                    <asp:RangeValidator ID="rvDate" ValidationGroup="vG0" runat="server" ControlToValidate="txttarih" ErrorMessage="Teslimi talep edilen tarih, bugünden sonraki bir gün seçilmelidir !" Type="Date" MinimumValue="04/03/2013" MaximumValue="01/01/2900" Display="Dynamic"></asp:RangeValidator>
                </td>
            </tr>
            <tr>
                <td class="solbaslik">
                    <b>Döküman Listesi</b>
                </td>
                <td>
                    <asp:UpdatePanel runat="server" ID="UpPanel1">
                        <ContentTemplate>
                            <asp:FileUpload ID="InsertFile" runat="server" />
                            <asp:Button ID="btnekle" ClientIDMode="Static" runat="server" Text="Ekle" OnClick="btnekle_Click" />
                        </ContentTemplate>
                        <Triggers>
                            <asp:PostBackTrigger ControlID="btnekle" />
                        </Triggers>
                    </asp:UpdatePanel>
                </td>
            </tr>
            <tr>
                <td class="solbaslik">&nbsp
                </td>
                <td>
                    <dx:ASPxGridView ID="gvInfo" runat="server" OnHtmlRowCreated="gvInfo_HtmlRowCreated"
                        AutoGenerateColumns="False" KeyFieldName="Kurumsal_TALEP_DETAIL_ID" Width="50%"
                        OnRowCommand="gvProducts_RowCommand" meta:resourcekey="gvInfoResource1">

                        <SettingsPager Mode="ShowAllRecords" />
                        <SettingsBehavior AllowSort="false" AllowGroup="false" AllowDragDrop="false" />
                        <Columns>
                            <dx:GridViewDataTextColumn Caption="ID" VisibleIndex="1" FieldName="ID" Visible="False"
                                meta:resourcekey="GridViewDataTextColumnResource1" ShowInCustomizationForm="True">
                                <DataItemTemplate>
                                    <%# Container.ItemIndex + 1 %>
                                </DataItemTemplate>
                            </dx:GridViewDataTextColumn>
                            <dx:GridViewDataTextColumn FieldName="CREATED_BY" Visible="false" VisibleIndex="20">
                            </dx:GridViewDataTextColumn>
                            <dx:GridViewDataTextColumn FieldName="CREATED_NAME" Caption="Ekleyen" ShowInCustomizationForm="True"
                                VisibleIndex="19" Width="250px" meta:resourcekey="GridViewDataTextColumnResource2"
                                CellStyle-CssClass="soladayali">
                            </dx:GridViewDataTextColumn>
                            <dx:GridViewDataTextColumn Caption="Döküman Adı" FieldName="DOKUMAN_ACIKLAMA" ShowInCustomizationForm="True"
                                VisibleIndex="3" Width="250px" meta:resourcekey="GridViewDataTextColumnResource2"
                                CellStyle-CssClass="soladayali">
                                <DataItemTemplate>
                                    <a href='<%#Eval("DOKUMAN_LINKI") %>'>
                                        <%#Eval("DOKUMAN_ACIKLAMA") %>
                                    </a>
                                </DataItemTemplate>
                                <HeaderStyle HorizontalAlign="Center" />
                                <CellStyle HorizontalAlign="Center" Wrap="True">
                                </CellStyle>
                            </dx:GridViewDataTextColumn>
                            <dx:GridViewDataButtonEditColumn Caption="Sil" FieldName="ID" VisibleIndex="22" meta:resourcekey="GridViewDataButtonEditColumnResource1"
                                ShowInCustomizationForm="True">
                                <DataItemTemplate>
                                    <dx:ASPxButton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                        CssPostfix="Office2010Silver" OnClick="btnRemove_Click" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                        Text="Sil" meta:resourcekey="DeleteButtonResource1">
                                        <ClientSideEvents Click="function(s, e) {e.processOnServer = confirm('Silmek istediğinize emin misiniz?');}" />
                                    </dx:ASPxButton>
                                </DataItemTemplate>
                                <CellStyle HorizontalAlign="Center">
                                </CellStyle>
                            </dx:GridViewDataButtonEditColumn>
                        </Columns>
                        <SettingsBehavior ConfirmDelete="True" AllowSelectSingleRowOnly="True" />
                        <SettingsBehavior AllowSelectSingleRowOnly="True" ConfirmDelete="True" />
                        <SettingsPager CurrentPageNumberFormat="{0}" ShowDefaultImages="False">
                            <AllButton Text="Tümü">
                            </AllButton>
                            <FirstPageButton Text="&lt;&lt; İlk Sayfa">
                            </FirstPageButton>
                            <LastPageButton Text="Son Sayfa &gt;&gt;">
                            </LastPageButton>
                            <NextPageButton Text="İleri &gt;">
                            </NextPageButton>
                            <PrevPageButton Text="&lt; Geri">
                            </PrevPageButton>
                            <Summary Text="Sayfa" />
                            <Summary Text="Sayfa" />
                        </SettingsPager>
                        <Settings ShowFilterRowMenu="True" />
                        <Settings ShowFilterRowMenu="True" />
                        <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                            FilterBarClear="Temizle" EmptyDataRow="Herhangi bir döküman listeye eklenmemiştir.Lütfen yukarıdaki bilgileri girip ekle butonuna tıklayınız."
                            ConfirmDelete="Bu Dökümanı silmek istediğinize emin misiniz?"></SettingsText>
                        <SettingsLoadingPanel Text="Yükleniyor" />
                        <SettingsLoadingPanel Text="Yükleniyor" />
                    </dx:ASPxGridView>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <hr>
                </td>
            </tr>
        </table>
    </asp:Panel>
    <asp:Panel ID="ContentPanel3" runat="server" Visible="false">
        <table width="100%">
            <tr>
                <td colspan="2" align="center">
                    <asp:CheckBox ID="chkanalizegonder" runat="server" TextAlign="Left" Text="<b>Problem mevcut, analiz adımına geri gönder</b>    " />
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <hr />
                </td>
            </tr>
        </table>
    </asp:Panel>
    <table width="100%">
        <tr>
            <td>* Talep, IT tarafından onaylandıktan sonra iş planına alınır.Gerekli alanları doldurulmamış
                yada eksik bilgi içeren talepler değerlendirilmez.
            </td>
        </tr>
    </table>
</asp:Content>