﻿using DevExpress.Web;
using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;

public partial class Kurumsal_Uygulama : WorkFlowPage
{
    private string SaveFolder = KurumsalTalepHelper.SaveFolder;
    private long logicalGroupBA = 438;
    private long logicalGroupTEST = 442;
    private long logicalGroupPAZARLAMA = 441;

    protected void Page_Load(object sender, EventArgs e)
    {
        rvDate.MinimumValue = DateTime.Now.AddDays(1).ToShortDateString();

        #region Ajax Calendar Extender workaround için yapıldı.

        if (IsPostBack)
        {
            if ((Request.Form[txttarih.UniqueID] != null) && (Request.Form[txttarih.UniqueID] != ""))
            {
                try
                {
                    ClnExtStartTime.SelectedDate = Convert.ToDateTime(Request.Form[txttarih.UniqueID]);
                }
                catch (Exception)
                {
                }

                try
                {
                    txttarih.Text = ClnExtStartTime.SelectedDate.Value.ToShortDateString();
                }
                catch (Exception)
                {
                }
            }
        }

        #endregion Ajax Calendar Extender workaround için yapıldı.

        #region logical Group Setleme

        if (ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
        {
            logicalGroupBA = 438;//Test Ortamındaki BA grubu
            logicalGroupTEST = 442; //Test Ortamındaki Test grubu
            logicalGroupPAZARLAMA = 441;//Test Ortamındaki Pazarlama Değerlendirme grubu
        }
        else
        {
            logicalGroupBA = 407;//Live Ortamdaki BA grubu
            logicalGroupTEST = 411; //Live Ortamındaki Test grubu
            logicalGroupPAZARLAMA = 410;//Live Ortamındaki Pazarlama Değerlendirme grubu
        }

        #endregion logical Group Setleme
    }

    protected void gvInfo_HtmlRowCreated(object sender, ASPxGridViewTableRowEventArgs e)
    {
        //Test aşamasında test ekibinin yalnızca kendi eklediği
        //dosyaları kaldırabilmesi için
        if (e.RowType != GridViewRowType.EmptyDataRow)
        {
            if (CurrentWfIns != null && CurrentStateIns != null)
            {
                if (CurrentStateDef.Name == "Test Aşaması")
                {
                    if (UserInformation.LoginObject.LoginId.ToString() != e.GetValue("CREATED_BY").ToString())
                    {
                        e.Row.Cells[2].Controls[0].Controls.Remove(e.Row.Cells[2].Controls[0].Controls[1]);
                    }
                }
            }
        }
    }

    /// <summary>
    ///     iş akışının onaylanması sırasında çalışır,(onayla butonuna basıldığı zaman)
    /// </summary>
    public override void ApprovalWorkFlow()
    {
        #region validasyonlar

        if (FormInformationHelper.IsDate(txttarih.Text))
        {
            if (ConvertionHelper.ConvertValue<DateTime>(txttarih.Text) <= DateTime.Now)
            {
                throw ExceptionHelper.ValidationError("Teslimi talep edilen tarih, bugünden sonraki bir gün seçilmelidir !");
            }
        }
        else
        {
            throw ExceptionHelper.ValidationError("Lütfen teslimi talep edilen tarihi kontrol ediniz !");
        }

        #endregion validasyonlar

        #region Onay Bloğu

        var toList = new List<FLogin>();
        FlowAdminOprObject FlowAdminOprs;

        //

        //
        using (UnitOfWork.Start())
        {
            CurrentWFContext.Parameters.AddOrChangeItem("AnalizeGonder", chkanalizegonder.Checked.ToString());
            CurrentWFContext.Parameters.AddOrChangeItem("Onayli", "Evet");
            CurrentWFContext.Save();
            ActionHelpers.VersionUpdateToEntity(InstanceId, CurrentActionTaskInstance.WfActionInstanceId);
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            Entity_guncelle();

            ActionHelpers.ApprovalWorkFlow(InstanceId, CurrentActionTaskInstance, UserInformation.LoginObject,
                                           CurrentWFContext, AssignedUser, true, Commend);
        }
        FlowAdminOperationChecking(FlowAdminOprs);

        #endregion Onay Bloğu

        toList = null;
        FlowAdminOprs = null;
        DisabledControl();
    }

    /// <summary>
    ///     Detaili ile birlikte entity kaydeder, referansları günceller.
    /// </summary>
    private void Entity_guncelle()
    {
        KurumsalTalep ReqObj;
        List<IDetailEntity> DetailObjList;
        Create_Entity(out ReqObj, out DetailObjList);
        ActionHelpers.EntitySave(ReqObj, DetailObjList);
        FWfWorkflowInstance WfIns2 = WFRepository<FWfWorkflowInstance>.GetEntity(CurrentWfIns.WfWorkflowInstanceId);
        WfIns2.EntityRefId = ReqObj.RequestId;
        WFRepository<FWfWorkflowInstance>.SaveEntity(WfIns2);
        WfIns2 = null;
    }

    /// <summary>
    ///     Tab Panelin Durumu değiştirilir
    /// </summary>
    /// <param name="newRequestTabPanelVisible"></param>
    /// <param name="approveRejectTabPanelVisible"></param>
    /// <param name="forwardTabPanelVisible"></param>
    /// <param name="suspendResumeTabPanelVisible"></param>
    /// <param name="abortTabPanelVisible"></param>
    /// <param name="addCommentTabPanelVisible"></param>
    /// <param name="rollbackTabPanelVisible"></param>
    public override void ChangeVisibilityOfTabs(bool newRequestTabPanelVisible, bool approveRejectTabPanelVisible,
                                                bool forwardTabPanelVisible, bool suspendResumeTabPanelVisible,
                                                bool abortTabPanelVisible, bool addCommentTabPanelVisible,
                                                bool rollbackTabPanelVisible, bool fileUploadTabPanelVisible, bool sendToCommentTabPanelVisible)
    {
        int ActiveTabIndex = 0;
        if (newRequestTabPanelVisible) ActiveTabIndex = 0;
        else if (approveRejectTabPanelVisible) ActiveTabIndex = 1;
        else if (forwardTabPanelVisible) ActiveTabIndex = 2;
        else if (sendToCommentTabPanelVisible) ActiveTabIndex = 3;
        else if (suspendResumeTabPanelVisible) ActiveTabIndex = 4;
        else if (abortTabPanelVisible) ActiveTabIndex = 5;
        else if (addCommentTabPanelVisible) ActiveTabIndex = 6;
        else if (rollbackTabPanelVisible) ActiveTabIndex = 7;
        else if (fileUploadTabPanelVisible) ActiveTabIndex = 8;
        ((MasterPage)Master).SetActiveTabIndex(ActiveTabIndex);
        ((MasterPage)Master).ShowActionsPanel(true);
        ((MasterPage)Master).ChangeVisibilityOfTabs
            (newRequestTabPanelVisible,
             approveRejectTabPanelVisible,
             forwardTabPanelVisible,
             suspendResumeTabPanelVisible,
             abortTabPanelVisible,
             addCommentTabPanelVisible,
             rollbackTabPanelVisible,
             fileUploadTabPanelVisible, 
             sendToCommentTabPanelVisible);
    }

    /// <summary>
    ///     yeni bir akış oluşturulurken çalıştırılır, (talep oluştur butonuna basıldığı zaman)
    /// </summary>
    public override void CreateWorkFlow()
    {
        DataTable KurumsalTalep_dtInfo = grid_kontrol();

        #region validasyonlar

        if (OrgTreeOwnerLoginId.UserId != null)
        {           
            if (OrgTreeOwnerLoginId.UserId == "0")
            {
                throw ExceptionHelper.ValidationError("Lütfen bir personel seçimi yapınız !");
            }
        }
                      
        if (FormInformationHelper.IsDate(txttarih.Text))
        {
            if (ConvertionHelper.ConvertValue<DateTime>(txttarih.Text) <= DateTime.Now)
            {
                throw ExceptionHelper.ValidationError("Teslim tarihi bugünden küçük seçilemez !");
            }
        }
        else
        {
            throw ExceptionHelper.ValidationError("Lütfen teslim tarihini kontrol ediniz !");
        }

        #endregion validasyonlar

        #region esitlemeler

        KurumsalTalep ReqObj;
        List<IDetailEntity> DetailObjList;
        Create_Entity(out ReqObj, out DetailObjList);

        #endregion esitlemeler

        var defId =
            ConvertionHelper.ConvertValue<long>(
                FormInformationHelper.GetdefinitionId(Request.Url.Segments[Request.Url.Segments.Length - 1]));
        ActionHelpers.CreateWorkFlow(ReqObj, DetailObjList, defId, UserInformation.LoginObject.LoginId);

        ReqObj = null;
        DetailObjList = null;
        DisabledControl();
    }

    /// <summary>
    ///     Entity detaili ile birlikte yeni oluşturulup out olarak döner
    /// </summary>
    /// <param name="ReqObj"></param>
    /// <param name="DetailObjList"></param>
    private void Create_Entity(out KurumsalTalep ReqObj, out List<IDetailEntity> DetailObjList)
    {
        ReqObj = new KurumsalTalep();
        ReqObj.ILGILI_UYGULAMA = ConvertionHelper.ConvertValue<Int32>(drpUygulamalar.SelectedValue);
        ReqObj.ONCELIK = ConvertionHelper.ConvertValue<Int32>(drpOncelik.SelectedValue);
        ReqObj.ACIKLAMA = txtaciklama.Text;
        ReqObj.TESLIM_TARIHI = ConvertionHelper.ConvertValue<DateTime>(txttarih.Text);
        ReqObj.LastUpdated = DateTime.Now;
        ReqObj.Created = DateTime.Now;
        ReqObj.CreatedBy = UserInformation.LoginObject.LoginId;
        ReqObj.LastUpdatedBy = UserInformation.LoginObject.LoginId;

        DetailObjList = new List<IDetailEntity>();
        foreach (DataRow item in ((DataTable)Session["KurumsalTalep_dtInfo"]).Rows)
        {
            var DetailRequestObj = new KurumsalTalepDetail();

            DetailRequestObj.DOKUMAN_ACIKLAMA = item["DOKUMAN_ACIKLAMA"].ToString();
            DetailRequestObj.DOKUMAN_PATH = item["DOKUMAN_PATH"].ToString();
            DetailRequestObj.LastUpdated = DateTime.Now;
            DetailRequestObj.Created = DateTime.Now;
            DetailRequestObj.CreatedBy = ConvertionHelper.ConvertValue<int>(item["CREATED_BY"].ToString());
            DetailRequestObj.LastUpdatedBy = UserInformation.LoginObject.LoginId;
            DetailObjList.Add(DetailRequestObj);
            DetailRequestObj = null;
        }
    }

    /// <summary>
    ///     Detail entity yi döndürür
    /// </summary>
    /// <returns></returns>
    private DataTable grid_kontrol()
    {
        DataTable KurumsalTalep_dtInfo;
        KurumsalTalep_dtInfo = (DataTable)Session["KurumsalTalep_dtInfo"];
        if (KurumsalTalep_dtInfo == null || KurumsalTalep_dtInfo.Rows.Count < 1)
        {
            fn_FormMemberTable();
            KurumsalTalep_dtInfo = (DataTable)Session["KurumsalTalep_dtInfo"];
        }
        return KurumsalTalep_dtInfo;
    }

    /// <summary>
    ///     tüm kontrolleri disable etmek için kullanılır
    /// </summary>
    public override void DisabledControl()
    {
        OrgTreeOwnerLoginId.SetControlStatus(false, false, false, false, false,false, false, false, false, false);
    }

    /// <summary>
    ///     sayfa yüklenirken kontroller default datasource ların bind edilmesi için kullanılır
    /// </summary>
    public override void LoadDataBinding()
    {
        fn_FormMemberTable();
        ((MasterPage)Master).ShowMenu(true);
        ((MasterPage)Master).PageTitle = FormInformationHelper.GetDefinitionName(Request.Url.Segments[Request.Url.Segments.Length - 1]);
    }

    /// <summary>
    ///     başlamış bir akış için kaydedilmiş entity lerin kontrollere yazılması için kullanılır,
    ///     ekrandaki textbox,dropdownlist, gridview vb gibi kontrollerin kaydedilen entity ye göre
    ///     doldurulması sağlanılır.
    /// </summary>
    public override void LoadEntityToControls()
    {
        #region master entity nin kontrolllere bind edilmesi

        KurumsalTalep ReqObj = WFRepository<KurumsalTalep>.GetEntity(CurrentWfIns.EntityRefId);
        OrgTreeOwnerLoginId.ManagerPanelVisible = false;
        OrgTreeOwnerLoginId.SetUser(CurrentWfIns.OwnerLogin.LoginId, false);

        #endregion master entity nin kontrolllere bind edilmesi

        #region Detailentity grid e yazılır

        DataTable dtDetails = KurumsalTalepHelper.GetDetails(CurrentWfIns.EntityRefId.Value);
        gvInfo.DataSource = dtDetails;
        gvInfo.DataBind();
        Session["KurumsalTalep_dtInfo"] = dtDetails;

        #endregion Detailentity grid e yazılır

        #region form üzerindeki esitlemeler

        rdpTuru.SelectedValue = RaporTalepHelper.UygulamaTuru(ReqObj.ILGILI_UYGULAMA.ToString());
        Uygulama_Getir();
        drpUygulamalar.SelectedValue = ReqObj.ILGILI_UYGULAMA.ToString(); ///change todo uygulama gizli olduğunda ne olacak düşünülecek?
        drpOncelik.SelectedValue = ReqObj.ONCELIK.ToString();
        txtaciklama.Text = ReqObj.ACIKLAMA;

        txttarih.Text = ReqObj.TESLIM_TARIHI.ToShortDateString();

        #endregion form üzerindeki esitlemeler

        ContentPanel.Enabled = false;
        ContentPanel2.Enabled = false;

        #region BA ve TEST grubuna dosya ekleme çıkarma hakkı kontrolü

        try
        {
            ContentPanel3.Visible = false;
            PanelOncelik.Visible = true;
            PanelOncelik.Enabled = false;

            #region Analiz ve Test Aşamalarında ilgili logical grupların içindeyse dosya ekleme çıkarma yetkisi verilmesi

            if (CurrentWfIns.WfCurrentState != null)
            {
                if (CurrentStateDef.Name == "Analiz Aşaması")
                {
                    if (LogicalGroupHelper.IsExistLogicalGroup(logicalGroupBA, UserInformation.LoginObject.LoginId))
                    {
                        ContentPanel2.Enabled = true;
                    }
                }
                else if (CurrentStateDef.Name == "Test Aşaması")
                {
                    if (LogicalGroupHelper.IsExistLogicalGroup(logicalGroupTEST, UserInformation.LoginObject.LoginId))
                    {
                        ContentPanel2.Enabled = true;
                        txttarih.Enabled = false;
                    }
                }
                else if (CurrentStateDef.Name == "Pazarlama Değerlendirme")
                {
                    if (LogicalGroupHelper.IsExistLogicalGroup(logicalGroupPAZARLAMA, UserInformation.LoginObject.LoginId))
                    {
                        PanelOncelik.Visible = true;
                        PanelOncelik.Enabled = true;
                    }
                }

            #endregion Analiz ve Test Aşamalarında ilgili logical grupların içindeyse dosya ekleme çıkarma yetkisi verilmesi

                #region Talep sahibi onayı ve Test aşamasında "Geri gönder" seçeneği çıkarılması

                if (CurrentStateDef.Name == "Talep Sahibi Onayı" || CurrentStateDef.Name == "Test Aşaması")
                {
                    ContentPanel3.Visible = true;
                }

                #endregion Talep sahibi onayı ve Test aşamasında "Geri gönder" seçeneği çıkarılması
            }
        }
        catch (Exception)
        {
            //Currentstate boş, akış bitmiş ,sorun yok.
            ContentPanel2.Enabled = false;
        }

        #endregion BA ve TEST grubuna dosya ekleme çıkarma hakkı kontrolü

        #region memorytemizleme

        dtDetails = null;
        ReqObj = null;

        #endregion memorytemizleme

        WorkFlowDiagramResult result = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDiagramHelper.GetContentOfFlowDiagram(InstanceId, System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US");
        ((MasterPage)this.Master).SetContentOfFlowDiagram(result);
    }

    /// <summary>
    /// Kontrolleri Enable etmek için kullanılır
    /// </summary>
    public override void EnabledToControl()
    {
        ContentPanel.Enabled = true;
        ContentPanel2.Enabled = true;
        if (LogicalGroupHelper.IsExistLogicalGroup(logicalGroupPAZARLAMA, UserInformation.LoginObject.LoginId))
        {
            PanelOncelik.Visible = true;
            PanelOncelik.Enabled = true;
        }
        else
        {
            PanelOncelik.Visible = false;
            PanelOncelik.Enabled = false;
        }
        ContentPanel3.Visible = false;
        fn_FormMemberTable();
        gvInfo.DataSource = Session["KurumsalTalep_dtInfo"];
        gvInfo.DataBind();
    }

    /// <summary>
    ///     ekran yeni bir akış için açılıyor ise bu metot çalıştırılır
    /// </summary>
    public override void NewWorkFlowLoading()
    {
        OrgTreeOwnerLoginId.SetUser(UserInformation.LoginObject.LoginId, false);
        Uygulama_Getir();
        PanelOncelik.Visible = false;
        if (LogicalGroupHelper.IsExistLogicalGroup(logicalGroupPAZARLAMA, UserInformation.LoginObject.LoginId))
        {
            PanelOncelik.Visible = true;
            PanelOncelik.Enabled = true;
        }
        else
        {
            PanelOncelik.Visible = false;
            PanelOncelik.Enabled = false;
        }
    }

    /// <summary>
    ///     akışı reddedilirken onay red butonuna basıldığında çalışır.
    /// </summary>
    public override void RejectWorkFlow()
    {
        if (string.IsNullOrEmpty(Commend))
        {
            throw ExceptionHelper.EmptyCommentValidationException();
        }
        FlowAdminOprObject FlowAdminOprs;
        using (UnitOfWork.Start())
        {
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

            #region Akış Reddedilir

            ActionHelpers.RejectWorkFlow(InstanceId, CurrentActionTaskInstance, UserInformation.LoginObject,
                                         CurrentWFContext, AssignedUser, WorkflowHistoryActionType.REJECTED, Commend);

            #endregion Akış Reddedilir
        }
        FlowAdminOperationChecking(FlowAdminOprs);
        DisabledControl();
        FlowAdminOprs = null;
    }

    /// <summary>
    ///     Mesaj kutusu çıkartılır
    /// </summary>
    /// <param name="Title"></param>
    /// <param name="Message"></param>
    public override void ShowInformation(string Title, string Message)
    {
        ((MasterPage)Master).ShowInformation(Title, Message);
    }

    /// <summary>
    ///     grid ten satır silmek için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnRemove_Click(object sender, EventArgs e)
    {
        var dtProducts = (DataTable)Session["KurumsalTalep_dtInfo"];
        dtProducts.Rows.RemoveAt(Convert.ToInt32(Session["KurumsalTalepRemoveID"]));
        Session["KurumsalTalep_dtInfo"] = dtProducts;
        gvInfo.DataSource = dtProducts;
        gvInfo.DataBind();
        Session["KurumsalTalep_dtInfo"] = (DataTable)dtProducts;
    }

    /// <summary>
    ///     Detay tablosu oluşturmak için kullanılır.
    /// </summary>
    /// <returns></returns>
    private void fn_FormMemberTable()
    {
        var dt = new DataTable();
        dt.Columns.Add(new DataColumn("Kurumsal_TALEP_DETAIL_ID", typeof(int)));
        dt.Columns.Add(new DataColumn("DOKUMAN_LINKI", typeof(string)));
        dt.Columns.Add(new DataColumn("DOKUMAN_PATH", typeof(string)));
        dt.Columns.Add(new DataColumn("DOKUMAN_ACIKLAMA", typeof(string)));
        dt.Columns.Add(new DataColumn("CREATED_BY", typeof(string)));
        dt.Columns.Add(new DataColumn("CREATED_NAME", typeof(string)));
        dt.Columns["Kurumsal_TALEP_DETAIL_ID"].AutoIncrement = true;
        dt.Columns["Kurumsal_TALEP_DETAIL_ID"].AutoIncrementSeed = 1;
        Session["KurumsalTalep_dtInfo"] = dt;
    }

    /// <summary>
    ///     Grid teki tüm veriyi silmek için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnRemoveAll_Click(object sender, EventArgs e)
    {
        Session["KurumsalTalep_dtInfo"] = null;
        gvInfo.DataSource = Session["KurumsalTalep_dtInfo"];
        gvInfo.DataBind();
        fn_FormMemberTable();
    }

    /// <summary>
    ///     Silinecek detay index ini alır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void gvProducts_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
    {
        Session["KurumsalTalepRemoveID"] = e.VisibleIndex;
    }

    protected void btnekle_Click(object sender, EventArgs e)
    {
        if (InsertFile.HasFile)
        {
            string SharePointPath = ConfigurationManager.AppSettings["SharePointKurumsalTalepUploadFolder"].ToString();
            string dosya_path = SharepointWrite(InsertFile, SharePointPath);
            DataTable KurumsalTalep_dtInfo = grid_kontrol();
            DataRow dr = KurumsalTalep_dtInfo.NewRow();
            dr["DOKUMAN_LINKI"] = dosya_path;
            dr["DOKUMAN_PATH"] = dosya_path.Replace(SharePointPath, "");
            dr["DOKUMAN_ACIKLAMA"] = InsertFile.FileName;
            dr["CREATED_BY"] = UserInformation.LoginObject.LoginId.ToString();
            dr["CREATED_NAME"] = UserInformation.UserNameSurName.ToString();
            KurumsalTalep_dtInfo.Rows.Add(dr);
            Session["KurumsalTalep_dtInfo"] = KurumsalTalep_dtInfo;
            gvInfo.DataSource = KurumsalTalep_dtInfo;
            gvInfo.DataBind();
        }
    }

    /// <summary>
    ///     görev atayarak akışı ilerletmek için kullanılır.
    /// </summary>
    /// <param name="SendTaskUserId"></param>
    public override void SendTaskWorkFlow(long SendTaskUserId)
    {
        #region validasyonlar

        if (FormInformationHelper.IsDate(txttarih.Text))
        {
            if (ConvertionHelper.ConvertValue<DateTime>(txttarih.Text) <= DateTime.Now)
            {
                throw ExceptionHelper.ValidationError("Teslimi talep edilen tarih, bugünden sonraki bir gün seçilmelidir !");
            }
        }
        else
        {
            throw ExceptionHelper.ValidationError("Lütfen teslim tarihini kontrol ediniz !");
        }

        if (string.IsNullOrEmpty(Commend))
        {
            throw ExceptionHelper.EmptyCommentValidationException();
        }
        if (SendTaskUserId == 0)
        {
            throw ExceptionHelper.ValidationError("Lütfen iş akışını atayacağınız developer'ı seçiniz !");
        }

        #endregion validasyonlar

        var toList = new List<FLogin>();
        FlowAdminOprObject FlowAdminOprs;
        using (UnitOfWork.Start())
        {
            CurrentWFContext.Parameters.AddOrChangeItem("ContractForwardPersonel", SendTaskUserId.ToString());
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            Entity_guncelle();
            ActionHelpers.Sendtask(InstanceId, SendTaskUserId, CurrentActionTaskInstance, UserInformation.LoginObject,
                                   CurrentWFContext, AssignedUser, true, Commend);
        }
        FlowAdminOperationChecking(FlowAdminOprs);
        DisabledControl();
    }

    protected void rdpTuru_SelectedIndexChanged(object sender, EventArgs e)
    {
        Uygulama_Getir();
    }

    private void Uygulama_Getir()
    {
        DataTable t = RaporTalepHelper.UygulamaListesi(rdpTuru.SelectedValue, "Kurumsal Uygulama", false, false);
        drpUygulamalar.DataSource = t;
        drpUygulamalar.DataTextField = "STRUYGULAMA";
        drpUygulamalar.DataValueField = "ID";
        drpUygulamalar.DataBind();
        UpdatePanel1.DataBind();
    }
}