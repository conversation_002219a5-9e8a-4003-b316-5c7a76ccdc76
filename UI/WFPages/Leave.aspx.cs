﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Web.UI.WebControls;

public partial class WfPages_Leave : Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage
{
    private long lCurrentRecord = 0;
    private long lRecordsPerRow = 7;

    private decimal izinCount = 0;
    private decimal resmiCount = 0;
    private decimal haftalikCount = 0;

    private string YillikIzin = "10";
    private string DogumIzni = "15";
    private string BabalikIzni = "20";
    private string VefatIzni = "25";
    private string SutIzni = "30";
    private string IdariIzni = "35";
    private string UcretsizIzin = "40";
    private string OkulIlkGunIzni = "41";
    private string KarneIzni = "42";
    private string MensturalIzin = "43";
    private string RaporIzni = "45";
    private string EvlilikIzni = "50";
    private string UcretsizDogumIzni = "55";
    private string BedelliAskerlikIzni = "56";
    private string CovidUcretsizIzni = "57";
    private string MazeretIzni = "100";
    private string TasinmaIzni = "101";
    private string DogumGunuIzni = "102"; 


    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            Session["SessionVardiya"] = null;
            Session["dtVardiyaDetail"] = null;
            Session["SessionF2F"] = null;
            Session["LeaveEndDate"] = null;
            Session["LeaveStartDate"] = null;
        }
    }

    public override void CancelEndFlow()
    {
        DataTable DtbDetail = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveRequestDetailInformation(CurrentWfIns.EntityRefId.Value);
        string Sicil = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetSicilName(CurrentWfIns.OwnerLogin.LoginId);
        Sicil = Sicil.PadLeft(5, '0');
        foreach (DataRow item in DtbDetail.Rows)
        {
            DateTime baslangic_tarihi = (DateTime)item["StartDate"];
            DateTime bitis_tarihi = (DateTime)item["EndDate"];
            string izin_tur_kodu = item["leaveCode"].ToString();
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveInformationCancelWFCorrection(
                Sicil,
                baslangic_tarihi.Year,
                baslangic_tarihi,
                bitis_tarihi,
                izin_tur_kodu);
        }
    }

    /// <summary>
    /// Yeni akış başlatır.
    /// </summary>
    public override void CreateWorkFlow()
    {

        
        bool createW = false;

        if (drpLeaveType.SelectedValue == UcretsizIzin)
        {
            ((MasterPage)Master).ShowPopup(false, "Uyarı", "İzin günü tarih aralıklarını belirlemek üzere İnsan Kaynakları departmanına uğramanızı rica ederiz.", false, ""); throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("İzin günü tarih aralıklarını belirlemek üzere İnsan Kaynakları departmanına uğramanızı rica ederiz.");
        }
        else
        {
            //Delegasyon tanımlamış mı kontrolü yapılır
            if (!chkNotDegelation.Checked)
            {
                DataTable dtDelegations = null;
                if (DtpEndTime.Text == null || DtpEndTime.Text == "")
                {
                    dtDelegations = DigiFlowDelegationManager.GetDelegationsOfUser(UserInformation.LoginObject.LoginId, ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text));
                }
                else
                {
                    dtDelegations = DigiFlowDelegationManager.GetDelegationsOfUser(UserInformation.LoginObject.LoginId, ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text));
                }

                if (dtDelegations.Rows.Count == 0)
                {
                    chkNotDegelation.ForeColor = System.Drawing.Color.Red;
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(Resources.Resource.izin_delegasyon_uyari);
                }
                else
                {
                    chkNotDegelation.ForeColor = System.Drawing.Color.Black;
                }
            }

            if (drpLeaveType.SelectedValue != MazeretIzni)
            {
                if (DtpEndTime.Text == "" || DtpEndTime.Text == "01.01.0001" || DtpEndTime.Text == "1.01.0001" || DtpEndTime.Text == null)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(Resources.Resource.izin_bitis_uyari);
                }
            }
            else
            {
                if (drpLeaveTimeHour.SelectedValue == "0")
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Mazeret İzin süresini Seçiniz.");
                }
                else
                {

                    decimal MazeretSure = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.GetMazeretSayiLng(UserInformation.LoginObject.LoginId.ToString(), ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Month.ToString("d2"), ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Year.ToString());

                    if (MazeretSure + ConvertionHelper.ConvertValue<decimal>(drpLeaveTimeHour.SelectedValue) > 3)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(Resources.Resource.izin_mazeret_uyari);
                    }

                }
            }

            string strUlkeId = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.CalisanUlkeIdBul(UserInformation.LoginObject.DomainUserName.ToString());

            double leaveTimeLimit = 0;
            if (strUlkeId == "1")
            {
                leaveTimeLimit = ConvertionHelper.ConvertValue<double>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveTypeLimit(drpLeaveType.SelectedValue));
                if (drpLeaveType.SelectedValue == IdariIzni) //z
                {
                    //leaveTimeLimit = 1;
                    leaveTimeLimit = 2;
                }
                else if (drpLeaveType.SelectedValue == KarneIzni) //z
                {
                    //leaveTimeLimit = 1;
                    leaveTimeLimit = 0.5;
                }
            }

            if (leaveTimeLimit != 0 && ConvertionHelper.ConvertValue<double>(txtQuoate.Text) > leaveTimeLimit)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("İzin değeri hatalı yeniden deneyiniz! En fazla girilebilecek süre : " + leaveTimeLimit);
            }

            Digiturk.Workflow.Digiflow.Entities.LeaveRequest RequestObject = new Digiturk.Workflow.Digiflow.Entities.LeaveRequest();
            RequestObject.IsNotDelegation = ConvertionHelper.ConvertValue<long>(chkNotDegelation.Checked);
            //RequestObject.OwnerLoginId = ConvertionHelper.ConvertValue<long>(OrgTreeOwnerLoginId.UserId);
            RequestObject.OwnerLoginId = ConvertionHelper.ConvertValue<long>(UserInformation.LoginObject.LoginId);
            RequestObject.StartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
            RequestObject.IsStartHalfDay = ConvertionHelper.ConvertValue<long>(ChkIsStartHalfDay.Checked);

            //Mazeret izni seçili ise saat seçilmiş ise
            if (drpLeaveTimeHour.SelectedValue != "0" && drpLeaveType.SelectedValue == MazeretIzni)
            {
                RequestObject.LeaveDayCount = 0;
                RequestObject.LeaveHourCount = ConvertionHelper.ConvertValue<decimal>(drpLeaveTimeHour.SelectedValue);
                RequestObject.EndTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
                if (RequestObject.LeaveHourCount == 0 || RequestObject.LeaveHourCount < 0)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("İzin değeri hatalı yeniden deneyiniz!");
                }
            }
            else
            {
                RequestObject.LeaveDayCount = ConvertionHelper.ConvertValue<decimal>(txtQuoate.Text); //Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationLongCalculate(RequestObject.StartTime, RequestObject.EndTime, strUlkeId);
                RequestObject.EndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
                RequestObject.LeaveHourCount = 0;
                if (RequestObject.LeaveDayCount == 0 || RequestObject.LeaveDayCount < 0)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("İzin değeri hatalı yeniden deneyiniz!");
                }
            }

            RequestObject.IsFinishHalfDay = ConvertionHelper.ConvertValue<long>(ChkIsFinishHalfDay.Checked);
            RequestObject.Decription = ConvertionHelper.ConvertValue<string>(txtDecription.Text);
            RequestObject.Created = DateTime.Now;
            RequestObject.CreatedBy = UserInformation.LoginObject.LoginId;

            if (RequestObject.StartTime.DayOfWeek == DayOfWeek.Saturday || RequestObject.StartTime.DayOfWeek == DayOfWeek.Sunday)
            {
                ChkIsStartHalfDay.Checked = false;
            }
            if (RequestObject.EndTime.DayOfWeek == DayOfWeek.Saturday || RequestObject.EndTime.DayOfWeek == DayOfWeek.Sunday)
            {
                ChkIsFinishHalfDay.Checked = false;
            }

            #region kapatılmış

            //if ((ChkIsStartHalfDay.Checked || ChkIsFinishHalfDay.Checked) && (ChkIsStartHalfDay.Checked != ChkIsFinishHalfDay.Checked))
            //{
            //    //RequestObject.LeaveDayCount= RequestObject.LeaveDayCount-1/2;
            //    decimal Result = 0;
            //    if (ChkIsStartHalfDay.Checked)
            //    {
            //        Result = decimal.Parse(RequestObject.LeaveDayCount.ToString()) - decimal.Parse("0,5");
            //    }
            //    else if (ChkIsFinishHalfDay.Checked)
            //    {
            //        Result = decimal.Parse(RequestObject.LeaveDayCount.ToString()) + decimal.Parse("0,5");
            //    }
            //    if (Result < 0)
            //    {
            //        Result = Result * -1;
            //    }
            //    //RequestObject.LeaveDayCount = decimal.Parse(RequestObject.LeaveDayCount.ToString()) - decimal.Parse("0,5");
            //    RequestObject.LeaveDayCount = Result;
            //}

            #endregion kapatılmış

            List<Digiturk.Workflow.Digiflow.Entities.IDetailEntity> DetailList = new List<IDetailEntity>();
            decimal TotalDayCount = 0;

            Digiturk.Workflow.Digiflow.Entities.LeaveDetailRequest DetailObject = new LeaveDetailRequest();
            DetailObject.leaveType = drpLeaveType.SelectedItem.Text;
            DetailObject.leaveCode = drpLeaveType.SelectedItem.Value;
            DetailObject.StartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);

            //Mazeret izni seçili ise saat seçilmiş ise
            if (drpLeaveTimeHour.SelectedValue != "0" && drpLeaveType.SelectedValue == MazeretIzni)
            {
                DetailObject.LeaveHourCount = ConvertionHelper.ConvertValue<decimal>(drpLeaveTimeHour.SelectedValue);
                DetailObject.Quoate = 0;
                DetailObject.EndTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
            }
            else
            {
                DetailObject.LeaveHourCount = 0;
                DetailObject.Quoate = ConvertionHelper.ConvertValue<decimal>(txtQuoate.Text);
                DetailObject.EndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
            }

            DetailObject.IsStartHalfDay = ChkIsStartHalfDay.Checked;
            DetailObject.IsFinishHalfDay = ChkIsFinishHalfDay.Checked;

            DetailObject.Created = DateTime.Now;
            DetailObject.CreatedBy = UserInformation.LoginObject.LoginId;
            TotalDayCount += DetailObject.Quoate;
            DetailList.Add(DetailObject);

            //Vardiya detay izin bilgileri
            List<Digiturk.Workflow.Digiflow.Entities.IDetailEntity> VardiyaDetailList = new List<IDetailEntity>();

            for (int i = 0; i < RepeaterVardiya.Items.Count; i++)
            {
                Digiturk.Workflow.Digiflow.Entities.LeaveVardiyaDetailRequest VardiyaDetailObject = new LeaveVardiyaDetailRequest();
                VardiyaDetailObject.leaveType = ((DropDownList)RepeaterVardiya.Items[i].FindControl("drpLeaveTypeR")).Text;
                VardiyaDetailObject.LeaveDate = ConvertionHelper.ConvertValue<DateTime>(((Label)RepeaterVardiya.Items[i].FindControl("lblTarih")).Text);
                VardiyaDetailObject.Quoate = 0;
                if (((CheckBox)RepeaterVardiya.Items[i].FindControl("ChkIsVardiyaHD")).Checked != null)
                {
                    VardiyaDetailObject.IsHalfDay = ConvertionHelper.ConvertValue<bool>(((CheckBox)RepeaterVardiya.Items[i].FindControl("ChkIsVardiyaHD")).Checked);
                }

                VardiyaDetailObject.Created = DateTime.Now;
                VardiyaDetailObject.CreatedBy = UserInformation.LoginObject.LoginId;
                VardiyaDetailList.Add(VardiyaDetailObject);
            }

            string Sicil = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetSicilName(UserInformation.LoginObject.LoginId);
            Sicil = Sicil.PadLeft(5, '0');

            //Poldyde izin toplamı
            decimal LeaveDayCount = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveDayCount(Sicil);
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.LeaveInfo LeaveValidityDayCount = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveValidityDayCount(Sicil);

            //eski sistemi kontrole gerek yok, 2012 yılından kullanılmıyor
            //decimal ApproveCount = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.OldSystemVacationApproveDay(UserInformation.LoginObject.LoginId);

            //Mevcut Sistemde Talep Edilmiş onay aşamasındaki izinlerin toplamı
            decimal NewSystemApproveDay = 0;

            if (drpLeaveType.SelectedValue != YillikIzin)//Yıllık izin dışında
            {
                NewSystemApproveDay = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationTypeApproveDay(UserInformation.LoginObject.LoginId, drpLeaveType.SelectedValue, RequestObject.StartTime);
            }
            else
            {
                //- bakiyeye düşmesine izin verildiği kontrol kapatılmıştı bu sebeple hesaplama işlemi de kapatılabilir
                NewSystemApproveDay = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationApproveDay(UserInformation.LoginObject.LoginId, ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(Request.Url.Segments[Request.Url.Segments.Length - 1])));
            }

            //decimal ResultApproveDay = ApproveCount + NewSystemApproveDay;
            decimal ResultApproveDay = NewSystemApproveDay;

            //Poldyde izinlerine izin haketmeden bakiyesine eklenmiş mi mesajı için
            //string LeaveDayCountMessage = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveInformationMessage(Sicil, LeaveDayCount, ResultApproveDay);

            lblLeaveDayCount.Text = LeaveDayCount.ToString();
            //lblLeaveDayCountMessage.Text = LeaveDayCountMessage;

            //Bu kota yıllık izin dışındaki izin tipleri için geçerli olacak(Evlilik ve Babalık)- iznin başlangıç tarihi için geçerli
            decimal Kota = ((decimal)(ResultApproveDay));
            
            if (RequestObject.EndTime.ToShortDateString() != "01.01.0001" && RequestObject.EndTime.ToShortDateString() != "1.01.0001" && RequestObject.EndTime < RequestObject.StartTime)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("İzin başlangıç tarihi bitiş tarihinden büyük olmamalıdır");
            }
            //eski sistemi kontrole gerek yok, 2012 yılından kullanılmıyor Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.OldSystemVacationDateCheck(UserInformation.LoginObject.LoginId, RequestObject.StartTime, RequestObject.EndTime) ||
            
            else if (ConvertionHelper.ConvertValue<double>(Kota + TotalDayCount) > leaveTimeLimit && leaveTimeLimit != 0) // babalık ve evlilik izin -doğum günü eklendi
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Yıl içinde Talep Edebileceğiniz İzin Kotası:" + leaveTimeLimit + " gündür. Bu yıl içinde daha önceden kullanılan izin :" + Kota + ", Talep Etmekte olduğunuz izin:" + TotalDayCount + " gündür. Bu Talebi Gerçekleştiremezsiniz");
            }
            else if (drpLeaveType.SelectedValue == YillikIzin && ConvertionHelper.ConvertValue<double>(Kota + TotalDayCount) > (LeaveValidityDayCount.HakedilecekGunSayisi / 2) + (double)LeaveDayCount) // Yıllık izin eksiye düşme
            {
                double KullanilabilirGun = ((LeaveValidityDayCount.HakedilecekGunSayisi / 2) + (double)LeaveDayCount);
                string Msg = "Talep edilen izin {0} gündür, şuanki bakiyeniz {1} gündür, Onayda bekleyen izin talep gün sayınız {2} gündür, kullanabileceğiniz gün sayısı {3} gündür</br>Bu talebi gerçekleştiremezsiniz.</br>İzin hakediş tarihiniz olan {4} tarihten sonra tekrar talep ediniz.";
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(String.Format(Msg, TotalDayCount, LeaveDayCount, Kota, (KullanilabilirGun > 0 ? KullanilabilirGun : 0), LeaveValidityDayCount.HakedilecekTarih.ToShortDateString()));
            }            
            else if (drpLeaveType.SelectedValue == IdariIzni && TotalDayCount > 2) // idari izin
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " Kotası 2 gündür. Talep Etmekte olduğunuz izin:" + TotalDayCount + " gündür. Bu Talebi Gerçekleştiremezsiniz");
            }
            else if (drpLeaveType.SelectedValue == IdariIzni) // idari izin
            {
                string strAy = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Month.ToString("d2");
                string strYil = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Year.ToString();
                string IdariIzinSure = VacationInformationHelper.GetIdariIzinAy(UserInformation.LoginObject.LoginId.ToString(), strAy, strYil);
                long isIdariIzin = 0;
                if (IdariIzinSure != "")
                {
                    if (ConvertionHelper.ConvertValue<decimal>(IdariIzinSure) + TotalDayCount > 2)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " Kotası aynı  ay içinde 2  gündür. Bu ay içinde daha önceden 2 gün idari izin kullandığınızdan bu talebi gerçekleştiremezsiniz");
                        isIdariIzin = 1;
                    }
                }

                IdariIzinSure = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.GetIdariIzinYil(UserInformation.LoginObject.LoginId.ToString(), strYil);
                if (IdariIzinSure != "")
                {
                    if (ConvertionHelper.ConvertValue<decimal>(IdariIzinSure) + TotalDayCount > 4)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " Kotası aynı  yıl içinde 4 gündür. Bu yıl içinde 4 gün idari izin kullandığınızdan bu talebi gerçekleştiremezsiniz");
                        isIdariIzin = 1;
                    }
                }

                if (isIdariIzin == 0)
                {
                    createW = true;
                }
            }
            else if (drpLeaveType.SelectedValue == KarneIzni)
            {
                string strAy = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Month.ToString("d2");
                string strYil = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Year.ToString();
                string KarneIzinSure = VacationInformationHelper.GetKarneIzinAy(UserInformation.LoginObject.LoginId.ToString(), strAy, strYil);
                long isKarneIzin = 0;
                if (KarneIzinSure != "")
                {
                    if (ConvertionHelper.ConvertValue<decimal>(KarneIzinSure) + TotalDayCount > ConvertionHelper.ConvertValue<decimal>(0.5))
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " Kotası aynı ay içinde / İnsan Kaynakları tarafından belirtilen eğitim ayı için 0.5 gündür. Tanımlı içinde daha önceden 0.5 gün karne izni kullandığınızdan bu talebi gerçekleştiremezsiniz");
                        isKarneIzin = 1;
                    }
                }

                KarneIzinSure = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.GetKarneIzinYil(UserInformation.LoginObject.LoginId.ToString(), strAy, strYil);
                if (KarneIzinSure != "")
                {
                    if (ConvertionHelper.ConvertValue<decimal>(KarneIzinSure) + TotalDayCount > 1)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " Kotası aynı  yıl içinde 1 gündür. Bu yıl içinde 1 gün Karne izni kullandığınızdan bu talebi gerçekleştiremezsiniz");
                        isKarneIzin = 1;
                    }
                }

                if (isKarneIzin == 0)
                {
                    createW = true;
                }
            }
            else if (drpLeaveType.SelectedValue == MensturalIzin)
            {
                string strAy = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Month.ToString("d2");
                string strYil = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Year.ToString();
                string MensturalIzinSure = VacationInformationHelper.GetMensturalIzinAy(UserInformation.LoginObject.LoginId.ToString(), strAy, strYil);
                long isMensturalIzin = 0;

                if (MensturalIzinSure != "")
                {
                    if (ConvertionHelper.ConvertValue<decimal>(MensturalIzinSure) + TotalDayCount > 1)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " kotası aynı ay içinde 1 gündür. Bu ay içinde daha önceden 1 gün " + drpLeaveType.SelectedItem.Text + "  kullandığınızdan bu talebi gerçekleştiremezsiniz");
                        isMensturalIzin = 1;
                    }
                    else if (ConvertionHelper.ConvertValue<decimal>(MensturalIzinSure) + TotalDayCount < 1)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " kotası aynı ay içinde 1 gündür. Daha az ya da fazla gün kullanamazsnız!");
                        isMensturalIzin = 1;
                    }
                }

                if (isMensturalIzin == 0)
                {
                    createW = true;
                }
            }
            else if (drpLeaveType.SelectedValue == OkulIlkGunIzni)
            {
                string strAy = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Month.ToString("d2");
                string strYil = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Year.ToString();
                string OkulIlkGunIzniSure = VacationInformationHelper.GetOkulIlkGunIzinAy(UserInformation.LoginObject.LoginId.ToString(), strAy, strYil);
                long isOkulIlkGunIzni = 0;

                if (OkulIlkGunIzniSure != "")
                {
                    if (ConvertionHelper.ConvertValue<decimal>(OkulIlkGunIzniSure) + TotalDayCount > 1)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " Kotası aynı ay içinde 1 gündür. Bu ay içinde daha önceden " + drpLeaveType.SelectedItem.Text + " kullandığınızdan bu talebi gerçekleştiremezsiniz");
                        isOkulIlkGunIzni = 1;
                    }
                    else if (ConvertionHelper.ConvertValue<decimal>(OkulIlkGunIzniSure) + TotalDayCount < 1)
                    {
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError(drpLeaveType.SelectedItem.Text + " kotası aynı ay içinde 1 gündür. Daha az ya da fazla gün kullanamazsnız!");
                        isOkulIlkGunIzni = 1;
                    }
                }

                if (isOkulIlkGunIzni == 0)
                {
                    createW = true;
                }
            }
            else
            {


                createW = true;
                // sonra açılacak
                // string tomail ="<EMAIL>";
                //// Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.CreateMeetingRequest(tomail, UserInformation.UserNameSurName + " izinli", "izin deneme", RequestObject.StartTime, RequestObject.EndTime);
                // Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.AddAppointment(tomail, UserInformation.UserNameSurName + " izinli", "izin deneme", RequestObject.StartTime, RequestObject.EndTime);

            }

            
            if (Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationDateCheck(UserInformation.LoginObject.LoginId, RequestObject.StartTime, RequestObject.EndTime, RequestObject.IsStartHalfDay.ToString(), RequestObject.IsFinishHalfDay.ToString()))
            {
                string VacationDateCheckV = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationDateCheckValue(UserInformation.LoginObject.LoginId, RequestObject.StartTime, RequestObject.EndTime, RequestObject.IsStartHalfDay.ToString(), RequestObject.IsFinishHalfDay.ToString());
                bool VacationDateCheckSameDate = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationDateCheckSameDate(UserInformation.LoginObject.LoginId, RequestObject.StartTime, RequestObject.EndTime);

                if (TotalDayCount == ConvertionHelper.ConvertValue<decimal>(0.5) && ConvertionHelper.ConvertValue<decimal>(VacationDateCheckV) == ConvertionHelper.ConvertValue<decimal>(0.5) && RequestObject.StartTime == RequestObject.EndTime && !VacationDateCheckSameDate)
                {
                    createW = true;
                }
                else
                {
                    createW = false;
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Girmiş olduğunuz izin tarihleri daha önceki taleplerinizle çakışmaktadır");
                }
            }
            

            long CarsambaYetkiliGrup = ConvertionHelper.ConvertValue<int>(LogicalGroupHelper.LogicalGroupIDBul("IzinCarsambaKontrolYetkiliGrup"));

            if (!(LogicalGroupHelper.IsExistLogicalGroup(CarsambaYetkiliGrup, UserInformation.LoginObject.LoginId)))
                {

                Session["SessionVardiya"] = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());

                if (drpLeaveType.SelectedValue == YillikIzin && ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]) == false)
                {
                    if (CarsambaKontrol() == false)
                    {
                        createW = false;
                        // lblMessage.Text = "Pazartesi ve Salı zaten izinli. Çarşamba olmadan Perşembe veya Cuma izin alınamaz.";
                        throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen, Çalışan İlişkileri ve Bordro İşlemleri Müdürlüğü ile iletişime geçiniz! <a href='mailto: <EMAIL>'> IS HR PAYROLL TEAM</a>");
                    }
                }
            }

            if (createW)
            {
                List<Digiturk.Workflow.Digiflow.Entities.IDetailEntity> DetailList3 = new List<IDetailEntity>();
                ActionHelpers.CreateWorkFlow(RequestObject, DetailList, VardiyaDetailList, DetailList3, ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(Request.Url.Segments[Request.Url.Segments.Length - 1])), UserInformation.LoginObject.LoginId);
                DisabledControl();
            }
            else
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Akış Oluşturulamadı!");

            }
        }
    }

    /// <summary>
    /// Onay aşamasındadır.
    /// </summary>
    public override void ApprovalWorkFlow()
    {
        if (string.IsNullOrEmpty(Commend))
        {
            throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
        }
        ActionHelpers.VersionUpdateToEntity(InstanceId, CurrentActionTaskInstance.WfActionInstanceId);
        List<FLogin> toList = new List<FLogin>();
        FlowAdminOprObject FlowAdminOprs;
        using (UnitOfWork.Start())
        {
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(InstanceId, CurrentActionTaskInstance, UserInformation.LoginObject, CurrentWFContext, AssignedUser, true, Commend);
        }
        FlowAdminOperationChecking(FlowAdminOprs);
        DisabledControl();
    }

    /// <summary>
    /// Red Aşamasındadır
    /// </summary>
    public override void RejectWorkFlow()
    {
        if (string.IsNullOrEmpty(Commend))
        {
            throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
        }
        FlowAdminOprObject FlowAdminOprs;
        using (UnitOfWork.Start())
        {
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

            #region Akış Reddedilir

            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.RejectWorkFlow(InstanceId, CurrentActionTaskInstance, UserInformation.LoginObject, CurrentWFContext, AssignedUser, WorkflowHistoryActionType.REJECTED, Commend);

            //string tomail = UserInformation.LoginObject.Email; // talep sahibine

            //Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.ThisAddIn_Startup(UserInformation.LoginObject.Email, UserInformation.UserNameSurName + " izinli", "izin deneme", ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text));

            #endregion Akış Reddedilir
        }
        FlowAdminOperationChecking(FlowAdminOprs);
        DisabledControl();
    }

    /// <summary>
    /// Kontroller Enable Eder
    /// </summary>
    public override void EnabledToControl()
    {
        ContentPanel.Enabled = true;
        base.EnabledToControl();
    }

    /// <summary>
    /// Kontroller Disable edilir
    /// </summary>
    public override void DisabledControl()
    {
        ContentPanel.Enabled = false;
        // CommentPanel.Enabled = false;
    }

    ///// <summary>
    ///// Yorumlar döndürülür
    ///// </summary>
    ///// <returns></returns>
    //public override string GetCommendInfo()
    //{
    //    return CommentsTextBox.Text;
    //}

    /// <summary>
    /// Yeni Boş bir akış oluşturur.
    /// </summary>
    public override void NewWorkFlowLoading()
    {
        //   CommentPanel.Visible = false;
        OrgTreeOwnerLoginId.ManagerPanelVisible = false;
        OrgTreeOwnerLoginId.UserPanelVisible = false;
        OrgTreeOwnerLoginId.ByPassDepartmentBinding = true;
        OrgTreeOwnerLoginId.SetValues(UserInformation.LoginDtoObject.DepartmentId, UserInformation.LoginDtoObject.DivisionId, UserInformation.LoginDtoObject.UnitId, UserInformation.LoginDtoObject.TeamId, "", false, UserInformation.LoginDtoObject.LoginId, true, true, UserInformation.LoginDtoObject.SubTeam_1_Id, UserInformation.LoginDtoObject.SubTeam_2_Id, UserInformation.LoginDtoObject.SubTeam_3_Id, UserInformation.LoginDtoObject.SubTeam_4_Id, UserInformation.LoginDtoObject.SubTeam_5_Id);
        //OrgTreeOwnerLoginId.SetValues(UserInformation.LoginDtoObject.DepartmentId, UserInformation.LoginDtoObject.DivisionId, UserInformation.LoginDtoObject.UnitId, UserInformation.LoginDtoObject.TeamId, "", false, UserInformation.LoginDtoObject.LoginId, true, true);
        OrgTreeOwnerLoginId.SetPageMode(true);
        //RptVacationDetail.DataSource = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveType();
        //RptVacationDetail.DataBind();
        ControlCheckedStateOfLeave();
        string Sicil = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetSicilName(UserInformation.LoginObject.LoginId);
        DataTable dtPersonelType = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.JobEntranceFormHelper.GetPersonalTypes(UserInformation.LoginObject.LoginId.ToString());

        if (dtPersonelType.Rows.Count > 0)
        {

            if (Sicil == "" || Sicil.Length > 5)
            {
                //string ApplicatioName = System.Configuration.ConfigurationManager.AppSettings["ApplicationName"];
                ////Common.SetSessionError("Yetkisiz Erişim", "Yıllık İzin Formuna erişim yetkiniz bulunmamaktadır.", null);
                //Response.Redirect(ApplicatioName + "/Error.aspx", true);
                DisabledControl();
                ShowInformation(Resources.Resource.main_hata, Resources.Resource.izin_kayit_bulunamadi);
            }
            Sicil = Sicil.PadLeft(5, '0');
            decimal LeaveDayCount = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveDayCount(Sicil);
            if (LeaveDayCount == Convert.ToDecimal("-1,10"))
            {
                DisabledControl();
                ShowInformation(Resources.Resource.main_hata, Resources.Resource.izin_kayit_bulunamadi);
            }
            else
            {
                decimal ApproveCount = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.OldSystemVacationApproveDay(UserInformation.LoginObject.LoginId);
                decimal NewSystemApproveDay = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationApproveDay(UserInformation.LoginObject.LoginId, ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(Request.Url.Segments[Request.Url.Segments.Length - 1])));
                decimal ResultApproveDay = ApproveCount + NewSystemApproveDay;
                string LeaveDayCountMessage = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveInformationMessage(Sicil, LeaveDayCount, ResultApproveDay);

                lblLeaveDayCount.Text = LeaveDayCount.ToString();
                //     lblLeaveDayCountMessage.Text = LeaveDayCountMessage;
                if (ResultApproveDay > 0)
                {
                    lblLeaveDayCount.Text = ((decimal)(LeaveDayCount)).ToString();
                    //LeaveDayCountMessage += " Onayda bekleyen izinleriniz izin kotanızdan düşürülmüştür. Bilgilerinize";
                }
                //  CommentPanel.Visible = false;

                DataTable DtbLeaveType = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveType(UserInformation.LoginObject.LoginId.ToString());
                DtbLeaveType.DefaultView.Sort = "leaveType";
                drpLeaveType.DataSource = DtbLeaveType;
                drpLeaveType.DataBind();
                DtbLeaveType = null;

                drpLeaveType.SelectedValue = YillikIzin;
            }
        }
        else
        {
            DisabledControl();
            ShowInformation(Resources.Resource.main_hata, Resources.Resource.izin_usertype_hata);
        }
    }

    /// <summary>
    /// Mevcut iş akışı yüklenir.
    /// </summary>
    public override void LoadEntityToControls()
    {
        LeaveRequest RequestObject = WFRepository<Digiturk.Workflow.Digiflow.Entities.LeaveRequest>.GetEntity(CurrentWfIns.EntityRefId);
        var lg = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDetailsOfLogin(RequestObject.OwnerLoginId);
        OrgTreeOwnerLoginId.ByPassDepartmentBinding = true;
        OrgTreeOwnerLoginId.SetValues(lg.DepartmentId, lg.DivisionId, lg.UnitId, lg.TeamId, "", false, lg.LoginId, true, true, lg.SubTeam_1_Id, lg.SubTeam_2_Id, lg.SubTeam_3_Id, lg.SubTeam_4_Id, lg.SubTeam_5_Id);
        OrgTreeOwnerLoginId.ManagerPanelVisible = false;
        OrgTreeOwnerLoginId.UserPanelVisible = true;

        DataTable DtbLeaveType = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveType();
        DtbLeaveType.DefaultView.Sort = "leaveType";
        drpLeaveType.DataSource = DtbLeaveType;
        drpLeaveType.DataBind();
        DtbLeaveType = null;


        //günlük izin ise
        if (RequestObject.LeaveDayCount != 0)
        {
            txtQuoate.Text = ConvertionHelper.ConvertValue<string>(RequestObject.LeaveDayCount);
            drpLeaveTimeHour.Visible = false;
            txtQuoate.Visible = true;
        }

        //saatlik izin ise
        if (RequestObject.LeaveHourCount != 0)
        {
            drpLeaveTimeHour.SelectedValue = ConvertionHelper.ConvertValue<string>(RequestObject.LeaveHourCount);
            drpLeaveTimeHour.SelectedItem.Value = ConvertionHelper.ConvertValue<string>(RequestObject.LeaveHourCount);
            drpLeaveTimeHour.Visible = true;
            txtQuoate.Visible = false;
            lblQuoate.Text = "Saat";
        }

        DtpStartTime.Text = RequestObject.StartTime.ToShortDateString();
        ChkIsStartHalfDay.Checked = ConvertionHelper.ConvertValue<bool>(RequestObject.IsStartHalfDay);
        DtpEndTime.Text = RequestObject.EndTime.ToShortDateString();
        ChkIsFinishHalfDay.Checked = ConvertionHelper.ConvertValue<bool>(RequestObject.IsFinishHalfDay);
        txtDecription.Text = ConvertionHelper.ConvertValue<string>(RequestObject.Decription);
        //drpLeaveType.SelectedValue = "0";
        chkNotDegelation.Checked = ConvertionHelper.ConvertValue<bool>(RequestObject.IsNotDelegation);

        DataTable dtDetail = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveRequestDetailInformation(RequestObject.RequestId);

        for (int i = 0; i < dtDetail.Rows.Count; i++)
        {
            drpLeaveType.SelectedValue = dtDetail.Rows[i]["leavecode"].ToString();
        }
        dtDetail = null;

        string Sicil = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetSicilName(CurrentWfIns.OwnerLogin.LoginId);
        Sicil = Sicil.PadLeft(5, '0');
        decimal LeaveDayCount = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveDayCount(Sicil);
        string LeaveDayCountMessage = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveInformationMessage(Sicil, 0, 0);
        lblLeaveDayCount.Text = LeaveDayCount.ToString();
        // lblLeaveDayCountMessage.Text = LeaveDayCountMessage;

        DataTable dtVardiyaDetail = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveVardiyaRequestDetailInformation(RequestObject.RequestId);

        Session["dtVardiyaDetail"] = null;

        if (dtVardiyaDetail.Rows.Count > 0)
        {
            Session["dtVardiyaDetail"] = dtVardiyaDetail;
            RepeaterVardiya.Visible = true;
            RepeaterVardiya.DataSource = dtVardiyaDetail;
            RepeaterVardiya.DataBind();
        }

        dtVardiyaDetail = null;

        #region kapatılmış

        //RptVacationDetail.DataSource = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveRequestDetailInformation(RequestObject.RequestId);
        //RptVacationDetail.DataBind();
        //chkNotDegelation.Checked = ConvertionHelper.ConvertValue<bool>(RequestObject.IsNotDelegation);
        //for (int i = 0; i < RptVacationDetail.Items.Count; i++)
        //{
        //    RangeValidator rvStartTime = (RangeValidator)RptVacationDetail.Items[i].FindControl("rvStartTimeDetail");
        //    rvStartTime.Enabled = false;
        //    RangeValidator rvEndTime = (RangeValidator)RptVacationDetail.Items[i].FindControl("rvDtpEndTimeDetail");
        //    rvEndTime.Enabled = false;
        //}
        //  CommentPanel.Enabled = true;

        #endregion kapatılmış

        WorkFlowDiagramResult result = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDiagramHelper.GetContentOfFlowDiagram(InstanceId, System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US");
        ((MasterPage)this.Master).SetContentOfFlowDiagram(result);
    }

    /// <summary>
    /// Kontrollerin içerisindeki veriler bind edilir.
    /// </summary>
    public override void LoadDataBinding()
    {
        rvDtpEndTime.MaximumValue = DateTime.MaxValue.ToShortDateString();
        rvDtpEndTime.MinimumValue = DateTime.MinValue.ToShortDateString();

        rvDtpStartTime.MaximumValue = DateTime.MaxValue.ToShortDateString();
        rvDtpStartTime.MinimumValue = DateTime.MinValue.ToShortDateString();
        

        ((MasterPage)this.Master).ShowMenu(true);
        ((MasterPage)this.Master).PageTitle = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetDefinitionName(Request.Url.Segments[Request.Url.Segments.Length - 1]);

        // Vardiyalı çalışan ise
        bool blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
        Session["SessionVardiya"] = blVardiya;

        if (blVardiya == false)
        {
            Literal4.Text = GetGlobalResourceObject("Resource", "izin_ise_baslama").ToString();
        }

        bool blF2F = VacationInformationHelper.F2FPersonelKontrol(UserInformation.LoginObject.LoginId.ToString());
        Session["SessionF2F"] = blF2F;
    }

    /// <summary>
    /// Tab Panelin Durumu denetlenir.
    /// </summary>
    /// <param name="newRequestTabPanelVisible"></param>
    /// <param name="approveRejectTabPanelVisible"></param>
    /// <param name="forwardTabPanelVisible"></param>
    /// <param name="suspendResumeTabPanelVisible"></param>
    /// <param name="abortTabPanelVisible"></param>
    /// <param name="addCommentTabPanelVisible"></param>
    /// <param name="rollbackTabPanelVisible"></param>
    public override void ChangeVisibilityOfTabs(bool newRequestTabPanelVisible, bool approveRejectTabPanelVisible, bool forwardTabPanelVisible, bool suspendResumeTabPanelVisible, bool abortTabPanelVisible, bool addCommentTabPanelVisible, bool rollbackTabPanelVisible, bool fileUploadTabPanelVisible, bool sendToCommentTabPanelVisible)
    {
        int ActiveTabIndex = 0;
        if (newRequestTabPanelVisible) ActiveTabIndex = 0;
        else if (approveRejectTabPanelVisible) ActiveTabIndex = 1;
        else if (forwardTabPanelVisible) ActiveTabIndex = 2;
        else if (sendToCommentTabPanelVisible) ActiveTabIndex = 3;
        else if (suspendResumeTabPanelVisible) ActiveTabIndex = 4;
        else if (abortTabPanelVisible) ActiveTabIndex = 5;
        else if (addCommentTabPanelVisible) ActiveTabIndex = 6;
        else if (rollbackTabPanelVisible) ActiveTabIndex = 7;
        else if (fileUploadTabPanelVisible) ActiveTabIndex = 8;
        ((MasterPage)this.Master).SetActiveTabIndex(ActiveTabIndex);
        ((MasterPage)this.Master).ShowActionsPanel(true);
        ((MasterPage)this.Master).ChangeVisibilityOfTabs
        (newRequestTabPanelVisible,
        approveRejectTabPanelVisible,
        forwardTabPanelVisible,
        suspendResumeTabPanelVisible,
        abortTabPanelVisible,
        addCommentTabPanelVisible,
        rollbackTabPanelVisible,
        fileUploadTabPanelVisible, 
        sendToCommentTabPanelVisible);
        ((MasterPage)this.Master).ChangeSuspendPanel(FlowIsSuspend);

        //CommentPanel.Visible = true;
        //CommentPanel.Enabled = true;
        //((MasterPage)this.Master).SetTabControlVisibility(true);
        //((MasterPage)this.Master).SetActiveTabIndex(6);
        //((MasterPage)this.Master).ChangeVisibilityOfTabs(false, false, false, false, true, false, true);
        //((MasterPage)this.Master).ShowActionsPanel(true);
    }

    /// <summary>
    /// Mesaj kutusu çıkartılır
    /// </summary>
    /// <param name="Title"></param>
    /// <param name="Message"></param>
    public override void ShowInformation(string Title, string Message)
    {
        ((MasterPage)this.Master).ShowInformation(Title, Message);
    }

    /// <summary>
    /// Başlangıç ve Bitiş Tarihi dolu ise gerekli izin günü hesaplamasını yapar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DtpStartTimeDetail_OnTextChanged(object sender, EventArgs e)
    {
        string strUlkeId = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.CalisanUlkeIdBul(UserInformation.LoginObject.DomainUserName.ToString());

        for (int i = 0; i < RptVacationDetail.Items.Count; i++)
        {
            TextBox txtStartTime = ((TextBox)RptVacationDetail.Items[i].FindControl("DtpStartTimeDetail"));
            TextBox txtEndTime = ((TextBox)RptVacationDetail.Items[i].FindControl("DtpEndTimeDetail"));
            TextBox txtResult = ((TextBox)RptVacationDetail.Items[i].FindControl("txtQuoate"));
            if (((CheckBox)RptVacationDetail.Items[i].FindControl("ChkLeaveType")).Checked)
            {
                if (txtStartTime.Text != "")
                {
                    if (txtEndTime.Text != "")
                    {
                        DateTime StartTime = ConvertionHelper.ConvertValue<DateTime>(txtStartTime.Text);
                        DateTime EndTime = ConvertionHelper.ConvertValue<DateTime>(txtEndTime.Text);
                        CheckBox ChkIsStartTimeDetail = ((CheckBox)RptVacationDetail.Items[i].FindControl("ChkIsStartTimeDetail"));
                        CheckBox ChkEndTimeDetail = ((CheckBox)RptVacationDetail.Items[i].FindControl("ChkEndTimeDetail"));
                        if (StartTime.DayOfWeek == DayOfWeek.Saturday || StartTime.DayOfWeek == DayOfWeek.Sunday)
                        {
                            ChkIsStartTimeDetail.Checked = false;
                            txtStartTime.Text = IsWeekendAndHolidayControl(StartTime, strUlkeId);
                            StartTime = ConvertionHelper.ConvertValue<DateTime>(txtStartTime.Text); // detay ile uyuşmazlıklar için eklendi
                        }
                        if (EndTime.DayOfWeek == DayOfWeek.Saturday || EndTime.DayOfWeek == DayOfWeek.Sunday)
                        {
                            ChkEndTimeDetail.Checked = false;
                            txtEndTime.Text = IsWeekendAndHolidayControl(EndTime, strUlkeId);
                            EndTime = ConvertionHelper.ConvertValue<DateTime>(txtEndTime.Text); // detay ile uyuşmazlıklar için eklendi
                        }

                        txtResult.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationLongCalculate
                             (StartTime, EndTime, strUlkeId, 0, drpLeaveType.SelectedValue).ToString();

                        if ((ChkIsStartTimeDetail.Checked || ChkEndTimeDetail.Checked) && (ChkIsStartTimeDetail.Checked != ChkEndTimeDetail.Checked))
                        {
                            //RequestObject.LeaveDayCount= RequestObject.LeaveDayCount-1/2;
                            decimal Result = 0;
                            if (ChkIsStartTimeDetail.Checked)
                            {
                                Result = decimal.Parse(txtResult.Text) - decimal.Parse("0,5");
                            }
                            else if (ChkEndTimeDetail.Checked)
                            {
                                Result = decimal.Parse(txtResult.Text) + decimal.Parse("0,5");
                            }
                            if (Result < 0)
                            {
                                Result = Result * -1;
                            }
                            //RequestObject.LeaveDayCount = decimal.Parse(RequestObject.LeaveDayCount.ToString()) - decimal.Parse("0,5");
                            txtResult.Text = Result.ToString();
                        }
                    }
                }
            }
        }
    }

    //bitiş tarihi cumartesi,pazar veya tatil seçilirse bitiş tarihi güncellenir
    public string WeekendAndHolidayControl(DateTime StartEndDate, string strUlkeId)
    {
        bool blVardiya = false;
        bool blF2F = false;

        if (Session["SessionVardiya"] == null)
        {
            blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
            Session["SessionVardiya"] = blVardiya;
        }
        else
        {
            blVardiya = ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]);
        }


        if (!blVardiya)
        {
            //if (StartEndDate.DayOfWeek == DayOfWeek.Saturday)
            //{
            //    StartEndDate = StartEndDate.AddDays(2);
            //}
            //else 
            if (StartEndDate.DayOfWeek == DayOfWeek.Sunday)
            {
                StartEndDate = StartEndDate.AddDays(1);
            }
            else
            {

            }
        }
        return StartEndDate.ToShortDateString();
    }

    public string IsWeekendAndHolidayControl(DateTime StartEndDate, string strUlkeId)
    {
        bool blVardiya = false;

        if (Session["SessionVardiya"] == null)
        {
            blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
            Session["SessionVardiya"] = blVardiya;
        }
        else
        {
            blVardiya = ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]);
        }

        if (!blVardiya)
        {
            //if (StartEndDate.DayOfWeek == DayOfWeek.Saturday)
            //{
            //    StartEndDate = StartEndDate.AddDays(2);
            //}
            //else 

            //if (StartEndDate.DayOfWeek == DayOfWeek.Sunday)
            //{
            //    StartEndDate = StartEndDate.AddDays(1);
            //}
            //1.10.2019
            //else
            //{

            //    if (VacationInformationHelper.IsResmiTatil(StartEndDate, strUlkeId))
            //    {
            //        do
            //        {
            //            StartEndDate = StartEndDate.AddDays(1);
            //        } while (VacationInformationHelper.IsResmiTatil(StartEndDate, strUlkeId));
            //    }
            //}

            //1.10.2019
            if (VacationInformationHelper.IsResmiTatil(StartEndDate, strUlkeId))
            {
                if (VacationInformationHelper.IsResmiTatilGun(StartEndDate, strUlkeId) != 0.5)
                {
                    do
                    {
                        StartEndDate = StartEndDate.AddDays(1);
                    } while (VacationInformationHelper.IsResmiTatil(StartEndDate, strUlkeId));
                }
            }
        }
        return StartEndDate.ToShortDateString();
    }

    //bitiş tarihi cumartesi ya da pazar seçilirse bitiş tarihi güncellenir
    public string WeekendControl(DateTime StartEndDate, string strUlkeId)
    {
        bool blVardiya = false;

        if (Session["SessionVardiya"] == null)
        {
            blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
            Session["SessionVardiya"] = blVardiya;
        }
        else
        {
            blVardiya = ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]);
        }

        if (!blVardiya)
        {
            //string str = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.WeekendCountryControl(strUlkeId);

            //if (StartEndDate.DayOfWeek == DayOfWeek.Saturday)
            //{
            //    StartEndDate = StartEndDate.AddDays(2);
            //}
            //else
            //{
            // StartEndDate = StartEndDate.AddDays(1);
            //}
        }
        return StartEndDate.ToShortDateString();
    }

    /// <summary>
    /// Repeater daki checkbox check li ise tarih seçilebilecek, değil ise seçilemeyecek
    /// </summary>
    private void ControlCheckedStateOfLeave()
    {
        for (int i = 0; i < RptVacationDetail.Items.Count; i++)
        {
            TextBox txtStartTime = ((TextBox)RptVacationDetail.Items[i].FindControl("DtpStartTimeDetail"));
            TextBox txtEndTime = ((TextBox)RptVacationDetail.Items[i].FindControl("DtpEndTimeDetail"));
            TextBox txtResult = ((TextBox)RptVacationDetail.Items[i].FindControl("txtQuoate"));
            if (((CheckBox)RptVacationDetail.Items[i].FindControl("ChkLeaveType")).Checked)
            {
                txtStartTime.Enabled = true;
                txtEndTime.Enabled = true;
            }
            else
            {
                txtStartTime.Enabled = false;
                txtEndTime.Enabled = false;
            }
        }
    }

    //
    protected void ChkLeaveType_OnCheckedChanged(object sender, EventArgs e)
    {
        ControlCheckedStateOfLeave();
    }

  
    protected void DtpStartTime_TextChanged(object sender, EventArgs e)
    {
        if (DtpStartTime.Text != "")
        {
            DateTime dtEndTime = new DateTime();
            DateTime dtStartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);

            if (DtpEndTime.Text != "")
            {
                dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
            }

            bool IzniKontrol = true;

            if (drpLeaveType.SelectedValue == KarneIzni) // z27.03.2025
            {
                IzniKontrol = KarneIzniKontrol();
            }
            else if (drpLeaveType.SelectedValue == OkulIlkGunIzni)
            {
                // OkulIlkGunAy

                IzniKontrol = OkulIlkGunIzniKontrol();
            }


            if ((dtEndTime > dtStartTime || dtEndTime == dtStartTime) && IzniKontrol)
            {
                // Vardiyalı çalışan ise
                bool blVardiya = false;

                if (Session["SessionVardiya"] != null)
                {
                    blVardiya = ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]);
                }
                else
                {
                    blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
                    Session["SessionVardiya"] = blVardiya;
                }

                if (drpLeaveType.SelectedValue != MazeretIzni)
                {
                    if (DtpEndTime.Text != "" && DtpStartTime.Text != "")
                    {                                                

                        if (blVardiya)// && (drpLeaveType.SelectedValue == YillikIzin || drpLeaveType.SelectedValue == DogumIzni  || drpLeaveType.SelectedValue == SutIzni || drpLeaveType.SelectedValue == UcretsizIzin || drpLeaveType.SelectedValue == "45"))
                        {
                            VardiyaDoldur();
                            GetCountDayVardiya();
                        }
                        else
                        {
                            GetCountDay(blVardiya, 0, 0, 0);
                        }
                    }
                }

                if (drpLeaveType.SelectedValue == MazeretIzni)
                {
                    if (!blVardiya)
                    {
                        string strUlkeId = VacationInformationHelper.CalisanUlkeIdBul(UserInformation.LoginObject.DomainUserName.ToString());

                        DtpStartTime.Text = IsWeekendAndHolidayControl(ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), strUlkeId);
                    }

                    DtpEndTime.Text = DtpStartTime.Text;

                }

                if (DtpEndTime.Text != "")
                {
                    dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
                }
                if (DtpStartTime.Text != "")
                {
                    dtStartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
                }               

            }
            else
            {
                txtQuoate.Text = "0";
                RepeaterVardiya.DataSource = null;
                RepeaterVardiya.DataBind();
            }
                      

            if (ChkIsFinishHalfDay.Checked)
            {
                if (dtStartTime == dtEndTime)
                {
                    lblIzinDurum.Visible = true;
                    lblIzinDurum.Text = dtStartTime + " öğleden sonra için izin doldurdunuz!!!";
                }
            }
            else if (ChkIsStartHalfDay.Checked)
            {
                if (dtStartTime == dtEndTime)
                {
                    lblIzinDurum.Visible = true;
                    lblIzinDurum.Text = dtStartTime + " öğleden önce için izin doldurdunuz!!!";
                }
                else
                {
                    lblIzinDurum.Visible = true;
                    lblIzinDurum.Text = dtStartTime + " öğleden sonra için izin doldurdunuz!!!";
                }
            }
        }
    }

    //
    protected void DtpEndTime_TextChanged(object sender, EventArgs e)
    {
        if (DtpEndTime.Text != "")
        {
            // Vardiyalı çalışan ise
            bool blVardiya = false;
            DateTime dtStartTime = new DateTime();
            DateTime dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);

            bool IzniKontrol = true;

            if (DtpStartTime.Text != "")
            {
                dtStartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
            }

            if (drpLeaveType.SelectedValue == KarneIzni) // z27.03.2025
            {
                IzniKontrol = KarneIzniKontrol();
            }
            else if (drpLeaveType.SelectedValue == OkulIlkGunIzni)
            {
                // OkulIlkGunAy

                IzniKontrol = OkulIlkGunIzniKontrol();
            }

            if ((dtEndTime > dtStartTime || dtEndTime == dtStartTime) && IzniKontrol)
            {
                Session["LeaveEndDate"] = DtpEndTime.Text;


                if (Session["SessionVardiya"] != null)
                {
                    blVardiya = ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]);
                }
                else
                {
                    blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
                }



                string strUlkeId = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.CalisanUlkeIdBul(UserInformation.LoginObject.DomainUserName.ToString());

                if (DtpEndTime.Text != "" && DtpStartTime.Text != "" && !blVardiya)
                {
                    if (dtEndTime.ToShortDateString() != "01.01.0001" && dtStartTime.ToShortDateString() != "01.01.0001" && dtEndTime.ToShortDateString() != "1.01.0001" && dtStartTime.ToShortDateString() != "1.01.0001")
                    {
                        GetCountDay(blVardiya, 0, 0, 0);
                    }
                }

                if (blVardiya) // && (drpLeaveType.SelectedValue == YillikIzin || drpLeaveType.SelectedValue == DogumIzni || drpLeaveType.SelectedValue == SutIzni || drpLeaveType.SelectedValue == UcretsizIzin || drpLeaveType.SelectedValue == "45"))
                {
                    VardiyaDoldur();
                    GetCountDayVardiya();
                }

                if (DtpEndTime.Text != "")
                {
                    dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
                }
                if (DtpStartTime.Text != "")
                {
                    dtStartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
                }
            }
            else
            {
                txtQuoate.Text = "0";
                RepeaterVardiya.DataSource = null;
                RepeaterVardiya.DataBind();
            }
    
            if (ChkIsFinishHalfDay.Checked)
            {
                if (dtStartTime == dtEndTime)
                {
                    lblIzinDurum.Visible = true;
                    lblIzinDurum.Text = dtStartTime + " öğleden sonra için izin doldurdunuz!!!";
                }
            }
            else if (ChkIsStartHalfDay.Checked) 
            {
                if (dtStartTime == dtEndTime)
                {
                    lblIzinDurum.Visible = true;
                    lblIzinDurum.Text = dtStartTime + " öğleden önce için izin doldurdunuz!!!";
                }
                else
                {
                    lblIzinDurum.Visible = true;
                    lblIzinDurum.Text = dtStartTime + " öğleden sonra için izin doldurdunuz!!!";
                }
            }
        }
    }

    //izin süresi hesaplama
    protected void GetCountDay(bool vardiya, decimal resmiCount, decimal haftalikCount, decimal izinCount)
    {       
        string strUlkeId = VacationInformationHelper.CalisanUlkeIdBul(UserInformation.LoginObject.DomainUserName.ToString());

        DateTime dtStartTime = new DateTime();
        DateTime dtEndTime = new DateTime();

        double leaveTime = 0;
        lblIzinDurum.Visible = false;

        if (strUlkeId == "1")
        {
            leaveTime = ConvertionHelper.ConvertValue<double>(VacationInformationHelper.DtbLeaveTypeLimit(drpLeaveType.SelectedValue));
            if (drpLeaveType.SelectedValue == IdariIzni)
            {
                //leaveTime = 1;
                leaveTime = 2;
            }
            else if (drpLeaveType.SelectedValue == KarneIzni)
            {
                leaveTime = 0.5;
                if(!vardiya)
                ChkIsStartHalfDay.Checked = true;
            }
        }

        if (DtpEndTime.Text != "")
        {
            dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
            dtStartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);

            if (dtEndTime > dtStartTime || dtEndTime == dtStartTime)
            {
                //
                if (ChkIsFinishHalfDay.Checked)
                {
                    if (dtStartTime == dtEndTime)
                    {
                        DtpEndTime.Text = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text).AddDays(1).ToShortDateString();
                        dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
                        ChkIsFinishHalfDay.Checked = false;
                        ChkIsStartHalfDay.Checked = true;
                    }
                }

                if (drpLeaveType.SelectedValue != RaporIzni && VacationInformationHelper.IsResmiTatil(dtEndTime, strUlkeId))
                {

                    if (dtEndTime.DayOfWeek == DayOfWeek.Sunday || dtEndTime.DayOfWeek == DayOfWeek.Saturday)
                    {
                        DtpEndTime.Text = IsWeekendAndHolidayControl(dtEndTime, strUlkeId);
                        dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
                    }
                    else if ((ChkIsFinishHalfDay.Checked && VacationInformationHelper.IsResmiTatilGun(dtEndTime, strUlkeId) == 0.5) || VacationInformationHelper.IsResmiTatilGun(dtEndTime, strUlkeId) > 0.5)
                    {
                        DtpEndTime.Text = IsWeekendAndHolidayControl(dtEndTime, strUlkeId);
                        dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
                    }

                }
            }
            //    

            Session["LeaveEndDate"] = DtpEndTime.Text;
        }

        if (DtpStartTime.Text != "")
        {
            dtStartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
            //babalık, rapor, evlilik
            if (drpLeaveType.SelectedValue != RaporIzni && (dtStartTime.DayOfWeek == DayOfWeek.Saturday || dtStartTime.DayOfWeek == DayOfWeek.Sunday))
            {
                DtpStartTime.Text = IsWeekendAndHolidayControl(dtStartTime, strUlkeId);
                dtStartTime = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);
            }
            else
            {
                if (ChkIsStartHalfDay.Checked && VacationInformationHelper.IsResmiTatilGun(dtStartTime, strUlkeId) == 0.5 && dtStartTime != dtEndTime)
                {
                    ChkIsStartHalfDay.Checked = false;
                }
                else if (ChkIsStartHalfDay.Checked && VacationInformationHelper.IsResmiTatilGun(dtStartTime, strUlkeId) == 0.5 && dtStartTime == dtEndTime)
                {
                    DtpEndTime.Text = IsWeekendAndHolidayControl(dtEndTime, strUlkeId);
                    dtEndTime = ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text);
                    ChkIsStartHalfDay.Checked = false;
                }
            }

            Session["LeaveStartDate"] = DtpStartTime.Text;
        }

        bool blF2F = false;

        if (Session["SessionF2F"] == null)
        {
            blF2F = VacationInformationHelper.F2FPersonelKontrol(UserInformation.LoginObject.LoginId.ToString());
        }
        else
        {
            blF2F = ConvertionHelper.ConvertValue<bool>(Session["SessionF2F"]);
        }

        bool KreaSakaryaSirket = false;
        string sirket = "";

        if (!blF2F)
        {
            sirket = ConvertionHelper.ConvertValue<string>(System.Configuration.ConfigurationManager.AppSettings["KreaSakaryaSirket"]);

            if (sirket == UzaktanCalismaHelper.CalisanSirket(UserInformation.LoginObject.LoginName.ToString()))
            {
                KreaSakaryaSirket = true;
            }
            else
            {
                KreaSakaryaSirket = false;
            }

            blF2F = KreaSakaryaSirket;

        }


        if (dtEndTime.ToShortDateString() != "01.01.0001" && dtEndTime.ToShortDateString() != "1.01.0001" && dtStartTime.ToShortDateString() != "1.01.0001" && dtStartTime.ToShortDateString() != "01.01.0001")
        {
            if (vardiya)
            {
                txtQuoate.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationLongCalculateVardiya(dtStartTime, dtEndTime, haftalikCount, izinCount, resmiCount, leaveTime, ChkIsStartHalfDay.Checked, ChkIsFinishHalfDay.Checked, drpLeaveType.SelectedValue, blF2F).ToString();
            }
            else
            {
                txtQuoate.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VacationLongCalculate(dtStartTime, dtEndTime, strUlkeId, leaveTime, ChkIsStartHalfDay.Checked, ChkIsFinishHalfDay.Checked, drpLeaveType.SelectedValue).ToString();
            }

            #region kapatıldı

            //if ((ChkIsStartHalfDay.Checked || ChkIsFinishHalfDay.Checked) && (ChkIsStartHalfDay.Checked != ChkIsFinishHalfDay.Checked))
            //{
            //    decimal Result = 0;
            //    if (ChkIsStartHalfDay.Checked)
            //    {
            //        if (vardiya)
            //        {
            //            Result = decimal.Parse(txtQuoate.Text);
            //        }
            //        else
            //        {
            //            Result = decimal.Parse(txtQuoate.Text) - decimal.Parse("0,5");
            //        }
            //    }
            //    else if (ChkIsFinishHalfDay.Checked)
            //    {
            //        Result = decimal.Parse(txtQuoate.Text) + decimal.Parse("0,5");
            //    }
            //    if (Result < 0)
            //    {
            //        Result = Result * -1;
            //    }
            //    txtQuoate.Text = Result.ToString();
            //}

            #endregion kapatıldı
        }

        if (leaveTime != 0)
        {
            if (txtQuoate.Text != "")
            {
                if (ConvertionHelper.ConvertValue<double>(txtQuoate.Text) > leaveTime || (ConvertionHelper.ConvertValue<double>(txtQuoate.Text) < leaveTime && (drpLeaveType.SelectedValue == BabalikIzni || drpLeaveType.SelectedValue == EvlilikIzni || drpLeaveType.SelectedValue == VefatIzni || drpLeaveType.SelectedValue == MensturalIzin || drpLeaveType.SelectedValue == OkulIlkGunIzni)))
                {
                    // DtpEndTime.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.AddWorkdays(ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text), ConvertionHelper.ConvertValue<int>(leaveTime)).ToShortDateString();
                    //Babalık ve evlilik iznine ek yapılmayacak
                    if (!vardiya)// && drpLeaveType.SelectedValue != BabalikIzni && drpLeaveType.SelectedValue != EvlilikIzni && drpLeaveType.SelectedValue != VefatIzni)
                    {
                        DataTable dtTableDate = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.AddWorkdays(ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text), ConvertionHelper.ConvertValue<int>(leaveTime));

                        if (dtTableDate.Rows.Count > 0)
                        {
                            DtpEndTime.Text = ConvertionHelper.ConvertValue<DateTime>(dtTableDate.Rows[0]["TempDate"].ToString()).ToShortDateString();

                            if (ChkIsFinishHalfDay.Checked != ChkIsStartHalfDay.Checked)
                            {
                                ChkIsFinishHalfDay.Checked = false;
                                ChkIsStartHalfDay.Checked = false;
                            }
                            if (dtTableDate.Rows[0]["YarimGun"].ToString() == "1")
                            {
                                ChkIsFinishHalfDay.Checked = true;
                            }
                        }
                        else
                        {
                            DtpEndTime.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.AddWorkdays(ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<int>(leaveTime)).ToShortDateString();


                        }
                        dtTableDate = null;
                    }
                    else
                    {
                        //if (!vardiya && (drpLeaveType.SelectedValue == BabalikIzni || drpLeaveType.SelectedValue == EvlilikIzni || drpLeaveType.SelectedValue == VefatIzni))
                        //{
                        //    DtpEndTime.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.Adddays(ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<int>(leaveTime)).ToShortDateString();
                        //}
                        //else if (vardiya && !blF2F && (drpLeaveType.SelectedValue == BabalikIzni || drpLeaveType.SelectedValue == EvlilikIzni || drpLeaveType.SelectedValue == VefatIzni))
                        //{
                        //    DtpEndTime.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.Adddays(ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), ConvertionHelper.ConvertValue<int>(leaveTime)).ToShortDateString();

                        //    VardiyaDoldur();
                        //}

                    }

                    //  ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).AddDays(leaveTime).ToShortDateString();
                    Session["LeaveEndDate"] = DtpEndTime.Text;


                    if (vardiya)
                    {
                        //if ((ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text) - ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text)).Days > leaveTime)
                        //{
                        //    txtQuoate.Text = "0";
                        //}
                        //if (drpLeaveType.SelectedValue == BabalikIzni || drpLeaveType.SelectedValue == EvlilikIzni || drpLeaveType.SelectedValue == VefatIzni)
                        //{
                        //    txtQuoate.Text = leaveTime.ToString();
                        //}

                    }
                    else
                    {
                        txtQuoate.Text = leaveTime.ToString();
                    }


                    ((MasterPage)Master).ShowPopup(false, "Uyarı", drpLeaveType.SelectedItem.Text + " kotası " + leaveTime + " gündür, " + leaveTime + " gün izin girebilirsiniz.", true, "");
                }
            }
        }
    }


    //
    public DataTable GetResultsTable(decimal vardiyaCount)
    {
        // Create the output table.
        DataTable d = new DataTable();
        d.Columns.Add("VDATE");
        d.Columns.Add("VDAY");
        d.Columns.Add("VTYPE");
        d.Columns.Add("ISHALFDAY");
        DateTime vardiyaT = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text);

        var culture = new System.Globalization.CultureInfo("tr-TR");

        // Loop through all process names.
        for (int i = 0; i < vardiyaCount; i++)
        {
            d.Rows.Add();
            d.Rows[i][0] = vardiyaT.ToShortDateString();
            d.Rows[i][1] = culture.DateTimeFormat.GetDayName(vardiyaT.DayOfWeek);
            d.Rows[i][2] = "1";
            d.Rows[i][3] = "false";

            vardiyaT = vardiyaT.AddDays(1);
        }
        return d;
    }

    //Vardiyalı çalışan tarih aralığı seçtiğinde
    protected void RepeaterVardiya_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        if (e.Item.ItemType != ListItemType.Header & e.Item.ItemType != ListItemType.Footer)
        {
            for (int i = 0; i <= e.Item.Controls.Count - 1; i++)
            {
                System.Web.UI.WebControls.Literal obLiteral = e.Item.Controls[i] as System.Web.UI.WebControls.Literal;
                if (obLiteral != null)
                {
                    if (obLiteral.ID == "litRowStart")
                    {
                        lCurrentRecord += 1;
                        if ((lCurrentRecord == 1))
                        {
                            obLiteral.Text = "<tr>";
                        }
                        break;
                    }
                }
            }
            for (int i = 0; i <= e.Item.Controls.Count - 1; i++)
            {
                System.Web.UI.WebControls.Literal obLiteral = e.Item.Controls[i] as System.Web.UI.WebControls.Literal;
                if (obLiteral != null)
                {
                    if (obLiteral.ID == "litRowEnd")
                    {
                        if (lCurrentRecord % lRecordsPerRow == 0)
                        {
                            obLiteral.Text = "</tr>";
                            lCurrentRecord = 0;
                        }
                        break;
                    }
                }
            }

            if (CurrentWfIns == null)
            {

                if (RepeaterVardiya.Items.Count > 0)
                {
                    //if (e.Item.ItemIndex == 0)
                    //{
                    if (drpLeaveType.SelectedValue == OkulIlkGunIzni || drpLeaveType.SelectedValue == MensturalIzin || drpLeaveType.SelectedValue == BabalikIzni || drpLeaveType.SelectedValue == EvlilikIzni || drpLeaveType.SelectedValue == VefatIzni)
                    {
                        ((CheckBox)e.Item.FindControl("ChkIsVardiyaHD")).Enabled = false;
                        ((CheckBox)RepeaterVardiya.Items[0].FindControl("ChkIsVardiyaHD")).Enabled = false;
                    }
                    else
                    {

                        if (ChkIsStartHalfDay.Checked)
                        {
                            ((CheckBox)RepeaterVardiya.Items[0].FindControl("ChkIsVardiyaHD")).Enabled = false;
                            ((CheckBox)RepeaterVardiya.Items[0].FindControl("ChkIsVardiyaHD")).Checked = true;
                        }
                        else
                        {
                            ((CheckBox)RepeaterVardiya.Items[0].FindControl("ChkIsVardiyaHD")).Enabled = true;
                            ((CheckBox)RepeaterVardiya.Items[0].FindControl("ChkIsVardiyaHD")).Checked = false;
                        }
                    }
                    //}
                }
                else
                {
                    if (ChkIsStartHalfDay.Checked)
                    {
                        ((CheckBox)e.Item.FindControl("ChkIsVardiyaHD")).Enabled = false;
                        ((CheckBox)e.Item.FindControl("ChkIsVardiyaHD")).Checked = true;
                    }
                    else
                    {
                        ((CheckBox)e.Item.FindControl("ChkIsVardiyaHD")).Enabled = true;
                        ((CheckBox)e.Item.FindControl("ChkIsVardiyaHD")).Checked = false;
                    }
                }
            }
        }
    }

    
    protected void drpLeaveTypeR_SelectedIndexChanged(object sender, EventArgs e)
    {
        GetCountDayVardiya();
    }

    //
    protected void ChkIsVardiyaHD_CheckedChanged(object sender, EventArgs e)
    {
        GetCountDayVardiya();
    }

    //
    protected void GetCountDayVardiya()
    {
        bool blVardiya = false;

        if (Session["SessionVardiya"] != null)
        {
            blVardiya = ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]);
        }
        else
        {
            blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
        }

        if (blVardiya)
        {
            for (int i = 0; i < RepeaterVardiya.Items.Count; i++)
            {
                if (((DropDownList)RepeaterVardiya.Items[i].FindControl("drpLeaveTypeR")).SelectedValue == "2") //resmi
                {
                    resmiCount = resmiCount + 1;
                    if (((CheckBox)RepeaterVardiya.Items[i].FindControl("ChkIsVardiyaHD")).Checked)
                    {
                        resmiCount = resmiCount - decimal.Parse("0,5");
                        izinCount = izinCount + decimal.Parse("0,5");
                    }
                }
                else if (((DropDownList)RepeaterVardiya.Items[i].FindControl("drpLeaveTypeR")).SelectedValue == "3") // haftalik
                {
                    haftalikCount = haftalikCount + 1;

                    if (((CheckBox)RepeaterVardiya.Items[i].FindControl("ChkIsVardiyaHD")).Checked)
                    {
                        haftalikCount = haftalikCount - decimal.Parse("0,5");
                    }
                }
                else
                {
                    // izin
                    izinCount = izinCount + 1;
                    if (i != 0)
                    {
                        if (((CheckBox)RepeaterVardiya.Items[i].FindControl("ChkIsVardiyaHD")).Checked)
                        {
                            izinCount = izinCount - decimal.Parse("0,5");
                        }
                    }
                    else
                    {
                        if (!ChkIsStartHalfDay.Checked)
                        {
                            if (((CheckBox)RepeaterVardiya.Items[i].FindControl("ChkIsVardiyaHD")).Checked)
                            {
                                izinCount = izinCount - decimal.Parse("0,5");
                            }
                        }
                    }

                    //if (ChkIsStartHalfDay.Checked && (((CheckBox)RepeaterVardiya.Items[0].FindControl("ChkIsVardiyaHD")).Checked) && i == 0)
                    //{
                    // izinCount = izinCount - decimal.Parse("0,5");
                    //}
                }
            }
        }

        //if (ChkIsFinishHalfDay.Checked)
        //{
        //    izinCount = izinCount + decimal.Parse("0,5");
        //}

        GetCountDay(blVardiya, resmiCount, haftalikCount, izinCount);
    }

    //vardiyalı izn türü
    protected void drpLeaveTypeR_Load(object sender, EventArgs e)
    {
        DropDownList drpSender = (DropDownList)sender;
        drpSender.Items[0].Attributes.Add("style", "color:Black");
        drpSender.Items[1].Attributes.Add("style", "color:Red");
        drpSender.Items[2].Attributes.Add("style", "color:Green");
    }

    //izin türü değiştirildiğinde
    protected void drpLeaveType_SelectedIndexChanged(object sender, EventArgs e)
    {
        // Vardiyalı çalışan ise
        bool blVardiya = false;

        if (Session["SessionVardiya"] != null)
        {
            blVardiya = ConvertionHelper.ConvertValue<bool>(Session["SessionVardiya"]);
        }
        else
        {
            blVardiya = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.VardiyaKontrol(UserInformation.LoginObject.LoginId.ToString());
            Session["SessionVardiya"] = blVardiya;
        }

        if (!blVardiya)
        {
            string strUlkeId = VacationInformationHelper.CalisanUlkeIdBul(UserInformation.LoginObject.DomainUserName.ToString());

            if (DtpStartTime.Text != "")
            {
                DtpStartTime.Text = IsWeekendAndHolidayControl(ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text), strUlkeId);
            }
        }

        if (drpLeaveType.SelectedValue == MazeretIzni)
        {            

            lblQuoate.Text = "Saat";
            DtpEndTime.Enabled = false;
            DtpEndTime.Text = DtpStartTime.Text;
            ChkIsStartHalfDay.Enabled = false;
            ChkIsFinishHalfDay.Enabled = false;
            ChkIsStartHalfDay.Checked = false;
            ChkIsFinishHalfDay.Checked = false;
            txtQuoate.Visible = false;
            drpLeaveTimeHour.Visible = true;
            lblMazeret.Visible = true;

            if (drpLeaveType.SelectedValue != YillikIzin && RepeaterVardiya.Items.Count > 0)
            {
                RepeaterVardiya.DataSource = null;
                RepeaterVardiya.DataBind();
            }
            else
            {
                VardiyaDoldur();
            }
        }
        else if (drpLeaveType.SelectedValue == KarneIzni) // z27.03.2025
        {
            KarneIzniKontrol();
            
        }
        else if (drpLeaveType.SelectedValue == OkulIlkGunIzni)
        {
            // OkulIlkGunAy

            OkulIlkGunIzniKontrol();

        }
        else
        {
            lblMazeret.Visible = false;
            //doğum izni, ücretsiz izin doğum izni, bedelli askerlik, covid-19 icretsiz izin
            if (drpLeaveType.SelectedValue == DogumIzni || drpLeaveType.SelectedValue == UcretsizIzin || drpLeaveType.SelectedValue == UcretsizDogumIzni || drpLeaveType.SelectedValue == BedelliAskerlikIzni || drpLeaveType.SelectedValue == CovidUcretsizIzni)
            {
                ((MasterPage)Master).ShowPopup(false, "Uyarı", "Seçmiş olduğunuz izin tipini kullanabilmek için İnsan Kaynakları ile iletişime geçiniz.", true, "");
                DtpEndTime.Enabled = false;
                DtpStartTime.Enabled = false;
                ChkIsStartHalfDay.Enabled = false;
                ChkIsFinishHalfDay.Enabled = false;
                txtDecription.Enabled = false;
                txtQuoate.Enabled = false;
                txtQuoate.Text = "";
                DtpEndTime.Text = "";
                DtpStartTime.Text = "";

            }
            else
            {
                if (drpLeaveType.SelectedValue == SutIzni)
                {
                    ((MasterPage)Master).ShowPopup(false, "Uyarı", "İzin günü tarih aralıklarını belirlemek üzere İnsan Kaynakları departmanına uğramanızı rica ederiz.", true, "");
                }
                //Rapor izin
                if (drpLeaveType.SelectedValue == RaporIzni)
                {
                    // ((MasterPage)Master).ShowPopup(false, "Uyarı", "Raporunuzu İnsan Kaynaklarına teslim etmenizi rica ederiz!", true, "");
                }

                if (drpLeaveType.SelectedValue == BabalikIzni || drpLeaveType.SelectedValue == VefatIzni || drpLeaveType.SelectedValue == EvlilikIzni || drpLeaveType.SelectedValue == MensturalIzin || drpLeaveType.SelectedValue == OkulIlkGunIzni)
                {
                    ChkIsStartHalfDay.Enabled = false;
                    ChkIsFinishHalfDay.Enabled = false;
                    ChkIsStartHalfDay.Checked = false;
                    ChkIsFinishHalfDay.Checked = false;
                }
                else
                {
                    ChkIsStartHalfDay.Enabled = true;
                    ChkIsFinishHalfDay.Enabled = true;
                }

                GetCountDay(blVardiya, 0, 0, 0);
                lblQuoate.Text = "Gün";
                DtpStartTime.Enabled = true;
                DtpEndTime.Enabled = true;
                txtQuoate.Visible = true;
                drpLeaveTimeHour.Visible = false;
                txtDecription.Enabled = true;
                txtQuoate.Enabled = true;

            }

            if (blVardiya)
            {                
                VardiyaDoldur();
            }
        }
    }

    public void VardiyaDoldur()
    {
        if (DtpEndTime.Text != "" && DtpStartTime.Text != "" && DtpEndTime.Text != DtpStartTime.Text)
        {
            decimal vardiyaCount = ConvertionHelper.ConvertValue<decimal>((ConvertionHelper.ConvertValue<DateTime>(DtpEndTime.Text) - ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text)).Days.ToString());

            if (vardiyaCount == 0)
            {
                vardiyaCount = 1;
            }
            RepeaterVardiya.DataSource = GetResultsTable(vardiyaCount);
            RepeaterVardiya.DataBind();
        }
        else
        {
            RepeaterVardiya.DataSource = null;
            RepeaterVardiya.DataBind();
        }
    }

    public bool KarneIzniKontrol()
    {
        bool isKarneAy = true;

        if (drpLeaveType.SelectedValue == KarneIzni) // z27.03.2025
        {
            if (DtpStartTime.Text != "")
            {
                string StartTimeM = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Month.ToString();

                if (ConfigurationManager.AppSettings["BirinciDonemKarneAy"] != StartTimeM && ConfigurationManager.AppSettings["IkinciDonemKarneAy"] != StartTimeM)
                {
                    isKarneAy = false;
                    ((MasterPage)Master).ShowPopup(false, "Uyarı", "Seçmiş olduğunuz izin tipinde Eğitim takvimindeki Karne dönemi baz alınır. Seçtiğiniz tarih buna uygun değil, tekrar deneyiniz!  ", true, "");

                    txtQuoate.Text = "0";
                }
            }
        }
        return isKarneAy;
    }

    public bool OkulIlkGunIzniKontrol()
    {
        bool isOkulIlkGunAy = true;

        if (drpLeaveType.SelectedValue == OkulIlkGunIzni)
        {
            // OkulIlkGunAy

            if (DtpStartTime.Text != "")
            {
                string StartTimeM = ConvertionHelper.ConvertValue<DateTime>(DtpStartTime.Text).Month.ToString();

                if (ConfigurationManager.AppSettings["OkulIlkGunAy"] != StartTimeM)
                {
                     isOkulIlkGunAy = false;

                    ((MasterPage)Master).ShowPopup(false, "Uyarı", "Seçmiş olduğunuz izin tipinde Eğitim takvimindeki Okul açılış dönemi baz alınır. Seçtiğiniz tarih buna uygun değil, tekrar deneyiniz!  ", true, "");


                    txtQuoate.Text = "0";
                }
            }

        }

        return isOkulIlkGunAy;
    }

    public bool CarsambaKontrol()
    {
        bool izinkontrol = true;
        string HaftaNo = "0";
        string HaftaNoList = "";
        List<string> HaftaNoList2 = new List<string>();

        List<VacationInformationHelper.DateTimeHafta> newLeaves = new List<VacationInformationHelper.DateTimeHafta>();
        DataTable dtHafta = VacationInformationHelper.GetHafta(DtpStartTime.Text, DtpEndTime.Text);
        if (dtHafta.Rows.Count > 0)
            newLeaves = VacationInformationHelper.ConvertToDateTimeList(dtHafta);
        {
            for (int i = 0; i < dtHafta.Rows.Count; i++)
            {
                if (HaftaNo != dtHafta.Rows[i]["HAFTA_NUMARASI"].ToString())
                {
                    if (HaftaNo != "0")
                    {
                        HaftaNoList = HaftaNoList + ",";
                    }
                    HaftaNoList = HaftaNoList + dtHafta.Rows[i]["HAFTA_NUMARASI"].ToString();
                    HaftaNo = dtHafta.Rows[i]["HAFTA_NUMARASI"].ToString();
                }
            }
        }
        string izinSure = "0";

        DataTable dtIzinHafta = VacationInformationHelper.GetIzinHafta(DtpStartTime.Text, DtpEndTime.Text, HaftaNoList, UserInformation.LoginObject.LoginId.ToString());
        List<VacationInformationHelper.DateTimeHafta> existingLeaves = new List<VacationInformationHelper.DateTimeHafta>();
        if (dtIzinHafta.Rows.Count > 0)
        {
            existingLeaves = VacationInformationHelper.ConvertToDateTimeList(dtIzinHafta);
            izinSure = VacationInformationHelper.GetIzinHaftaSure(DtpStartTime.Text, DtpEndTime.Text, HaftaNoList, UserInformation.LoginObject.LoginId.ToString());
        }
        dtIzinHafta = null;

        
        // 3) Kurala göre doğrula
        if (!VacationInformationHelper.IsValidLeaveRequest(existingLeaves, newLeaves, UserInformation.LoginObject.LoginId.ToString(), izinSure, txtQuoate.Text))
        {
            // lblMessage.Text = "Pazartesi ve Salı zaten izinli. Çarşamba olmadan Perşembe veya Cuma izin alınamaz.";
           // ((MasterPage)Master).ShowPopup(false, "Uyarı", "Lütfen çalışan ilişkileri ve bordro işlemleri müdürlüğü ile iletişime geçiniz IS HR PAYROLL TEAM <<EMAIL>>", false, ""); throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Çarşamba günü dahil olmalı.");

            izinkontrol = false;

        }

        return izinkontrol;
    }
}