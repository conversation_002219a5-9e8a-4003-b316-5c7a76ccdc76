﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Digiturk.Services.AvansAktar {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Data;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="PersonelBilgisiSoap", Namespace="http://tempuri.org/")]
    public partial class PersonelBilgisi : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback KisiBilgileriGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback OrganizasyonelBilgileriGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback IletisimBilgileriGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback OgrenimBilgileriGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback OgrenimBilgileriEkleGuncelleOperationCompleted;
        
        private System.Threading.SendOrPostCallback OgrenimBilgileriSilOperationCompleted;
        
        private System.Threading.SendOrPostCallback EskiIsYeriBilgileriGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EskiIsYeriBilgileriEkleGuncelleOperationCompleted;
        
        private System.Threading.SendOrPostCallback KursSeminerBilgileriGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback KursSeminerBilgileriEkleGuncelleOperationCompleted;
        
        private System.Threading.SendOrPostCallback IzinBilgileriGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback IzinBilgileriEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback IzinBilgileriSilOperationCompleted;
        
        private System.Threading.SendOrPostCallback OzelSigortaBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelYeniGirisEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback RosterBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback BolumBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback DepartmanBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback GorevBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback UnvanBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback LokasyonBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback IsAilesiBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback SirketBilgileriListeleOperationCompleted;
        
        private System.Threading.SendOrPostCallback AvansAktarOperationCompleted;
        
        private System.Threading.SendOrPostCallback ARGEIzinRaporuOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelTitleBilgiOperationCompleted;
        
        private System.Threading.SendOrPostCallback ParamTitleListeOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelAcilDurumListeOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelAcilDurumGuncelleOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelAcilDurumSilOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelCocukListeOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelCocukGuncelleOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelCocukSilOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelEsListeOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelEsGuncelleOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelEsSilOperationCompleted;
        
        private System.Threading.SendOrPostCallback TahsilOkulParametresiGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback TahsilFakulteParametresiGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback TahsilOkulTuruParametresiGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback TahsilOkulBolumParametresiGetirOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public PersonelBilgisi() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_IseGirisPoldy_PersonelBilgisi;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event KisiBilgileriGetirCompletedEventHandler KisiBilgileriGetirCompleted;
        
        /// <remarks/>
        public event OrganizasyonelBilgileriGetirCompletedEventHandler OrganizasyonelBilgileriGetirCompleted;
        
        /// <remarks/>
        public event IletisimBilgileriGetirCompletedEventHandler IletisimBilgileriGetirCompleted;
        
        /// <remarks/>
        public event OgrenimBilgileriGetirCompletedEventHandler OgrenimBilgileriGetirCompleted;
        
        /// <remarks/>
        public event OgrenimBilgileriEkleGuncelleCompletedEventHandler OgrenimBilgileriEkleGuncelleCompleted;
        
        /// <remarks/>
        public event OgrenimBilgileriSilCompletedEventHandler OgrenimBilgileriSilCompleted;
        
        /// <remarks/>
        public event EskiIsYeriBilgileriGetirCompletedEventHandler EskiIsYeriBilgileriGetirCompleted;
        
        /// <remarks/>
        public event EskiIsYeriBilgileriEkleGuncelleCompletedEventHandler EskiIsYeriBilgileriEkleGuncelleCompleted;
        
        /// <remarks/>
        public event KursSeminerBilgileriGetirCompletedEventHandler KursSeminerBilgileriGetirCompleted;
        
        /// <remarks/>
        public event KursSeminerBilgileriEkleGuncelleCompletedEventHandler KursSeminerBilgileriEkleGuncelleCompleted;
        
        /// <remarks/>
        public event IzinBilgileriGetirCompletedEventHandler IzinBilgileriGetirCompleted;
        
        /// <remarks/>
        public event IzinBilgileriEkleCompletedEventHandler IzinBilgileriEkleCompleted;
        
        /// <remarks/>
        public event IzinBilgileriSilCompletedEventHandler IzinBilgileriSilCompleted;
        
        /// <remarks/>
        public event OzelSigortaBilgileriListeleCompletedEventHandler OzelSigortaBilgileriListeleCompleted;
        
        /// <remarks/>
        public event PersonelYeniGirisEkleCompletedEventHandler PersonelYeniGirisEkleCompleted;
        
        /// <remarks/>
        public event PersonelBilgileriListeleCompletedEventHandler PersonelBilgileriListeleCompleted;
        
        /// <remarks/>
        public event RosterBilgileriListeleCompletedEventHandler RosterBilgileriListeleCompleted;
        
        /// <remarks/>
        public event BolumBilgileriListeleCompletedEventHandler BolumBilgileriListeleCompleted;
        
        /// <remarks/>
        public event DepartmanBilgileriListeleCompletedEventHandler DepartmanBilgileriListeleCompleted;
        
        /// <remarks/>
        public event GorevBilgileriListeleCompletedEventHandler GorevBilgileriListeleCompleted;
        
        /// <remarks/>
        public event UnvanBilgileriListeleCompletedEventHandler UnvanBilgileriListeleCompleted;
        
        /// <remarks/>
        public event LokasyonBilgileriListeleCompletedEventHandler LokasyonBilgileriListeleCompleted;
        
        /// <remarks/>
        public event IsAilesiBilgileriListeleCompletedEventHandler IsAilesiBilgileriListeleCompleted;
        
        /// <remarks/>
        public event SirketBilgileriListeleCompletedEventHandler SirketBilgileriListeleCompleted;
        
        /// <remarks/>
        public event AvansAktarCompletedEventHandler AvansAktarCompleted;
        
        /// <remarks/>
        public event ARGEIzinRaporuCompletedEventHandler ARGEIzinRaporuCompleted;
        
        /// <remarks/>
        public event PersonelTitleBilgiCompletedEventHandler PersonelTitleBilgiCompleted;
        
        /// <remarks/>
        public event ParamTitleListeCompletedEventHandler ParamTitleListeCompleted;
        
        /// <remarks/>
        public event PersonelAcilDurumListeCompletedEventHandler PersonelAcilDurumListeCompleted;
        
        /// <remarks/>
        public event PersonelAcilDurumGuncelleCompletedEventHandler PersonelAcilDurumGuncelleCompleted;
        
        /// <remarks/>
        public event PersonelAcilDurumSilCompletedEventHandler PersonelAcilDurumSilCompleted;
        
        /// <remarks/>
        public event PersonelCocukListeCompletedEventHandler PersonelCocukListeCompleted;
        
        /// <remarks/>
        public event PersonelCocukGuncelleCompletedEventHandler PersonelCocukGuncelleCompleted;
        
        /// <remarks/>
        public event PersonelCocukSilCompletedEventHandler PersonelCocukSilCompleted;
        
        /// <remarks/>
        public event PersonelEsListeCompletedEventHandler PersonelEsListeCompleted;
        
        /// <remarks/>
        public event PersonelEsGuncelleCompletedEventHandler PersonelEsGuncelleCompleted;
        
        /// <remarks/>
        public event PersonelEsSilCompletedEventHandler PersonelEsSilCompleted;
        
        /// <remarks/>
        public event TahsilOkulParametresiGetirCompletedEventHandler TahsilOkulParametresiGetirCompleted;
        
        /// <remarks/>
        public event TahsilFakulteParametresiGetirCompletedEventHandler TahsilFakulteParametresiGetirCompleted;
        
        /// <remarks/>
        public event TahsilOkulTuruParametresiGetirCompletedEventHandler TahsilOkulTuruParametresiGetirCompleted;
        
        /// <remarks/>
        public event TahsilOkulBolumParametresiGetirCompletedEventHandler TahsilOkulBolumParametresiGetirCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/KisiBilgileriGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet KisiBilgileriGetir(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("KisiBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void KisiBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil) {
            this.KisiBilgileriGetirAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void KisiBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.KisiBilgileriGetirOperationCompleted == null)) {
                this.KisiBilgileriGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnKisiBilgileriGetirOperationCompleted);
            }
            this.InvokeAsync("KisiBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.KisiBilgileriGetirOperationCompleted, userState);
        }
        
        private void OnKisiBilgileriGetirOperationCompleted(object arg) {
            if ((this.KisiBilgileriGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.KisiBilgileriGetirCompleted(this, new KisiBilgileriGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/OrganizasyonelBilgileriGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet OrganizasyonelBilgileriGetir(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("OrganizasyonelBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void OrganizasyonelBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil) {
            this.OrganizasyonelBilgileriGetirAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void OrganizasyonelBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.OrganizasyonelBilgileriGetirOperationCompleted == null)) {
                this.OrganizasyonelBilgileriGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOrganizasyonelBilgileriGetirOperationCompleted);
            }
            this.InvokeAsync("OrganizasyonelBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.OrganizasyonelBilgileriGetirOperationCompleted, userState);
        }
        
        private void OnOrganizasyonelBilgileriGetirOperationCompleted(object arg) {
            if ((this.OrganizasyonelBilgileriGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OrganizasyonelBilgileriGetirCompleted(this, new OrganizasyonelBilgileriGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IletisimBilgileriGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet IletisimBilgileriGetir(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("IletisimBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void IletisimBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil) {
            this.IletisimBilgileriGetirAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void IletisimBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.IletisimBilgileriGetirOperationCompleted == null)) {
                this.IletisimBilgileriGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnIletisimBilgileriGetirOperationCompleted);
            }
            this.InvokeAsync("IletisimBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.IletisimBilgileriGetirOperationCompleted, userState);
        }
        
        private void OnIletisimBilgileriGetirOperationCompleted(object arg) {
            if ((this.IletisimBilgileriGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.IletisimBilgileriGetirCompleted(this, new IletisimBilgileriGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/OgrenimBilgileriGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet OgrenimBilgileriGetir(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("OgrenimBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void OgrenimBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil) {
            this.OgrenimBilgileriGetirAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void OgrenimBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.OgrenimBilgileriGetirOperationCompleted == null)) {
                this.OgrenimBilgileriGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOgrenimBilgileriGetirOperationCompleted);
            }
            this.InvokeAsync("OgrenimBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.OgrenimBilgileriGetirOperationCompleted, userState);
        }
        
        private void OnOgrenimBilgileriGetirOperationCompleted(object arg) {
            if ((this.OgrenimBilgileriGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OgrenimBilgileriGetirCompleted(this, new OgrenimBilgileriGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/OgrenimBilgileriEkleGuncelle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int OgrenimBilgileriEkleGuncelle(string kul_adi, string kul_sifre, string sicil, int idno, string okul_kodu, string fakulte_kodu, string bolum_kodu, short giris_yili, short cikis_yili, string okul_tur_kodu) {
            object[] results = this.Invoke("OgrenimBilgileriEkleGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno,
                        okul_kodu,
                        fakulte_kodu,
                        bolum_kodu,
                        giris_yili,
                        cikis_yili,
                        okul_tur_kodu});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void OgrenimBilgileriEkleGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int idno, string okul_kodu, string fakulte_kodu, string bolum_kodu, short giris_yili, short cikis_yili, string okul_tur_kodu) {
            this.OgrenimBilgileriEkleGuncelleAsync(kul_adi, kul_sifre, sicil, idno, okul_kodu, fakulte_kodu, bolum_kodu, giris_yili, cikis_yili, okul_tur_kodu, null);
        }
        
        /// <remarks/>
        public void OgrenimBilgileriEkleGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int idno, string okul_kodu, string fakulte_kodu, string bolum_kodu, short giris_yili, short cikis_yili, string okul_tur_kodu, object userState) {
            if ((this.OgrenimBilgileriEkleGuncelleOperationCompleted == null)) {
                this.OgrenimBilgileriEkleGuncelleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOgrenimBilgileriEkleGuncelleOperationCompleted);
            }
            this.InvokeAsync("OgrenimBilgileriEkleGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno,
                        okul_kodu,
                        fakulte_kodu,
                        bolum_kodu,
                        giris_yili,
                        cikis_yili,
                        okul_tur_kodu}, this.OgrenimBilgileriEkleGuncelleOperationCompleted, userState);
        }
        
        private void OnOgrenimBilgileriEkleGuncelleOperationCompleted(object arg) {
            if ((this.OgrenimBilgileriEkleGuncelleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OgrenimBilgileriEkleGuncelleCompleted(this, new OgrenimBilgileriEkleGuncelleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/OgrenimBilgileriSil", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool OgrenimBilgileriSil(string kul_adi, string kul_sifre, string sicil, int idno) {
            object[] results = this.Invoke("OgrenimBilgileriSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void OgrenimBilgileriSilAsync(string kul_adi, string kul_sifre, string sicil, int idno) {
            this.OgrenimBilgileriSilAsync(kul_adi, kul_sifre, sicil, idno, null);
        }
        
        /// <remarks/>
        public void OgrenimBilgileriSilAsync(string kul_adi, string kul_sifre, string sicil, int idno, object userState) {
            if ((this.OgrenimBilgileriSilOperationCompleted == null)) {
                this.OgrenimBilgileriSilOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOgrenimBilgileriSilOperationCompleted);
            }
            this.InvokeAsync("OgrenimBilgileriSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno}, this.OgrenimBilgileriSilOperationCompleted, userState);
        }
        
        private void OnOgrenimBilgileriSilOperationCompleted(object arg) {
            if ((this.OgrenimBilgileriSilCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OgrenimBilgileriSilCompleted(this, new OgrenimBilgileriSilCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EskiIsYeriBilgileriGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EskiIsYeriBilgileriGetir(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("EskiIsYeriBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EskiIsYeriBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil) {
            this.EskiIsYeriBilgileriGetirAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void EskiIsYeriBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.EskiIsYeriBilgileriGetirOperationCompleted == null)) {
                this.EskiIsYeriBilgileriGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEskiIsYeriBilgileriGetirOperationCompleted);
            }
            this.InvokeAsync("EskiIsYeriBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.EskiIsYeriBilgileriGetirOperationCompleted, userState);
        }
        
        private void OnEskiIsYeriBilgileriGetirOperationCompleted(object arg) {
            if ((this.EskiIsYeriBilgileriGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EskiIsYeriBilgileriGetirCompleted(this, new EskiIsYeriBilgileriGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EskiIsYeriBilgileriEkleGuncelle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool EskiIsYeriBilgileriEkleGuncelle(string kul_adi, string kul_sifre, string sicil, int idno, string sirket_adi, string sirket_sektor, string sirketteki_gorevi, string ayrilma_nedeni, System.DateTime giris_tarihi, System.DateTime cikis_tarihi, string isyeri_adres1, string isyeri_adres2, string isyeri_semt, string isyeri_il) {
            object[] results = this.Invoke("EskiIsYeriBilgileriEkleGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno,
                        sirket_adi,
                        sirket_sektor,
                        sirketteki_gorevi,
                        ayrilma_nedeni,
                        giris_tarihi,
                        cikis_tarihi,
                        isyeri_adres1,
                        isyeri_adres2,
                        isyeri_semt,
                        isyeri_il});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void EskiIsYeriBilgileriEkleGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int idno, string sirket_adi, string sirket_sektor, string sirketteki_gorevi, string ayrilma_nedeni, System.DateTime giris_tarihi, System.DateTime cikis_tarihi, string isyeri_adres1, string isyeri_adres2, string isyeri_semt, string isyeri_il) {
            this.EskiIsYeriBilgileriEkleGuncelleAsync(kul_adi, kul_sifre, sicil, idno, sirket_adi, sirket_sektor, sirketteki_gorevi, ayrilma_nedeni, giris_tarihi, cikis_tarihi, isyeri_adres1, isyeri_adres2, isyeri_semt, isyeri_il, null);
        }
        
        /// <remarks/>
        public void EskiIsYeriBilgileriEkleGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int idno, string sirket_adi, string sirket_sektor, string sirketteki_gorevi, string ayrilma_nedeni, System.DateTime giris_tarihi, System.DateTime cikis_tarihi, string isyeri_adres1, string isyeri_adres2, string isyeri_semt, string isyeri_il, object userState) {
            if ((this.EskiIsYeriBilgileriEkleGuncelleOperationCompleted == null)) {
                this.EskiIsYeriBilgileriEkleGuncelleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEskiIsYeriBilgileriEkleGuncelleOperationCompleted);
            }
            this.InvokeAsync("EskiIsYeriBilgileriEkleGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno,
                        sirket_adi,
                        sirket_sektor,
                        sirketteki_gorevi,
                        ayrilma_nedeni,
                        giris_tarihi,
                        cikis_tarihi,
                        isyeri_adres1,
                        isyeri_adres2,
                        isyeri_semt,
                        isyeri_il}, this.EskiIsYeriBilgileriEkleGuncelleOperationCompleted, userState);
        }
        
        private void OnEskiIsYeriBilgileriEkleGuncelleOperationCompleted(object arg) {
            if ((this.EskiIsYeriBilgileriEkleGuncelleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EskiIsYeriBilgileriEkleGuncelleCompleted(this, new EskiIsYeriBilgileriEkleGuncelleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/KursSeminerBilgileriGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet KursSeminerBilgileriGetir(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("KursSeminerBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void KursSeminerBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil) {
            this.KursSeminerBilgileriGetirAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void KursSeminerBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.KursSeminerBilgileriGetirOperationCompleted == null)) {
                this.KursSeminerBilgileriGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnKursSeminerBilgileriGetirOperationCompleted);
            }
            this.InvokeAsync("KursSeminerBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.KursSeminerBilgileriGetirOperationCompleted, userState);
        }
        
        private void OnKursSeminerBilgileriGetirOperationCompleted(object arg) {
            if ((this.KursSeminerBilgileriGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.KursSeminerBilgileriGetirCompleted(this, new KursSeminerBilgileriGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/KursSeminerBilgileriEkleGuncelle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet KursSeminerBilgileriEkleGuncelle(string kul_adi, string kul_sifre, string sicil, int idno, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string konu, string kurs_sure, string kurs_sorumlusu, string kurs_derecesi, string kurs_seviyesi) {
            object[] results = this.Invoke("KursSeminerBilgileriEkleGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno,
                        baslangic_tarihi,
                        bitis_tarihi,
                        konu,
                        kurs_sure,
                        kurs_sorumlusu,
                        kurs_derecesi,
                        kurs_seviyesi});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void KursSeminerBilgileriEkleGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int idno, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string konu, string kurs_sure, string kurs_sorumlusu, string kurs_derecesi, string kurs_seviyesi) {
            this.KursSeminerBilgileriEkleGuncelleAsync(kul_adi, kul_sifre, sicil, idno, baslangic_tarihi, bitis_tarihi, konu, kurs_sure, kurs_sorumlusu, kurs_derecesi, kurs_seviyesi, null);
        }
        
        /// <remarks/>
        public void KursSeminerBilgileriEkleGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int idno, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string konu, string kurs_sure, string kurs_sorumlusu, string kurs_derecesi, string kurs_seviyesi, object userState) {
            if ((this.KursSeminerBilgileriEkleGuncelleOperationCompleted == null)) {
                this.KursSeminerBilgileriEkleGuncelleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnKursSeminerBilgileriEkleGuncelleOperationCompleted);
            }
            this.InvokeAsync("KursSeminerBilgileriEkleGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        idno,
                        baslangic_tarihi,
                        bitis_tarihi,
                        konu,
                        kurs_sure,
                        kurs_sorumlusu,
                        kurs_derecesi,
                        kurs_seviyesi}, this.KursSeminerBilgileriEkleGuncelleOperationCompleted, userState);
        }
        
        private void OnKursSeminerBilgileriEkleGuncelleOperationCompleted(object arg) {
            if ((this.KursSeminerBilgileriEkleGuncelleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.KursSeminerBilgileriEkleGuncelleCompleted(this, new KursSeminerBilgileriEkleGuncelleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IzinBilgileriGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet IzinBilgileriGetir(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("IzinBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void IzinBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil) {
            this.IzinBilgileriGetirAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void IzinBilgileriGetirAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.IzinBilgileriGetirOperationCompleted == null)) {
                this.IzinBilgileriGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnIzinBilgileriGetirOperationCompleted);
            }
            this.InvokeAsync("IzinBilgileriGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.IzinBilgileriGetirOperationCompleted, userState);
        }
        
        private void OnIzinBilgileriGetirOperationCompleted(object arg) {
            if ((this.IzinBilgileriGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.IzinBilgileriGetirCompleted(this, new IzinBilgileriGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IzinBilgileriEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool IzinBilgileriEkle(string kul_adi, string kul_sifre, string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu, decimal brut_toplam, decimal hafta_tatili, decimal bayram_tatili, decimal net_izin) {
            object[] results = this.Invoke("IzinBilgileriEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        izinyili,
                        baslangic_tarihi,
                        bitis_tarihi,
                        izin_tur_kodu,
                        brut_toplam,
                        hafta_tatili,
                        bayram_tatili,
                        net_izin});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void IzinBilgileriEkleAsync(string kul_adi, string kul_sifre, string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu, decimal brut_toplam, decimal hafta_tatili, decimal bayram_tatili, decimal net_izin) {
            this.IzinBilgileriEkleAsync(kul_adi, kul_sifre, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu, brut_toplam, hafta_tatili, bayram_tatili, net_izin, null);
        }
        
        /// <remarks/>
        public void IzinBilgileriEkleAsync(string kul_adi, string kul_sifre, string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu, decimal brut_toplam, decimal hafta_tatili, decimal bayram_tatili, decimal net_izin, object userState) {
            if ((this.IzinBilgileriEkleOperationCompleted == null)) {
                this.IzinBilgileriEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnIzinBilgileriEkleOperationCompleted);
            }
            this.InvokeAsync("IzinBilgileriEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        izinyili,
                        baslangic_tarihi,
                        bitis_tarihi,
                        izin_tur_kodu,
                        brut_toplam,
                        hafta_tatili,
                        bayram_tatili,
                        net_izin}, this.IzinBilgileriEkleOperationCompleted, userState);
        }
        
        private void OnIzinBilgileriEkleOperationCompleted(object arg) {
            if ((this.IzinBilgileriEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.IzinBilgileriEkleCompleted(this, new IzinBilgileriEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IzinBilgileriSil", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool IzinBilgileriSil(string kul_adi, string kul_sifre, string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu) {
            object[] results = this.Invoke("IzinBilgileriSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        izinyili,
                        baslangic_tarihi,
                        bitis_tarihi,
                        izin_tur_kodu});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void IzinBilgileriSilAsync(string kul_adi, string kul_sifre, string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu) {
            this.IzinBilgileriSilAsync(kul_adi, kul_sifre, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu, null);
        }
        
        /// <remarks/>
        public void IzinBilgileriSilAsync(string kul_adi, string kul_sifre, string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu, object userState) {
            if ((this.IzinBilgileriSilOperationCompleted == null)) {
                this.IzinBilgileriSilOperationCompleted = new System.Threading.SendOrPostCallback(this.OnIzinBilgileriSilOperationCompleted);
            }
            this.InvokeAsync("IzinBilgileriSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        izinyili,
                        baslangic_tarihi,
                        bitis_tarihi,
                        izin_tur_kodu}, this.IzinBilgileriSilOperationCompleted, userState);
        }
        
        private void OnIzinBilgileriSilOperationCompleted(object arg) {
            if ((this.IzinBilgileriSilCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.IzinBilgileriSilCompleted(this, new IzinBilgileriSilCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/OzelSigortaBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet OzelSigortaBilgileriListele(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("OzelSigortaBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void OzelSigortaBilgileriListeleAsync(string kul_adi, string kul_sifre, string sicil) {
            this.OzelSigortaBilgileriListeleAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void OzelSigortaBilgileriListeleAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.OzelSigortaBilgileriListeleOperationCompleted == null)) {
                this.OzelSigortaBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOzelSigortaBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("OzelSigortaBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.OzelSigortaBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnOzelSigortaBilgileriListeleOperationCompleted(object arg) {
            if ((this.OzelSigortaBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OzelSigortaBilgileriListeleCompleted(this, new OzelSigortaBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelYeniGirisEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string PersonelYeniGirisEkle(string kul_adi, string kul_sifre, System.Data.DataTable personel_list) {
            object[] results = this.Invoke("PersonelYeniGirisEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        personel_list});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelYeniGirisEkleAsync(string kul_adi, string kul_sifre, System.Data.DataTable personel_list) {
            this.PersonelYeniGirisEkleAsync(kul_adi, kul_sifre, personel_list, null);
        }
        
        /// <remarks/>
        public void PersonelYeniGirisEkleAsync(string kul_adi, string kul_sifre, System.Data.DataTable personel_list, object userState) {
            if ((this.PersonelYeniGirisEkleOperationCompleted == null)) {
                this.PersonelYeniGirisEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelYeniGirisEkleOperationCompleted);
            }
            this.InvokeAsync("PersonelYeniGirisEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        personel_list}, this.PersonelYeniGirisEkleOperationCompleted, userState);
        }
        
        private void OnPersonelYeniGirisEkleOperationCompleted(object arg) {
            if ((this.PersonelYeniGirisEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelYeniGirisEkleCompleted(this, new PersonelYeniGirisEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet PersonelBilgileriListele(string kul_adi, string kul_sifre, string sicil, string tckimlik) {
            object[] results = this.Invoke("PersonelBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        tckimlik});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelBilgileriListeleAsync(string kul_adi, string kul_sifre, string sicil, string tckimlik) {
            this.PersonelBilgileriListeleAsync(kul_adi, kul_sifre, sicil, tckimlik, null);
        }
        
        /// <remarks/>
        public void PersonelBilgileriListeleAsync(string kul_adi, string kul_sifre, string sicil, string tckimlik, object userState) {
            if ((this.PersonelBilgileriListeleOperationCompleted == null)) {
                this.PersonelBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("PersonelBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        tckimlik}, this.PersonelBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnPersonelBilgileriListeleOperationCompleted(object arg) {
            if ((this.PersonelBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelBilgileriListeleCompleted(this, new PersonelBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/RosterBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet RosterBilgileriListele(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("RosterBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void RosterBilgileriListeleAsync(string kul_adi, string kul_sifre, string sicil) {
            this.RosterBilgileriListeleAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void RosterBilgileriListeleAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.RosterBilgileriListeleOperationCompleted == null)) {
                this.RosterBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnRosterBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("RosterBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.RosterBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnRosterBilgileriListeleOperationCompleted(object arg) {
            if ((this.RosterBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.RosterBilgileriListeleCompleted(this, new RosterBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/BolumBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet BolumBilgileriListele(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("BolumBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void BolumBilgileriListeleAsync(string kul_adi, string kul_sifre) {
            this.BolumBilgileriListeleAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void BolumBilgileriListeleAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.BolumBilgileriListeleOperationCompleted == null)) {
                this.BolumBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnBolumBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("BolumBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre}, this.BolumBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnBolumBilgileriListeleOperationCompleted(object arg) {
            if ((this.BolumBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.BolumBilgileriListeleCompleted(this, new BolumBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DepartmanBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet DepartmanBilgileriListele(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("DepartmanBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void DepartmanBilgileriListeleAsync(string kul_adi, string kul_sifre) {
            this.DepartmanBilgileriListeleAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void DepartmanBilgileriListeleAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.DepartmanBilgileriListeleOperationCompleted == null)) {
                this.DepartmanBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDepartmanBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("DepartmanBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre}, this.DepartmanBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnDepartmanBilgileriListeleOperationCompleted(object arg) {
            if ((this.DepartmanBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DepartmanBilgileriListeleCompleted(this, new DepartmanBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GorevBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet GorevBilgileriListele(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("GorevBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void GorevBilgileriListeleAsync(string kul_adi, string kul_sifre) {
            this.GorevBilgileriListeleAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void GorevBilgileriListeleAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.GorevBilgileriListeleOperationCompleted == null)) {
                this.GorevBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGorevBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("GorevBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre}, this.GorevBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnGorevBilgileriListeleOperationCompleted(object arg) {
            if ((this.GorevBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GorevBilgileriListeleCompleted(this, new GorevBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UnvanBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet UnvanBilgileriListele(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("UnvanBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void UnvanBilgileriListeleAsync(string kul_adi, string kul_sifre) {
            this.UnvanBilgileriListeleAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void UnvanBilgileriListeleAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.UnvanBilgileriListeleOperationCompleted == null)) {
                this.UnvanBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUnvanBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("UnvanBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre}, this.UnvanBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnUnvanBilgileriListeleOperationCompleted(object arg) {
            if ((this.UnvanBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UnvanBilgileriListeleCompleted(this, new UnvanBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/LokasyonBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet LokasyonBilgileriListele(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("LokasyonBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void LokasyonBilgileriListeleAsync(string kul_adi, string kul_sifre) {
            this.LokasyonBilgileriListeleAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void LokasyonBilgileriListeleAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.LokasyonBilgileriListeleOperationCompleted == null)) {
                this.LokasyonBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnLokasyonBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("LokasyonBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre}, this.LokasyonBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnLokasyonBilgileriListeleOperationCompleted(object arg) {
            if ((this.LokasyonBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.LokasyonBilgileriListeleCompleted(this, new LokasyonBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IsAilesiBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet IsAilesiBilgileriListele(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("IsAilesiBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void IsAilesiBilgileriListeleAsync(string kul_adi, string kul_sifre) {
            this.IsAilesiBilgileriListeleAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void IsAilesiBilgileriListeleAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.IsAilesiBilgileriListeleOperationCompleted == null)) {
                this.IsAilesiBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnIsAilesiBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("IsAilesiBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre}, this.IsAilesiBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnIsAilesiBilgileriListeleOperationCompleted(object arg) {
            if ((this.IsAilesiBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.IsAilesiBilgileriListeleCompleted(this, new IsAilesiBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SirketBilgileriListele", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet SirketBilgileriListele(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("SirketBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void SirketBilgileriListeleAsync(string kul_adi, string kul_sifre) {
            this.SirketBilgileriListeleAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void SirketBilgileriListeleAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.SirketBilgileriListeleOperationCompleted == null)) {
                this.SirketBilgileriListeleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSirketBilgileriListeleOperationCompleted);
            }
            this.InvokeAsync("SirketBilgileriListele", new object[] {
                        kul_adi,
                        kul_sifre}, this.SirketBilgileriListeleOperationCompleted, userState);
        }
        
        private void OnSirketBilgileriListeleOperationCompleted(object arg) {
            if ((this.SirketBilgileriListeleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SirketBilgileriListeleCompleted(this, new SirketBilgileriListeleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AvansAktar", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int AvansAktar(string kul_adi, string kul_sifre, string sicil, short yil, short ay, decimal tutar) {
            object[] results = this.Invoke("AvansAktar", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        yil,
                        ay,
                        tutar});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void AvansAktarAsync(string kul_adi, string kul_sifre, string sicil, short yil, short ay, decimal tutar) {
            this.AvansAktarAsync(kul_adi, kul_sifre, sicil, yil, ay, tutar, null);
        }
        
        /// <remarks/>
        public void AvansAktarAsync(string kul_adi, string kul_sifre, string sicil, short yil, short ay, decimal tutar, object userState) {
            if ((this.AvansAktarOperationCompleted == null)) {
                this.AvansAktarOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAvansAktarOperationCompleted);
            }
            this.InvokeAsync("AvansAktar", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        yil,
                        ay,
                        tutar}, this.AvansAktarOperationCompleted, userState);
        }
        
        private void OnAvansAktarOperationCompleted(object arg) {
            if ((this.AvansAktarCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.AvansAktarCompleted(this, new AvansAktarCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ARGEIzinRaporu", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet ARGEIzinRaporu(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("ARGEIzinRaporu", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void ARGEIzinRaporuAsync(string kul_adi, string kul_sifre, string sicil) {
            this.ARGEIzinRaporuAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void ARGEIzinRaporuAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.ARGEIzinRaporuOperationCompleted == null)) {
                this.ARGEIzinRaporuOperationCompleted = new System.Threading.SendOrPostCallback(this.OnARGEIzinRaporuOperationCompleted);
            }
            this.InvokeAsync("ARGEIzinRaporu", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.ARGEIzinRaporuOperationCompleted, userState);
        }
        
        private void OnARGEIzinRaporuOperationCompleted(object arg) {
            if ((this.ARGEIzinRaporuCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ARGEIzinRaporuCompleted(this, new ARGEIzinRaporuCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelTitleBilgi", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet PersonelTitleBilgi(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("PersonelTitleBilgi", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelTitleBilgiAsync(string kul_adi, string kul_sifre, string sicil) {
            this.PersonelTitleBilgiAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void PersonelTitleBilgiAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.PersonelTitleBilgiOperationCompleted == null)) {
                this.PersonelTitleBilgiOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelTitleBilgiOperationCompleted);
            }
            this.InvokeAsync("PersonelTitleBilgi", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.PersonelTitleBilgiOperationCompleted, userState);
        }
        
        private void OnPersonelTitleBilgiOperationCompleted(object arg) {
            if ((this.PersonelTitleBilgiCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelTitleBilgiCompleted(this, new PersonelTitleBilgiCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ParamTitleListe", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet ParamTitleListe(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("ParamTitleListe", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void ParamTitleListeAsync(string kul_adi, string kul_sifre) {
            this.ParamTitleListeAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void ParamTitleListeAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.ParamTitleListeOperationCompleted == null)) {
                this.ParamTitleListeOperationCompleted = new System.Threading.SendOrPostCallback(this.OnParamTitleListeOperationCompleted);
            }
            this.InvokeAsync("ParamTitleListe", new object[] {
                        kul_adi,
                        kul_sifre}, this.ParamTitleListeOperationCompleted, userState);
        }
        
        private void OnParamTitleListeOperationCompleted(object arg) {
            if ((this.ParamTitleListeCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ParamTitleListeCompleted(this, new ParamTitleListeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelAcilDurumListe", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet PersonelAcilDurumListe(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("PersonelAcilDurumListe", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelAcilDurumListeAsync(string kul_adi, string kul_sifre, string sicil) {
            this.PersonelAcilDurumListeAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void PersonelAcilDurumListeAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.PersonelAcilDurumListeOperationCompleted == null)) {
                this.PersonelAcilDurumListeOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelAcilDurumListeOperationCompleted);
            }
            this.InvokeAsync("PersonelAcilDurumListe", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.PersonelAcilDurumListeOperationCompleted, userState);
        }
        
        private void OnPersonelAcilDurumListeOperationCompleted(object arg) {
            if ((this.PersonelAcilDurumListeCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelAcilDurumListeCompleted(this, new PersonelAcilDurumListeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelAcilDurumGuncelle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string PersonelAcilDurumGuncelle(string kul_adi, string kul_sifre, string sicil, int islem_kisi_no, string adsoyad, string istel, string evtel, string ceptel, string yakinlik_derece, string adres, string eposta) {
            object[] results = this.Invoke("PersonelAcilDurumGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        islem_kisi_no,
                        adsoyad,
                        istel,
                        evtel,
                        ceptel,
                        yakinlik_derece,
                        adres,
                        eposta});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelAcilDurumGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int islem_kisi_no, string adsoyad, string istel, string evtel, string ceptel, string yakinlik_derece, string adres, string eposta) {
            this.PersonelAcilDurumGuncelleAsync(kul_adi, kul_sifre, sicil, islem_kisi_no, adsoyad, istel, evtel, ceptel, yakinlik_derece, adres, eposta, null);
        }
        
        /// <remarks/>
        public void PersonelAcilDurumGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int islem_kisi_no, string adsoyad, string istel, string evtel, string ceptel, string yakinlik_derece, string adres, string eposta, object userState) {
            if ((this.PersonelAcilDurumGuncelleOperationCompleted == null)) {
                this.PersonelAcilDurumGuncelleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelAcilDurumGuncelleOperationCompleted);
            }
            this.InvokeAsync("PersonelAcilDurumGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        islem_kisi_no,
                        adsoyad,
                        istel,
                        evtel,
                        ceptel,
                        yakinlik_derece,
                        adres,
                        eposta}, this.PersonelAcilDurumGuncelleOperationCompleted, userState);
        }
        
        private void OnPersonelAcilDurumGuncelleOperationCompleted(object arg) {
            if ((this.PersonelAcilDurumGuncelleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelAcilDurumGuncelleCompleted(this, new PersonelAcilDurumGuncelleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelAcilDurumSil", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string PersonelAcilDurumSil(string kul_adi, string kul_sifre, string sicil, int islem_kisi_no) {
            object[] results = this.Invoke("PersonelAcilDurumSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        islem_kisi_no});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelAcilDurumSilAsync(string kul_adi, string kul_sifre, string sicil, int islem_kisi_no) {
            this.PersonelAcilDurumSilAsync(kul_adi, kul_sifre, sicil, islem_kisi_no, null);
        }
        
        /// <remarks/>
        public void PersonelAcilDurumSilAsync(string kul_adi, string kul_sifre, string sicil, int islem_kisi_no, object userState) {
            if ((this.PersonelAcilDurumSilOperationCompleted == null)) {
                this.PersonelAcilDurumSilOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelAcilDurumSilOperationCompleted);
            }
            this.InvokeAsync("PersonelAcilDurumSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        islem_kisi_no}, this.PersonelAcilDurumSilOperationCompleted, userState);
        }
        
        private void OnPersonelAcilDurumSilOperationCompleted(object arg) {
            if ((this.PersonelAcilDurumSilCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelAcilDurumSilCompleted(this, new PersonelAcilDurumSilCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelCocukListe", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet PersonelCocukListe(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("PersonelCocukListe", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelCocukListeAsync(string kul_adi, string kul_sifre, string sicil) {
            this.PersonelCocukListeAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void PersonelCocukListeAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.PersonelCocukListeOperationCompleted == null)) {
                this.PersonelCocukListeOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelCocukListeOperationCompleted);
            }
            this.InvokeAsync("PersonelCocukListe", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.PersonelCocukListeOperationCompleted, userState);
        }
        
        private void OnPersonelCocukListeOperationCompleted(object arg) {
            if ((this.PersonelCocukListeCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelCocukListeCompleted(this, new PersonelCocukListeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelCocukGuncelle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string PersonelCocukGuncelle(string kul_adi, string kul_sifre, string sicil, int kayit_id, string adi, string soyadi, string cins, System.DateTime dogtar, string tahsil, string tc_kimlik_no) {
            object[] results = this.Invoke("PersonelCocukGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        kayit_id,
                        adi,
                        soyadi,
                        cins,
                        dogtar,
                        tahsil,
                        tc_kimlik_no});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelCocukGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int kayit_id, string adi, string soyadi, string cins, System.DateTime dogtar, string tahsil, string tc_kimlik_no) {
            this.PersonelCocukGuncelleAsync(kul_adi, kul_sifre, sicil, kayit_id, adi, soyadi, cins, dogtar, tahsil, tc_kimlik_no, null);
        }
        
        /// <remarks/>
        public void PersonelCocukGuncelleAsync(string kul_adi, string kul_sifre, string sicil, int kayit_id, string adi, string soyadi, string cins, System.DateTime dogtar, string tahsil, string tc_kimlik_no, object userState) {
            if ((this.PersonelCocukGuncelleOperationCompleted == null)) {
                this.PersonelCocukGuncelleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelCocukGuncelleOperationCompleted);
            }
            this.InvokeAsync("PersonelCocukGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        kayit_id,
                        adi,
                        soyadi,
                        cins,
                        dogtar,
                        tahsil,
                        tc_kimlik_no}, this.PersonelCocukGuncelleOperationCompleted, userState);
        }
        
        private void OnPersonelCocukGuncelleOperationCompleted(object arg) {
            if ((this.PersonelCocukGuncelleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelCocukGuncelleCompleted(this, new PersonelCocukGuncelleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelCocukSil", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string PersonelCocukSil(string kul_adi, string kul_sifre, string sicil, int kayit_id) {
            object[] results = this.Invoke("PersonelCocukSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        kayit_id});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelCocukSilAsync(string kul_adi, string kul_sifre, string sicil, int kayit_id) {
            this.PersonelCocukSilAsync(kul_adi, kul_sifre, sicil, kayit_id, null);
        }
        
        /// <remarks/>
        public void PersonelCocukSilAsync(string kul_adi, string kul_sifre, string sicil, int kayit_id, object userState) {
            if ((this.PersonelCocukSilOperationCompleted == null)) {
                this.PersonelCocukSilOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelCocukSilOperationCompleted);
            }
            this.InvokeAsync("PersonelCocukSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        kayit_id}, this.PersonelCocukSilOperationCompleted, userState);
        }
        
        private void OnPersonelCocukSilOperationCompleted(object arg) {
            if ((this.PersonelCocukSilCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelCocukSilCompleted(this, new PersonelCocukSilCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelEsListe", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet PersonelEsListe(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("PersonelEsListe", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelEsListeAsync(string kul_adi, string kul_sifre, string sicil) {
            this.PersonelEsListeAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void PersonelEsListeAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.PersonelEsListeOperationCompleted == null)) {
                this.PersonelEsListeOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelEsListeOperationCompleted);
            }
            this.InvokeAsync("PersonelEsListe", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.PersonelEsListeOperationCompleted, userState);
        }
        
        private void OnPersonelEsListeOperationCompleted(object arg) {
            if ((this.PersonelEsListeCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelEsListeCompleted(this, new PersonelEsListeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelEsGuncelle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string PersonelEsGuncelle(string kul_adi, string kul_sifre, string sicil, string adi, string soyadi, System.DateTime dogtar, string tahsil, string calisiyormu, string tc_kimlik_no) {
            object[] results = this.Invoke("PersonelEsGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        adi,
                        soyadi,
                        dogtar,
                        tahsil,
                        calisiyormu,
                        tc_kimlik_no});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelEsGuncelleAsync(string kul_adi, string kul_sifre, string sicil, string adi, string soyadi, System.DateTime dogtar, string tahsil, string calisiyormu, string tc_kimlik_no) {
            this.PersonelEsGuncelleAsync(kul_adi, kul_sifre, sicil, adi, soyadi, dogtar, tahsil, calisiyormu, tc_kimlik_no, null);
        }
        
        /// <remarks/>
        public void PersonelEsGuncelleAsync(string kul_adi, string kul_sifre, string sicil, string adi, string soyadi, System.DateTime dogtar, string tahsil, string calisiyormu, string tc_kimlik_no, object userState) {
            if ((this.PersonelEsGuncelleOperationCompleted == null)) {
                this.PersonelEsGuncelleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelEsGuncelleOperationCompleted);
            }
            this.InvokeAsync("PersonelEsGuncelle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        adi,
                        soyadi,
                        dogtar,
                        tahsil,
                        calisiyormu,
                        tc_kimlik_no}, this.PersonelEsGuncelleOperationCompleted, userState);
        }
        
        private void OnPersonelEsGuncelleOperationCompleted(object arg) {
            if ((this.PersonelEsGuncelleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelEsGuncelleCompleted(this, new PersonelEsGuncelleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelEsSil", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string PersonelEsSil(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("PersonelEsSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelEsSilAsync(string kul_adi, string kul_sifre, string sicil) {
            this.PersonelEsSilAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void PersonelEsSilAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.PersonelEsSilOperationCompleted == null)) {
                this.PersonelEsSilOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelEsSilOperationCompleted);
            }
            this.InvokeAsync("PersonelEsSil", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.PersonelEsSilOperationCompleted, userState);
        }
        
        private void OnPersonelEsSilOperationCompleted(object arg) {
            if ((this.PersonelEsSilCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelEsSilCompleted(this, new PersonelEsSilCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TahsilOkulParametresiGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet TahsilOkulParametresiGetir(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            object[] results = this.Invoke("TahsilOkulParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void TahsilOkulParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            this.TahsilOkulParametresiGetirAsync(kul_adi, kul_sifre, sicil, sort_tipi, null);
        }
        
        /// <remarks/>
        public void TahsilOkulParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi, object userState) {
            if ((this.TahsilOkulParametresiGetirOperationCompleted == null)) {
                this.TahsilOkulParametresiGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTahsilOkulParametresiGetirOperationCompleted);
            }
            this.InvokeAsync("TahsilOkulParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi}, this.TahsilOkulParametresiGetirOperationCompleted, userState);
        }
        
        private void OnTahsilOkulParametresiGetirOperationCompleted(object arg) {
            if ((this.TahsilOkulParametresiGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TahsilOkulParametresiGetirCompleted(this, new TahsilOkulParametresiGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TahsilFakulteParametresiGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet TahsilFakulteParametresiGetir(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            object[] results = this.Invoke("TahsilFakulteParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void TahsilFakulteParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            this.TahsilFakulteParametresiGetirAsync(kul_adi, kul_sifre, sicil, sort_tipi, null);
        }
        
        /// <remarks/>
        public void TahsilFakulteParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi, object userState) {
            if ((this.TahsilFakulteParametresiGetirOperationCompleted == null)) {
                this.TahsilFakulteParametresiGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTahsilFakulteParametresiGetirOperationCompleted);
            }
            this.InvokeAsync("TahsilFakulteParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi}, this.TahsilFakulteParametresiGetirOperationCompleted, userState);
        }
        
        private void OnTahsilFakulteParametresiGetirOperationCompleted(object arg) {
            if ((this.TahsilFakulteParametresiGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TahsilFakulteParametresiGetirCompleted(this, new TahsilFakulteParametresiGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TahsilOkulTuruParametresiGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet TahsilOkulTuruParametresiGetir(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            object[] results = this.Invoke("TahsilOkulTuruParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void TahsilOkulTuruParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            this.TahsilOkulTuruParametresiGetirAsync(kul_adi, kul_sifre, sicil, sort_tipi, null);
        }
        
        /// <remarks/>
        public void TahsilOkulTuruParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi, object userState) {
            if ((this.TahsilOkulTuruParametresiGetirOperationCompleted == null)) {
                this.TahsilOkulTuruParametresiGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTahsilOkulTuruParametresiGetirOperationCompleted);
            }
            this.InvokeAsync("TahsilOkulTuruParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi}, this.TahsilOkulTuruParametresiGetirOperationCompleted, userState);
        }
        
        private void OnTahsilOkulTuruParametresiGetirOperationCompleted(object arg) {
            if ((this.TahsilOkulTuruParametresiGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TahsilOkulTuruParametresiGetirCompleted(this, new TahsilOkulTuruParametresiGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TahsilOkulBolumParametresiGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet TahsilOkulBolumParametresiGetir(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            object[] results = this.Invoke("TahsilOkulBolumParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void TahsilOkulBolumParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi) {
            this.TahsilOkulBolumParametresiGetirAsync(kul_adi, kul_sifre, sicil, sort_tipi, null);
        }
        
        /// <remarks/>
        public void TahsilOkulBolumParametresiGetirAsync(string kul_adi, string kul_sifre, string sicil, string sort_tipi, object userState) {
            if ((this.TahsilOkulBolumParametresiGetirOperationCompleted == null)) {
                this.TahsilOkulBolumParametresiGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTahsilOkulBolumParametresiGetirOperationCompleted);
            }
            this.InvokeAsync("TahsilOkulBolumParametresiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sort_tipi}, this.TahsilOkulBolumParametresiGetirOperationCompleted, userState);
        }
        
        private void OnTahsilOkulBolumParametresiGetirOperationCompleted(object arg) {
            if ((this.TahsilOkulBolumParametresiGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TahsilOkulBolumParametresiGetirCompleted(this, new TahsilOkulBolumParametresiGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void KisiBilgileriGetirCompletedEventHandler(object sender, KisiBilgileriGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class KisiBilgileriGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal KisiBilgileriGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void OrganizasyonelBilgileriGetirCompletedEventHandler(object sender, OrganizasyonelBilgileriGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OrganizasyonelBilgileriGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OrganizasyonelBilgileriGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void IletisimBilgileriGetirCompletedEventHandler(object sender, IletisimBilgileriGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class IletisimBilgileriGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal IletisimBilgileriGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void OgrenimBilgileriGetirCompletedEventHandler(object sender, OgrenimBilgileriGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OgrenimBilgileriGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OgrenimBilgileriGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void OgrenimBilgileriEkleGuncelleCompletedEventHandler(object sender, OgrenimBilgileriEkleGuncelleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OgrenimBilgileriEkleGuncelleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OgrenimBilgileriEkleGuncelleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void OgrenimBilgileriSilCompletedEventHandler(object sender, OgrenimBilgileriSilCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OgrenimBilgileriSilCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OgrenimBilgileriSilCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EskiIsYeriBilgileriGetirCompletedEventHandler(object sender, EskiIsYeriBilgileriGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EskiIsYeriBilgileriGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EskiIsYeriBilgileriGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EskiIsYeriBilgileriEkleGuncelleCompletedEventHandler(object sender, EskiIsYeriBilgileriEkleGuncelleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EskiIsYeriBilgileriEkleGuncelleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EskiIsYeriBilgileriEkleGuncelleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void KursSeminerBilgileriGetirCompletedEventHandler(object sender, KursSeminerBilgileriGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class KursSeminerBilgileriGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal KursSeminerBilgileriGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void KursSeminerBilgileriEkleGuncelleCompletedEventHandler(object sender, KursSeminerBilgileriEkleGuncelleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class KursSeminerBilgileriEkleGuncelleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal KursSeminerBilgileriEkleGuncelleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void IzinBilgileriGetirCompletedEventHandler(object sender, IzinBilgileriGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class IzinBilgileriGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal IzinBilgileriGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void IzinBilgileriEkleCompletedEventHandler(object sender, IzinBilgileriEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class IzinBilgileriEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal IzinBilgileriEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void IzinBilgileriSilCompletedEventHandler(object sender, IzinBilgileriSilCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class IzinBilgileriSilCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal IzinBilgileriSilCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void OzelSigortaBilgileriListeleCompletedEventHandler(object sender, OzelSigortaBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OzelSigortaBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OzelSigortaBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelYeniGirisEkleCompletedEventHandler(object sender, PersonelYeniGirisEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelYeniGirisEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelYeniGirisEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelBilgileriListeleCompletedEventHandler(object sender, PersonelBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void RosterBilgileriListeleCompletedEventHandler(object sender, RosterBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class RosterBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal RosterBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void BolumBilgileriListeleCompletedEventHandler(object sender, BolumBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BolumBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal BolumBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void DepartmanBilgileriListeleCompletedEventHandler(object sender, DepartmanBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DepartmanBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DepartmanBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GorevBilgileriListeleCompletedEventHandler(object sender, GorevBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GorevBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GorevBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void UnvanBilgileriListeleCompletedEventHandler(object sender, UnvanBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UnvanBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UnvanBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void LokasyonBilgileriListeleCompletedEventHandler(object sender, LokasyonBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class LokasyonBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal LokasyonBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void IsAilesiBilgileriListeleCompletedEventHandler(object sender, IsAilesiBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class IsAilesiBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal IsAilesiBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SirketBilgileriListeleCompletedEventHandler(object sender, SirketBilgileriListeleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SirketBilgileriListeleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SirketBilgileriListeleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void AvansAktarCompletedEventHandler(object sender, AvansAktarCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AvansAktarCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal AvansAktarCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void ARGEIzinRaporuCompletedEventHandler(object sender, ARGEIzinRaporuCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ARGEIzinRaporuCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ARGEIzinRaporuCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelTitleBilgiCompletedEventHandler(object sender, PersonelTitleBilgiCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelTitleBilgiCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelTitleBilgiCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void ParamTitleListeCompletedEventHandler(object sender, ParamTitleListeCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ParamTitleListeCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ParamTitleListeCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelAcilDurumListeCompletedEventHandler(object sender, PersonelAcilDurumListeCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelAcilDurumListeCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelAcilDurumListeCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelAcilDurumGuncelleCompletedEventHandler(object sender, PersonelAcilDurumGuncelleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelAcilDurumGuncelleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelAcilDurumGuncelleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelAcilDurumSilCompletedEventHandler(object sender, PersonelAcilDurumSilCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelAcilDurumSilCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelAcilDurumSilCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelCocukListeCompletedEventHandler(object sender, PersonelCocukListeCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelCocukListeCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelCocukListeCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelCocukGuncelleCompletedEventHandler(object sender, PersonelCocukGuncelleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelCocukGuncelleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelCocukGuncelleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelCocukSilCompletedEventHandler(object sender, PersonelCocukSilCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelCocukSilCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelCocukSilCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelEsListeCompletedEventHandler(object sender, PersonelEsListeCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelEsListeCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelEsListeCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelEsGuncelleCompletedEventHandler(object sender, PersonelEsGuncelleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelEsGuncelleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelEsGuncelleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelEsSilCompletedEventHandler(object sender, PersonelEsSilCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelEsSilCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelEsSilCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void TahsilOkulParametresiGetirCompletedEventHandler(object sender, TahsilOkulParametresiGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TahsilOkulParametresiGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TahsilOkulParametresiGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void TahsilFakulteParametresiGetirCompletedEventHandler(object sender, TahsilFakulteParametresiGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TahsilFakulteParametresiGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TahsilFakulteParametresiGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void TahsilOkulTuruParametresiGetirCompletedEventHandler(object sender, TahsilOkulTuruParametresiGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TahsilOkulTuruParametresiGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TahsilOkulTuruParametresiGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void TahsilOkulBolumParametresiGetirCompletedEventHandler(object sender, TahsilOkulBolumParametresiGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TahsilOkulBolumParametresiGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TahsilOkulBolumParametresiGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591