﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.CreateBayiBelgeOnayAkis {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="CreateWorkflowNewSoap", Namespace="http://tempuri.org/")]
    public partial class CreateWorkflowNew : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback CancelDTSatisliTicariYayinAcmaAkisiOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateDTSatisliTicariYayinAcmaAkisiOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateTicariGrupDegisikligiAkisOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateTicariFiyatIstisnaAkisOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateTicariUyeYetkilendirmeAkisOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateTicariUcretIadeAkisOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateBayiFotografDegisikligiAkisOperationCompleted;
        
        private System.Threading.SendOrPostCallback CreateBayiBelgeOnayAkisOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public CreateWorkflowNew() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_CreateBayiBelgeOnayAkis_CreateWorkflowNew;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event CancelDTSatisliTicariYayinAcmaAkisiCompletedEventHandler CancelDTSatisliTicariYayinAcmaAkisiCompleted;
        
        /// <remarks/>
        public event CreateDTSatisliTicariYayinAcmaAkisiCompletedEventHandler CreateDTSatisliTicariYayinAcmaAkisiCompleted;
        
        /// <remarks/>
        public event CreateTicariGrupDegisikligiAkisCompletedEventHandler CreateTicariGrupDegisikligiAkisCompleted;
        
        /// <remarks/>
        public event CreateTicariFiyatIstisnaAkisCompletedEventHandler CreateTicariFiyatIstisnaAkisCompleted;
        
        /// <remarks/>
        public event CreateTicariUyeYetkilendirmeAkisCompletedEventHandler CreateTicariUyeYetkilendirmeAkisCompleted;
        
        /// <remarks/>
        public event CreateTicariUcretIadeAkisCompletedEventHandler CreateTicariUcretIadeAkisCompleted;
        
        /// <remarks/>
        public event CreateBayiFotografDegisikligiAkisCompletedEventHandler CreateBayiFotografDegisikligiAkisCompleted;
        
        /// <remarks/>
        public event CreateBayiBelgeOnayAkisCompletedEventHandler CreateBayiBelgeOnayAkisCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CancelDTSatisliTicariYayinAcmaAkisi", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CancelDTSatisliTicariYayinAcmaAkisi(long IRIS_ID, string Comment) {
            object[] results = this.Invoke("CancelDTSatisliTicariYayinAcmaAkisi", new object[] {
                        IRIS_ID,
                        Comment});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CancelDTSatisliTicariYayinAcmaAkisiAsync(long IRIS_ID, string Comment) {
            this.CancelDTSatisliTicariYayinAcmaAkisiAsync(IRIS_ID, Comment, null);
        }
        
        /// <remarks/>
        public void CancelDTSatisliTicariYayinAcmaAkisiAsync(long IRIS_ID, string Comment, object userState) {
            if ((this.CancelDTSatisliTicariYayinAcmaAkisiOperationCompleted == null)) {
                this.CancelDTSatisliTicariYayinAcmaAkisiOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCancelDTSatisliTicariYayinAcmaAkisiOperationCompleted);
            }
            this.InvokeAsync("CancelDTSatisliTicariYayinAcmaAkisi", new object[] {
                        IRIS_ID,
                        Comment}, this.CancelDTSatisliTicariYayinAcmaAkisiOperationCompleted, userState);
        }
        
        private void OnCancelDTSatisliTicariYayinAcmaAkisiOperationCompleted(object arg) {
            if ((this.CancelDTSatisliTicariYayinAcmaAkisiCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CancelDTSatisliTicariYayinAcmaAkisiCompleted(this, new CancelDTSatisliTicariYayinAcmaAkisiCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateDTSatisliTicariYayinAcmaAkisi", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CreateDTSatisliTicariYayinAcmaAkisi(
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<long> AccountNumber, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<long> ProspectNumber, 
                    string Ad, 
                    string Soyad, 
                    string AdresUlke, 
                    string AdresIl, 
                    string AdresIlce, 
                    string AdresDetay, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<long> AdresUAVTId, 
                    string AdresGPSLokasyon, 
                    string BayiKodu, 
                    string BayiAdi, 
                    string BayiPersonelKodu, 
                    string BayiAdresIl, 
                    string BayiAdresIlce, 
                    string BayiBolgeKodu, 
                    string BayiBolgeAdi, 
                    string BayiTemsilciDbsKodu, 
                    long IrisKayitId, 
                    string UyduTipi, 
                    string TicariGrupKod, 
                    string TicariGrupAdi, 
                    string OdemeTipi, 
                    string OdemeTipiDescr, 
                    long SimpleOfferId, 
                    string SimpleOfferIdDescr, 
                    long BundleOfferId, 
                    string BundleOfferIdDescr, 
                    string Frekans, 
                    string FrekansDescr, 
                    decimal IndirimOrani, 
                    decimal ListeFiyati, 
                    decimal IndirimUygulanmisFiyat, 
                    string Aciklama, 
                    long Offerbusinessinterid, 
                    long Alterationbusinessinterid, 
                    long ServiceAccountId, 
                    string DbsoutletLocation) {
            object[] results = this.Invoke("CreateDTSatisliTicariYayinAcmaAkisi", new object[] {
                        AccountNumber,
                        ProspectNumber,
                        Ad,
                        Soyad,
                        AdresUlke,
                        AdresIl,
                        AdresIlce,
                        AdresDetay,
                        AdresUAVTId,
                        AdresGPSLokasyon,
                        BayiKodu,
                        BayiAdi,
                        BayiPersonelKodu,
                        BayiAdresIl,
                        BayiAdresIlce,
                        BayiBolgeKodu,
                        BayiBolgeAdi,
                        BayiTemsilciDbsKodu,
                        IrisKayitId,
                        UyduTipi,
                        TicariGrupKod,
                        TicariGrupAdi,
                        OdemeTipi,
                        OdemeTipiDescr,
                        SimpleOfferId,
                        SimpleOfferIdDescr,
                        BundleOfferId,
                        BundleOfferIdDescr,
                        Frekans,
                        FrekansDescr,
                        IndirimOrani,
                        ListeFiyati,
                        IndirimUygulanmisFiyat,
                        Aciklama,
                        Offerbusinessinterid,
                        Alterationbusinessinterid,
                        ServiceAccountId,
                        DbsoutletLocation});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CreateDTSatisliTicariYayinAcmaAkisiAsync(
                    System.Nullable<long> AccountNumber, 
                    System.Nullable<long> ProspectNumber, 
                    string Ad, 
                    string Soyad, 
                    string AdresUlke, 
                    string AdresIl, 
                    string AdresIlce, 
                    string AdresDetay, 
                    System.Nullable<long> AdresUAVTId, 
                    string AdresGPSLokasyon, 
                    string BayiKodu, 
                    string BayiAdi, 
                    string BayiPersonelKodu, 
                    string BayiAdresIl, 
                    string BayiAdresIlce, 
                    string BayiBolgeKodu, 
                    string BayiBolgeAdi, 
                    string BayiTemsilciDbsKodu, 
                    long IrisKayitId, 
                    string UyduTipi, 
                    string TicariGrupKod, 
                    string TicariGrupAdi, 
                    string OdemeTipi, 
                    string OdemeTipiDescr, 
                    long SimpleOfferId, 
                    string SimpleOfferIdDescr, 
                    long BundleOfferId, 
                    string BundleOfferIdDescr, 
                    string Frekans, 
                    string FrekansDescr, 
                    decimal IndirimOrani, 
                    decimal ListeFiyati, 
                    decimal IndirimUygulanmisFiyat, 
                    string Aciklama, 
                    long Offerbusinessinterid, 
                    long Alterationbusinessinterid, 
                    long ServiceAccountId, 
                    string DbsoutletLocation) {
            this.CreateDTSatisliTicariYayinAcmaAkisiAsync(AccountNumber, ProspectNumber, Ad, Soyad, AdresUlke, AdresIl, AdresIlce, AdresDetay, AdresUAVTId, AdresGPSLokasyon, BayiKodu, BayiAdi, BayiPersonelKodu, BayiAdresIl, BayiAdresIlce, BayiBolgeKodu, BayiBolgeAdi, BayiTemsilciDbsKodu, IrisKayitId, UyduTipi, TicariGrupKod, TicariGrupAdi, OdemeTipi, OdemeTipiDescr, SimpleOfferId, SimpleOfferIdDescr, BundleOfferId, BundleOfferIdDescr, Frekans, FrekansDescr, IndirimOrani, ListeFiyati, IndirimUygulanmisFiyat, Aciklama, Offerbusinessinterid, Alterationbusinessinterid, ServiceAccountId, DbsoutletLocation, null);
        }
        
        /// <remarks/>
        public void CreateDTSatisliTicariYayinAcmaAkisiAsync(
                    System.Nullable<long> AccountNumber, 
                    System.Nullable<long> ProspectNumber, 
                    string Ad, 
                    string Soyad, 
                    string AdresUlke, 
                    string AdresIl, 
                    string AdresIlce, 
                    string AdresDetay, 
                    System.Nullable<long> AdresUAVTId, 
                    string AdresGPSLokasyon, 
                    string BayiKodu, 
                    string BayiAdi, 
                    string BayiPersonelKodu, 
                    string BayiAdresIl, 
                    string BayiAdresIlce, 
                    string BayiBolgeKodu, 
                    string BayiBolgeAdi, 
                    string BayiTemsilciDbsKodu, 
                    long IrisKayitId, 
                    string UyduTipi, 
                    string TicariGrupKod, 
                    string TicariGrupAdi, 
                    string OdemeTipi, 
                    string OdemeTipiDescr, 
                    long SimpleOfferId, 
                    string SimpleOfferIdDescr, 
                    long BundleOfferId, 
                    string BundleOfferIdDescr, 
                    string Frekans, 
                    string FrekansDescr, 
                    decimal IndirimOrani, 
                    decimal ListeFiyati, 
                    decimal IndirimUygulanmisFiyat, 
                    string Aciklama, 
                    long Offerbusinessinterid, 
                    long Alterationbusinessinterid, 
                    long ServiceAccountId, 
                    string DbsoutletLocation, 
                    object userState) {
            if ((this.CreateDTSatisliTicariYayinAcmaAkisiOperationCompleted == null)) {
                this.CreateDTSatisliTicariYayinAcmaAkisiOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateDTSatisliTicariYayinAcmaAkisiOperationCompleted);
            }
            this.InvokeAsync("CreateDTSatisliTicariYayinAcmaAkisi", new object[] {
                        AccountNumber,
                        ProspectNumber,
                        Ad,
                        Soyad,
                        AdresUlke,
                        AdresIl,
                        AdresIlce,
                        AdresDetay,
                        AdresUAVTId,
                        AdresGPSLokasyon,
                        BayiKodu,
                        BayiAdi,
                        BayiPersonelKodu,
                        BayiAdresIl,
                        BayiAdresIlce,
                        BayiBolgeKodu,
                        BayiBolgeAdi,
                        BayiTemsilciDbsKodu,
                        IrisKayitId,
                        UyduTipi,
                        TicariGrupKod,
                        TicariGrupAdi,
                        OdemeTipi,
                        OdemeTipiDescr,
                        SimpleOfferId,
                        SimpleOfferIdDescr,
                        BundleOfferId,
                        BundleOfferIdDescr,
                        Frekans,
                        FrekansDescr,
                        IndirimOrani,
                        ListeFiyati,
                        IndirimUygulanmisFiyat,
                        Aciklama,
                        Offerbusinessinterid,
                        Alterationbusinessinterid,
                        ServiceAccountId,
                        DbsoutletLocation}, this.CreateDTSatisliTicariYayinAcmaAkisiOperationCompleted, userState);
        }
        
        private void OnCreateDTSatisliTicariYayinAcmaAkisiOperationCompleted(object arg) {
            if ((this.CreateDTSatisliTicariYayinAcmaAkisiCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateDTSatisliTicariYayinAcmaAkisiCompleted(this, new CreateDTSatisliTicariYayinAcmaAkisiCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateTicariGrupDegisikligiAkis", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CreateTicariGrupDegisikligiAkis(
                    long IRIS_ID, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string YENI_GRUP, 
                    string YENI_PESIN, 
                    string YENI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK) {
            object[] results = this.Invoke("CreateTicariGrupDegisikligiAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ESKI_GRUP,
                        ESKI_PESIN,
                        ESKI_TAKSITLI,
                        YENI_GRUP,
                        YENI_PESIN,
                        YENI_TAKSITLI,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        KAYNAK});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CreateTicariGrupDegisikligiAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string YENI_GRUP, 
                    string YENI_PESIN, 
                    string YENI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK) {
            this.CreateTicariGrupDegisikligiAkisAsync(IRIS_ID, UYE_NO, UYE_ADI, BAYI_AD, BAYI_KOD, BAYI_BOLGE_KOD, BAYI_YONETICI, ACIKLAMA, ESKI_GRUP, ESKI_PESIN, ESKI_TAKSITLI, YENI_GRUP, YENI_PESIN, YENI_TAKSITLI, ADRES, IL, ILCE, DEMOGRAFIK, BAYI_MAIL, POTANSIYEL_NO, POTANSIYEL_ADI, KAYNAK, null);
        }
        
        /// <remarks/>
        public void CreateTicariGrupDegisikligiAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string YENI_GRUP, 
                    string YENI_PESIN, 
                    string YENI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK, 
                    object userState) {
            if ((this.CreateTicariGrupDegisikligiAkisOperationCompleted == null)) {
                this.CreateTicariGrupDegisikligiAkisOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateTicariGrupDegisikligiAkisOperationCompleted);
            }
            this.InvokeAsync("CreateTicariGrupDegisikligiAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ESKI_GRUP,
                        ESKI_PESIN,
                        ESKI_TAKSITLI,
                        YENI_GRUP,
                        YENI_PESIN,
                        YENI_TAKSITLI,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        KAYNAK}, this.CreateTicariGrupDegisikligiAkisOperationCompleted, userState);
        }
        
        private void OnCreateTicariGrupDegisikligiAkisOperationCompleted(object arg) {
            if ((this.CreateTicariGrupDegisikligiAkisCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateTicariGrupDegisikligiAkisCompleted(this, new CreateTicariGrupDegisikligiAkisCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateTicariFiyatIstisnaAkis", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CreateTicariFiyatIstisnaAkis(
                    long IRIS_ID, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string ODEME_TIPI, 
                    string MEVCUT_GRUP, 
                    string MEVCUT_FIYAT, 
                    string ISTENEN_FIYAT, 
                    string BAYI_MAIL, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK) {
            object[] results = this.Invoke("CreateTicariFiyatIstisnaAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        ODEME_TIPI,
                        MEVCUT_GRUP,
                        MEVCUT_FIYAT,
                        ISTENEN_FIYAT,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        KAYNAK});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CreateTicariFiyatIstisnaAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string ODEME_TIPI, 
                    string MEVCUT_GRUP, 
                    string MEVCUT_FIYAT, 
                    string ISTENEN_FIYAT, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK) {
            this.CreateTicariFiyatIstisnaAkisAsync(IRIS_ID, UYE_NO, UYE_ADI, BAYI_AD, BAYI_KOD, BAYI_BOLGE_KOD, BAYI_YONETICI, ACIKLAMA, ADRES, IL, ILCE, DEMOGRAFIK, ODEME_TIPI, MEVCUT_GRUP, MEVCUT_FIYAT, ISTENEN_FIYAT, BAYI_MAIL, POTANSIYEL_NO, POTANSIYEL_ADI, KAYNAK, null);
        }
        
        /// <remarks/>
        public void CreateTicariFiyatIstisnaAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string ODEME_TIPI, 
                    string MEVCUT_GRUP, 
                    string MEVCUT_FIYAT, 
                    string ISTENEN_FIYAT, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK, 
                    object userState) {
            if ((this.CreateTicariFiyatIstisnaAkisOperationCompleted == null)) {
                this.CreateTicariFiyatIstisnaAkisOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateTicariFiyatIstisnaAkisOperationCompleted);
            }
            this.InvokeAsync("CreateTicariFiyatIstisnaAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        ODEME_TIPI,
                        MEVCUT_GRUP,
                        MEVCUT_FIYAT,
                        ISTENEN_FIYAT,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        KAYNAK}, this.CreateTicariFiyatIstisnaAkisOperationCompleted, userState);
        }
        
        private void OnCreateTicariFiyatIstisnaAkisOperationCompleted(object arg) {
            if ((this.CreateTicariFiyatIstisnaAkisCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateTicariFiyatIstisnaAkisCompleted(this, new CreateTicariFiyatIstisnaAkisCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateTicariUyeYetkilendirmeAkis", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CreateTicariUyeYetkilendirmeAkis(
                    long IRIS_ID, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string YENI_GRUP, 
                    string YENI_PESIN, 
                    string YENI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK, 
                    string BAYI_IL) {
            object[] results = this.Invoke("CreateTicariUyeYetkilendirmeAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ESKI_GRUP,
                        ESKI_PESIN,
                        ESKI_TAKSITLI,
                        YENI_GRUP,
                        YENI_PESIN,
                        YENI_TAKSITLI,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        KAYNAK,
                        BAYI_IL});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CreateTicariUyeYetkilendirmeAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string YENI_GRUP, 
                    string YENI_PESIN, 
                    string YENI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK, 
                    string BAYI_IL) {
            this.CreateTicariUyeYetkilendirmeAkisAsync(IRIS_ID, UYE_NO, UYE_ADI, BAYI_AD, BAYI_KOD, BAYI_BOLGE_KOD, BAYI_YONETICI, ACIKLAMA, ESKI_GRUP, ESKI_PESIN, ESKI_TAKSITLI, YENI_GRUP, YENI_PESIN, YENI_TAKSITLI, ADRES, IL, ILCE, DEMOGRAFIK, BAYI_MAIL, POTANSIYEL_NO, POTANSIYEL_ADI, KAYNAK, BAYI_IL, null);
        }
        
        /// <remarks/>
        public void CreateTicariUyeYetkilendirmeAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string YENI_GRUP, 
                    string YENI_PESIN, 
                    string YENI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string KAYNAK, 
                    string BAYI_IL, 
                    object userState) {
            if ((this.CreateTicariUyeYetkilendirmeAkisOperationCompleted == null)) {
                this.CreateTicariUyeYetkilendirmeAkisOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateTicariUyeYetkilendirmeAkisOperationCompleted);
            }
            this.InvokeAsync("CreateTicariUyeYetkilendirmeAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ESKI_GRUP,
                        ESKI_PESIN,
                        ESKI_TAKSITLI,
                        YENI_GRUP,
                        YENI_PESIN,
                        YENI_TAKSITLI,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        KAYNAK,
                        BAYI_IL}, this.CreateTicariUyeYetkilendirmeAkisOperationCompleted, userState);
        }
        
        private void OnCreateTicariUyeYetkilendirmeAkisOperationCompleted(object arg) {
            if ((this.CreateTicariUyeYetkilendirmeAkisCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateTicariUyeYetkilendirmeAkisCompleted(this, new CreateTicariUyeYetkilendirmeAkisCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateTicariUcretIadeAkis", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CreateTicariUcretIadeAkis(
                    long IRIS_ID, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string GPS_LOKASYON, 
                    string KAYNAK) {
            object[] results = this.Invoke("CreateTicariUcretIadeAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ESKI_GRUP,
                        ESKI_PESIN,
                        ESKI_TAKSITLI,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        GPS_LOKASYON,
                        KAYNAK});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CreateTicariUcretIadeAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string GPS_LOKASYON, 
                    string KAYNAK) {
            this.CreateTicariUcretIadeAkisAsync(IRIS_ID, UYE_NO, UYE_ADI, BAYI_AD, BAYI_KOD, BAYI_BOLGE_KOD, BAYI_YONETICI, ACIKLAMA, ESKI_GRUP, ESKI_PESIN, ESKI_TAKSITLI, ADRES, IL, ILCE, DEMOGRAFIK, BAYI_MAIL, POTANSIYEL_NO, POTANSIYEL_ADI, GPS_LOKASYON, KAYNAK, null);
        }
        
        /// <remarks/>
        public void CreateTicariUcretIadeAkisAsync(
                    long IRIS_ID, 
                    System.Nullable<int> UYE_NO, 
                    string UYE_ADI, 
                    string BAYI_AD, 
                    string BAYI_KOD, 
                    string BAYI_BOLGE_KOD, 
                    string BAYI_YONETICI, 
                    string ACIKLAMA, 
                    string ESKI_GRUP, 
                    string ESKI_PESIN, 
                    string ESKI_TAKSITLI, 
                    string ADRES, 
                    string IL, 
                    string ILCE, 
                    string DEMOGRAFIK, 
                    string BAYI_MAIL, 
                    System.Nullable<int> POTANSIYEL_NO, 
                    string POTANSIYEL_ADI, 
                    string GPS_LOKASYON, 
                    string KAYNAK, 
                    object userState) {
            if ((this.CreateTicariUcretIadeAkisOperationCompleted == null)) {
                this.CreateTicariUcretIadeAkisOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateTicariUcretIadeAkisOperationCompleted);
            }
            this.InvokeAsync("CreateTicariUcretIadeAkis", new object[] {
                        IRIS_ID,
                        UYE_NO,
                        UYE_ADI,
                        BAYI_AD,
                        BAYI_KOD,
                        BAYI_BOLGE_KOD,
                        BAYI_YONETICI,
                        ACIKLAMA,
                        ESKI_GRUP,
                        ESKI_PESIN,
                        ESKI_TAKSITLI,
                        ADRES,
                        IL,
                        ILCE,
                        DEMOGRAFIK,
                        BAYI_MAIL,
                        POTANSIYEL_NO,
                        POTANSIYEL_ADI,
                        GPS_LOKASYON,
                        KAYNAK}, this.CreateTicariUcretIadeAkisOperationCompleted, userState);
        }
        
        private void OnCreateTicariUcretIadeAkisOperationCompleted(object arg) {
            if ((this.CreateTicariUcretIadeAkisCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateTicariUcretIadeAkisCompleted(this, new CreateTicariUcretIadeAkisCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateBayiFotografDegisikligiAkis", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CreateBayiFotografDegisikligiAkis(long IRIS_ID, string personel_id, string personel_ad_soyad, string calistigi_bayi, string onceki_calistigi_bayi, string bayi_id, string foto_adres, string gorevi, string calisma_sekli, string ise_baslama_tarihi, string TeknikServisYonetici, string Satistemsilcisi) {
            object[] results = this.Invoke("CreateBayiFotografDegisikligiAkis", new object[] {
                        IRIS_ID,
                        personel_id,
                        personel_ad_soyad,
                        calistigi_bayi,
                        onceki_calistigi_bayi,
                        bayi_id,
                        foto_adres,
                        gorevi,
                        calisma_sekli,
                        ise_baslama_tarihi,
                        TeknikServisYonetici,
                        Satistemsilcisi});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CreateBayiFotografDegisikligiAkisAsync(long IRIS_ID, string personel_id, string personel_ad_soyad, string calistigi_bayi, string onceki_calistigi_bayi, string bayi_id, string foto_adres, string gorevi, string calisma_sekli, string ise_baslama_tarihi, string TeknikServisYonetici, string Satistemsilcisi) {
            this.CreateBayiFotografDegisikligiAkisAsync(IRIS_ID, personel_id, personel_ad_soyad, calistigi_bayi, onceki_calistigi_bayi, bayi_id, foto_adres, gorevi, calisma_sekli, ise_baslama_tarihi, TeknikServisYonetici, Satistemsilcisi, null);
        }
        
        /// <remarks/>
        public void CreateBayiFotografDegisikligiAkisAsync(long IRIS_ID, string personel_id, string personel_ad_soyad, string calistigi_bayi, string onceki_calistigi_bayi, string bayi_id, string foto_adres, string gorevi, string calisma_sekli, string ise_baslama_tarihi, string TeknikServisYonetici, string Satistemsilcisi, object userState) {
            if ((this.CreateBayiFotografDegisikligiAkisOperationCompleted == null)) {
                this.CreateBayiFotografDegisikligiAkisOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateBayiFotografDegisikligiAkisOperationCompleted);
            }
            this.InvokeAsync("CreateBayiFotografDegisikligiAkis", new object[] {
                        IRIS_ID,
                        personel_id,
                        personel_ad_soyad,
                        calistigi_bayi,
                        onceki_calistigi_bayi,
                        bayi_id,
                        foto_adres,
                        gorevi,
                        calisma_sekli,
                        ise_baslama_tarihi,
                        TeknikServisYonetici,
                        Satistemsilcisi}, this.CreateBayiFotografDegisikligiAkisOperationCompleted, userState);
        }
        
        private void OnCreateBayiFotografDegisikligiAkisOperationCompleted(object arg) {
            if ((this.CreateBayiFotografDegisikligiAkisCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateBayiFotografDegisikligiAkisCompleted(this, new CreateBayiFotografDegisikligiAkisCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CreateBayiBelgeOnayAkis", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string CreateBayiBelgeOnayAkis(long IrisTalepID, string PersonelId, string PersonelAdSoyad, string PersonelGorevi, string CalistigiBayi, string BayiKodu, string TeknikServisYonetici, string SatisTemsilcisi, string TeknikServisMuduru, BayiBelgeDokuman[] BayiBelgeDokuman) {
            object[] results = this.Invoke("CreateBayiBelgeOnayAkis", new object[] {
                        IrisTalepID,
                        PersonelId,
                        PersonelAdSoyad,
                        PersonelGorevi,
                        CalistigiBayi,
                        BayiKodu,
                        TeknikServisYonetici,
                        SatisTemsilcisi,
                        TeknikServisMuduru,
                        BayiBelgeDokuman});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void CreateBayiBelgeOnayAkisAsync(long IrisTalepID, string PersonelId, string PersonelAdSoyad, string PersonelGorevi, string CalistigiBayi, string BayiKodu, string TeknikServisYonetici, string SatisTemsilcisi, string TeknikServisMuduru, BayiBelgeDokuman[] BayiBelgeDokuman) {
            this.CreateBayiBelgeOnayAkisAsync(IrisTalepID, PersonelId, PersonelAdSoyad, PersonelGorevi, CalistigiBayi, BayiKodu, TeknikServisYonetici, SatisTemsilcisi, TeknikServisMuduru, BayiBelgeDokuman, null);
        }
        
        /// <remarks/>
        public void CreateBayiBelgeOnayAkisAsync(long IrisTalepID, string PersonelId, string PersonelAdSoyad, string PersonelGorevi, string CalistigiBayi, string BayiKodu, string TeknikServisYonetici, string SatisTemsilcisi, string TeknikServisMuduru, BayiBelgeDokuman[] BayiBelgeDokuman, object userState) {
            if ((this.CreateBayiBelgeOnayAkisOperationCompleted == null)) {
                this.CreateBayiBelgeOnayAkisOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCreateBayiBelgeOnayAkisOperationCompleted);
            }
            this.InvokeAsync("CreateBayiBelgeOnayAkis", new object[] {
                        IrisTalepID,
                        PersonelId,
                        PersonelAdSoyad,
                        PersonelGorevi,
                        CalistigiBayi,
                        BayiKodu,
                        TeknikServisYonetici,
                        SatisTemsilcisi,
                        TeknikServisMuduru,
                        BayiBelgeDokuman}, this.CreateBayiBelgeOnayAkisOperationCompleted, userState);
        }
        
        private void OnCreateBayiBelgeOnayAkisOperationCompleted(object arg) {
            if ((this.CreateBayiBelgeOnayAkisCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CreateBayiBelgeOnayAkisCompleted(this, new CreateBayiBelgeOnayAkisCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class BayiBelgeDokuman {
        
        private string dokumanIdField;
        
        private string dokumanAdiField;
        
        private string dokumanLinkiField;
        
        private System.DateTime dokumanYuklemeTarihiField;
        
        /// <remarks/>
        public string DokumanId {
            get {
                return this.dokumanIdField;
            }
            set {
                this.dokumanIdField = value;
            }
        }
        
        /// <remarks/>
        public string DokumanAdi {
            get {
                return this.dokumanAdiField;
            }
            set {
                this.dokumanAdiField = value;
            }
        }
        
        /// <remarks/>
        public string DokumanLinki {
            get {
                return this.dokumanLinkiField;
            }
            set {
                this.dokumanLinkiField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime DokumanYuklemeTarihi {
            get {
                return this.dokumanYuklemeTarihiField;
            }
            set {
                this.dokumanYuklemeTarihiField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CancelDTSatisliTicariYayinAcmaAkisiCompletedEventHandler(object sender, CancelDTSatisliTicariYayinAcmaAkisiCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CancelDTSatisliTicariYayinAcmaAkisiCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CancelDTSatisliTicariYayinAcmaAkisiCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CreateDTSatisliTicariYayinAcmaAkisiCompletedEventHandler(object sender, CreateDTSatisliTicariYayinAcmaAkisiCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateDTSatisliTicariYayinAcmaAkisiCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateDTSatisliTicariYayinAcmaAkisiCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CreateTicariGrupDegisikligiAkisCompletedEventHandler(object sender, CreateTicariGrupDegisikligiAkisCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateTicariGrupDegisikligiAkisCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateTicariGrupDegisikligiAkisCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CreateTicariFiyatIstisnaAkisCompletedEventHandler(object sender, CreateTicariFiyatIstisnaAkisCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateTicariFiyatIstisnaAkisCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateTicariFiyatIstisnaAkisCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CreateTicariUyeYetkilendirmeAkisCompletedEventHandler(object sender, CreateTicariUyeYetkilendirmeAkisCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateTicariUyeYetkilendirmeAkisCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateTicariUyeYetkilendirmeAkisCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CreateTicariUcretIadeAkisCompletedEventHandler(object sender, CreateTicariUcretIadeAkisCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateTicariUcretIadeAkisCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateTicariUcretIadeAkisCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CreateBayiFotografDegisikligiAkisCompletedEventHandler(object sender, CreateBayiFotografDegisikligiAkisCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateBayiFotografDegisikligiAkisCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateBayiFotografDegisikligiAkisCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void CreateBayiBelgeOnayAkisCompletedEventHandler(object sender, CreateBayiBelgeOnayAkisCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CreateBayiBelgeOnayAkisCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CreateBayiBelgeOnayAkisCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591