import React, { createContext, useContext, useCallback, useEffect, ReactNode } from 'react'

/**
 * Analytics event interface
 */
export interface AnalyticsEvent {
  name: string
  properties?: Record<string, unknown>
  timestamp?: number
  userId?: string
  sessionId?: string
}

/**
 * User properties interface
 */
export interface UserProperties {
  id?: string
  email?: string
  name?: string
  plan?: string
  [key: string]: unknown
}

/**
 * Page view properties interface
 */
export interface PageViewProperties {
  path: string
  title?: string
  referrer?: string
  search?: string
  [key: string]: unknown
}

/**
 * Analytics provider interface
 */
export interface AnalyticsAdapter {
  // eslint-disable-next-line no-unused-vars
  initialize: (config: Record<string, unknown>) => void | Promise<void>
  // eslint-disable-next-line no-unused-vars
  track: (event: AnalyticsEvent) => void | Promise<void>
  // eslint-disable-next-line no-unused-vars
  page: (properties: PageViewProperties) => void | Promise<void>
  // eslint-disable-next-line no-unused-vars
  identify: (userId: string, properties?: UserProperties) => void | Promise<void>
  reset: () => void | Promise<void>
}

/**
 * Analytics context interface
 */
interface AnalyticsContextType {
  // eslint-disable-next-line no-unused-vars
  track: (eventName: string, properties?: Record<string, unknown>) => void
  // eslint-disable-next-line no-unused-vars
  page: (pageName?: string, properties?: Record<string, unknown>) => void
  // eslint-disable-next-line no-unused-vars
  identify: (userId: string, properties?: UserProperties) => void
  reset: () => void
  // eslint-disable-next-line no-unused-vars
  setUserProperty: (key: string, value: unknown) => void
  isEnabled: boolean
  adapter: AnalyticsAdapter | null
}

/**
 * Props for AnalyticsProvider component
 */
interface AnalyticsProviderProps {
  children: ReactNode
  adapter?: AnalyticsAdapter
  enabled?: boolean
  debug?: boolean
  userId?: string
  userProperties?: UserProperties
  trackPageViews?: boolean
  ignoreRoutes?: string[]
  // eslint-disable-next-line no-unused-vars
  beforeTrack?: (event: AnalyticsEvent) => AnalyticsEvent | null
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined)

/**
 * Console analytics adapter for development
 */
class ConsoleAnalyticsAdapter implements AnalyticsAdapter {
  private debug: boolean

  constructor(debug = true) {
    this.debug = debug
  }

  initialize(config: Record<string, unknown>): void {
    if (this.debug) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('[Analytics] Initialized with config:', config)
      }
    }
  }

  track(event: AnalyticsEvent): void {
    if (this.debug) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('[Analytics] Track:', event)
      }
    }
  }

  page(properties: PageViewProperties): void {
    if (this.debug) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('[Analytics] Page view:', properties)
      }
    }
  }

  identify(userId: string, properties?: UserProperties): void {
    if (this.debug) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('[Analytics] Identify:', { userId, properties })
      }
    }
  }

  reset(): void {
    if (this.debug) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('[Analytics] Reset')
      }
    }
  }
}

/**
 * Analytics provider that manages tracking and user analytics
 * @component
 */
export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({
  children,
  adapter,
  enabled = true,
  debug = false,
  userId,
  userProperties,
  trackPageViews = true,
  ignoreRoutes = [],
  beforeTrack,
}) => {
  // Use console adapter in development if no adapter provided
  const analyticsAdapter = React.useMemo(() => adapter ?? (debug ? new ConsoleAnalyticsAdapter(debug) : null), [adapter, debug])

  /**
   * Track analytics event
   */
  const track = useCallback(
    (eventName: string, properties?: Record<string, unknown>) => {
      if (!enabled || !analyticsAdapter) return

      try {
        const event: AnalyticsEvent = {
          name: eventName,
          properties,
          timestamp: Date.now(),
          userId,
        }

        // Apply beforeTrack transformation if provided
        const transformedEvent = beforeTrack ? beforeTrack(event) : event

        // Skip if beforeTrack returns null
        if (!transformedEvent) return

        void analyticsAdapter.track(transformedEvent)
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[Analytics] Failed to track event:', _error)
        }
      }
    },
    [enabled, analyticsAdapter, userId, beforeTrack],
  )

  /**
   * Track page view
   */
  const page = useCallback(
    (pageName?: string, properties?: Record<string, unknown>) => {
      if (!enabled || !analyticsAdapter) return

      try {
        const pageProperties: PageViewProperties = {
          path: window.location.pathname,
          title: pageName ?? window.document.title,
          referrer: window.document.referrer,
          search: window.location.search,
          ...properties,
        }

        // Check if route should be ignored
        if (ignoreRoutes.some((route) => pageProperties.path.includes(route))) {
          return
        }

        void analyticsAdapter.page(pageProperties)
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[Analytics] Failed to track page view:', _error)
        }
      }
    },
    [enabled, analyticsAdapter, ignoreRoutes],
  )

  /**
   * Identify user
   */
  const identify = useCallback(
    (newUserId: string, properties?: UserProperties) => {
      if (!enabled || !analyticsAdapter) return

      try {
        void analyticsAdapter.identify(newUserId, properties)
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[Analytics] Failed to identify user:', _error)
        }
      }
    },
    [enabled, analyticsAdapter],
  )

  /**
   * Reset analytics (logout)
   */
  const reset = useCallback(() => {
    if (!analyticsAdapter) return

    try {
      void analyticsAdapter.reset()
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[Analytics] Failed to reset:', _error)
      }
    }
  }, [analyticsAdapter])

  /**
   * Set user property
   */
  const setUserProperty = useCallback(
    (key: string, value: unknown) => {
      if (!enabled || !analyticsAdapter || !userId) return

      try {
        void analyticsAdapter.identify(userId, { [key]: value })
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[Analytics] Failed to set user property:', _error)
        }
      }
    },
    [enabled, analyticsAdapter, userId],
  )

  // Initialize analytics adapter
  useEffect(() => {
    if (!analyticsAdapter || !enabled) return

    try {
      void analyticsAdapter.initialize({
        debug,
        userId,
        userProperties,
      })
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[Analytics] Failed to initialize:', _error)
      }
    }
  }, [analyticsAdapter, enabled, debug, userId, userProperties])

  // Identify user when userId changes
  useEffect(() => {
    if (!userId || !enabled || !analyticsAdapter) return

    identify(userId, userProperties)
  }, [userId, userProperties, identify, enabled, analyticsAdapter])

  // Track page views on route change
  useEffect(() => {
    if (!trackPageViews || !enabled) return

    // Initial page view
    page()

    // Listen for route changes (for SPAs)
    const handleRouteChange = () => {
      page()
    }

    // Listen to popstate for browser navigation
    void window.addEventListener('popstate', handleRouteChange)

    // Override pushState and replaceState
    const originalPushState = window.history.pushState
    const originalReplaceState = window.history.replaceState

    window.history.pushState = function (...args) {
      void originalPushState.apply(window.history, args)
      handleRouteChange()
    }

    window.history.replaceState = function (...args) {
      void originalReplaceState.apply(window.history, args)
      handleRouteChange()
    }

    return () => {
      void window.removeEventListener('popstate', handleRouteChange)
      window.history.pushState = originalPushState
      window.history.replaceState = originalReplaceState
    }
  }, [trackPageViews, enabled, page])

  const contextValue: AnalyticsContextType = {
    track,
    page,
    identify,
    reset,
    setUserProperty,
    isEnabled: enabled,
    adapter: analyticsAdapter,
  }

  return <AnalyticsContext.Provider value={contextValue}>{children}</AnalyticsContext.Provider>
}

/**
 * Hook to access analytics context
 * @returns Analytics context
 * @throws Error if used outside of AnalyticsProvider
 */
export const useAnalytics = (): AnalyticsContextType => {
  const context = useContext(AnalyticsContext)
  if (!context) {
    throw new Error('useAnalytics must be used within AnalyticsProvider')
  }
  return context
}

/**
 * Hook to track events
 * @returns Track function
 */
export const useTrack = () => {
  const { track } = useAnalytics()
  return track
}

/**
 * Hook to track component mount
 * @param eventName - Event name to track
 * @param properties - Event properties
 */
export const useTrackMount = (eventName: string, properties?: Record<string, unknown>) => {
  const track = useTrack()

  useEffect(() => {
    track(eventName, properties)
  }, []) // eslint-disable-line react-hooks/exhaustive-deps
}

/**
 * Hook to track clicks with proper event handling
 * @param eventName - Event name to track
 * @param properties - Event properties
 * @returns Click handler
 */
export const useTrackClick = (eventName: string, properties?: Record<string, unknown>): ((_event?: React.MouseEvent) => void) => {
  const track = useTrack()

  return useCallback(
    (event?: React.MouseEvent) => {
      track(eventName, {
        ...properties,
        elementText: event?.currentTarget?.textContent,
        elementType: event?.currentTarget?.tagName,
      })
    },
    [track, eventName, properties],
  )
}
