import React, { ReactNode } from 'react'
import { QueryClient, QueryClientProvider, Query<PERSON>ache, MutationCache } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

/**
 * Props for QueryProvider component
 */
interface QueryProviderProps {
  children: ReactNode
  client?: QueryClient
  enableDevtools?: boolean
  devtoolsPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  onError?: (_error: unknown) => void
  onSuccess?: (_data: unknown) => void
}

/**
 * Create default query client with optimized settings
 */
const createQueryClient = (onError?: (_error: unknown) => void, onSuccess?: (_data: unknown) => void): QueryClient => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Stale time of 5 minutes
        staleTime: 5 * 60 * 1000,
        // Cache time of 10 minutes
        gcTime: 10 * 60 * 1000,
        // Retry failed requests 3 times with exponential backoff
        retry: (failureCount, error) => {
          // Don't retry on 4xx errors
          if (error instanceof Error) {
            const message = error.message.toLowerCase()
            if (message.includes('4') && message.includes('error')) {
              return false
            }
          }
          return failureCount < 3
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch on window focus in production
        refetchOnWindowFocus: process.env.NODE_ENV === 'production',
        // Refetch on reconnect
        refetchOnReconnect: 'always',
        // Keep previous data while fetching
        placeholderData: (previousData: unknown) => previousData,
      },
      mutations: {
        // Retry mutations once
        retry: 1,
        retryDelay: 1000,
      },
    },
    queryCache: new QueryCache({
      onError: (_error, query) => {
        // Global error handling for queries
        if (process.env.NODE_ENV === 'development') {
          console.error(`Query error for ${query.queryKey}:`, _error)
        }
        onError?.(_error)
      },
      onSuccess: (_data) => {
        onSuccess?.(_data)
      },
    }),
    mutationCache: new MutationCache({
      onError: (_error, _variables, _context, mutation) => {
        // Global error handling for mutations
        if (process.env.NODE_ENV === 'development') {
          console.error(`Mutation error for ${mutation.options.mutationKey}:`, _error)
        }
        onError?.(_error)
      },
      onSuccess: (_data) => {
        onSuccess?.(_data)
      },
    }),
  })
}

// Create a default client instance
let defaultQueryClient: QueryClient | null = null

/**
 * Get or create the default query client
 */
const getDefaultQueryClient = (onError?: (_error: unknown) => void, onSuccess?: (_data: unknown) => void): QueryClient => {
  defaultQueryClient ??= createQueryClient(onError, onSuccess)
  return defaultQueryClient
}

/**
 * React Query provider that configures query client for the application
 * @component
 */
export const QueryProvider: React.FC<QueryProviderProps> = ({
  children,
  client,
  enableDevtools = process.env.NODE_ENV === 'development',
  devtoolsPosition = 'bottom-right' as const,
  onError,
  onSuccess,
}) => {
  const queryClient = client ?? getDefaultQueryClient(onError, onSuccess)

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {enableDevtools && <ReactQueryDevtools initialIsOpen={false} position={devtoolsPosition as any} buttonPosition={devtoolsPosition} />}
    </QueryClientProvider>
  )
}

/**
 * Hook to get the query client instance
 * @returns Query client instance
 */
export { useQueryClient } from '@tanstack/react-query'

/**
 * Re-export commonly used hooks and utilities
 */
export {
  useQuery,
  useMutation,
  useInfiniteQuery,
  useQueries,
  useIsFetching,
  useIsMutating,
  useMutationState,
  useSuspenseQuery,
  useSuspenseInfiniteQuery,
  useSuspenseQueries,
  usePrefetchQuery,
  usePrefetchInfiniteQuery,
} from '@tanstack/react-query'

/**
 * Utility function to invalidate queries by key
 * @param queryClient - Query client instance
 * @param queryKey - Query key to invalidate
 */
export const invalidateQueries = async (queryClient: QueryClient, queryKey: unknown[]): Promise<void> => {
  await queryClient.invalidateQueries({ queryKey })
}

/**
 * Utility function to refetch queries by key
 * @param queryClient - Query client instance
 * @param queryKey - Query key to refetch
 */
export const refetchQueries = async (queryClient: QueryClient, queryKey: unknown[]): Promise<void> => {
  await queryClient.refetchQueries({ queryKey })
}

/**
 * Utility function to cancel queries by key
 * @param queryClient - Query client instance
 * @param queryKey - Query key to cancel
 */
export const cancelQueries = async (queryClient: QueryClient, queryKey: unknown[]): Promise<void> => {
  await queryClient.cancelQueries({ queryKey })
}

/**
 * Utility function to reset queries by key
 * @param queryClient - Query client instance
 * @param queryKey - Query key to reset
 */
export const resetQueries = async (queryClient: QueryClient, queryKey: unknown[]): Promise<void> => {
  await queryClient.resetQueries({ queryKey })
}
