import React from 'react'
import { IConfiguration } from 'wface'
import screenList from './screenList'
import AuthService from '@/services/wface/authService'
import AppWrapper from '@/services/wface/appWrapper'
import CustomTopbar from '@/components/layout/CustomTopbar'
import LazyScreenWrapper from '@/components/LazyScreenWrapper/LazyScreenWrapper'

const configuration: IConfiguration = {
  projectName: 'Digiflow',
  screenList: screenList,
  basename: 'react',
  //  defaultRoute: '/main/inbox',
  useAuthService: AuthService,
  wrapApp: (children: React.ReactNode) => <AppWrapper>{children}</AppWrapper>,
  search: false,
  authRequired: false, // Keep this as is, auth logic is separate
  hideSidebar: true,
  hideTopbar: false, // Hide default topbar to use custom one
  customTopbar: CustomTopbar, // Enable custom topbar
  components: {
    ScreenWrapper: LazyScreenWrapper, // Wrap each screen with Suspense for lazy loading
  },
  theme: {
    shape: { borderRadius: 6 }, // Consistent with --df-radius
    spacing: 4, // Default MUI spacing unit
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif', // Consistent with --df-font-family
      fontSize: 13, // Consistent with --df-font-size
      button: { textTransform: 'none', fontWeight: 600 },
    },
    palette: {
      primary: { main: '#5c2d91', light: '#8c58c5', dark: '#4b2173' }, // Main purple theme
      secondary: { main: '#805ad5' }, // Accent purple
      success: { main: '#38a169' },
      warning: { main: '#dd6b20' },
      error: { main: '#e53e3e' },
      info: { main: '#4a5568' }, // Darker info color
      background: { default: '#f7fafc', paper: '#ffffff' }, // Light gray default, white for paper
      text: { primary: '#2d3748', secondary: '#718096' }, // Text colors
    },
    components: {
      MuiButton: {
        defaultProps: { variant: 'contained', disableElevation: true },
        styleOverrides: {
          root: {
            borderRadius: 6, // Consistent with --df-radius
            padding: '6px 16px',
            fontWeight: 500, // Standard weight for buttons
            boxShadow: 'none',
            '&:hover': {
              backgroundColor: '#4b2173', // Darker purple on hover
              boxShadow: '0 1px 3px rgba(0,0,0,.07)',
            },
            outlined: {
              borderColor: '#5c2d91',
              color: '#5c2d91',
              '&:hover': {
                backgroundColor: 'rgba(92, 45, 145, 0.04)', // Light purple tint on hover
                borderColor: '#4b2173',
              },
              text: {
                color: '#5c2d91',
                '&:hover': {
                  backgroundColor: 'rgba(92, 45, 145, 0.04)', // Light purple tint on hover
                },
                MuiPaper: {
                  styleOverrides: {
                    root: {
                      borderRadius: 6,
                      border: '1px solid #e2e8f0', // Consistent with --df-border-color
                    },
                    elevation1: {
                      boxShadow: '0 1px 2px rgba(0,0,0,.05)', // Softer shadow
                    },
                    MuiCard: {
                      styleOverrides: {
                        root: {
                          borderRadius: 6,
                          border: '1px solid #e2e8f0',
                          boxShadow: '0 1px 2px rgba(0,0,0,.05)',
                        },
                        MuiTabs: {
                          styleOverrides: {
                            indicator: {
                              height: 3,
                              borderRadius: 3,
                              backgroundColor: '#5c2d91', // Purple indicator
                            },
                            MuiTab: {
                              styleOverrides: {
                                root: {
                                  color: '#4a5568', // Default tab text color
                                  '&.Mui-selected': {
                                    color: '#5c2d91', // Purple for selected tab
                                    fontWeight: 600,
                                  },
                                  MuiAppBar: {
                                    styleOverrides: {
                                      root: {
                                        background: '#5c2d91', // Solid purple header, like ASP.NET
                                        boxShadow: '0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12)', // Standard app bar shadow
                                      },
                                      MuiDrawer: {
                                        styleOverrides: {
                                          paper: {
                                            background: '#ffffff', // White drawer background
                                            borderRight: '1px solid #e2e8f0',
                                          },
                                          MuiTextField: {
                                            defaultProps: {
                                              variant: 'outlined', // Outlined is generally more modern
                                              size: 'small',
                                              autoComplete: 'off',
                                            },
                                            styleOverrides: {
                                              root: {
                                                fontSize: '0.875rem', // 14px
                                                '.MuiOutlinedInput-root': {
                                                  borderRadius: 6, // Consistent radius
                                                },
                                                MuiInputBase: {
                                                  styleOverrides: {
                                                    root: {
                                                      fontSize: '0.875rem',
                                                    },
                                                    input: {
                                                      fontSize: '0.875rem',
                                                      // padding: '10px 12px', // Adjust padding for outlined variant
                                                    },
                                                    MuiSelect: {
                                                      defaultProps: {
                                                        variant: 'outlined', // Outlined for consistency
                                                        size: 'small',
                                                      },
                                                      styleOverrides: {
                                                        select: {
                                                          fontSize: '0.875rem',
                                                          // padding: '10px 12px', // Adjust padding for outlined variant
                                                        },
                                                        MuiGrid: {
                                                          styleOverrides: {
                                                            root: {
                                                              fontSize: '0.875rem',
                                                            },
                                                            MuiListItemButton: {
                                                              styleOverrides: {
                                                                root: {
                                                                  color: '#2d3748', // Default text color
                                                                  borderRadius: 6,
                                                                  '&.Mui-selected': {
                                                                    backgroundColor: 'rgba(92, 45, 145, 0.08)', // Light purple for selected items
                                                                    color: '#5c2d91',
                                                                    fontWeight: 600,
                                                                    '& .MuiListItemIcon-root': {
                                                                      color: '#5c2d91',
                                                                    },
                                                                    '&:hover': {
                                                                      backgroundColor: 'rgba(92, 45, 145, 0.04)',
                                                                    },
                                                                    MuiListItemIcon: {
                                                                      styleOverrides: {
                                                                        root: {
                                                                          color: '#718096', // Muted icon color
                                                                          minWidth: 40,
                                                                        },
                                                                        MuiDialog: {
                                                                          styleOverrides: {
                                                                            paper: {
                                                                              borderRadius: 8, // Slightly larger radius for dialogs
                                                                              boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
                                                                            },
                                                                            MuiDialogTitle: {
                                                                              styleOverrides: {
                                                                                root: {
                                                                                  fontWeight: 600,
                                                                                  fontSize: '1.125rem', // 18px
                                                                                },
                                                                                MuiDialogContent: {
                                                                                  styleOverrides: {
                                                                                    root: {
                                                                                      fontSize: '0.875rem', // 14px
                                                                                    },
                                                                                    MuiChip: {
                                                                                      styleOverrides: {
                                                                                        root: {
                                                                                          borderRadius: 16, // Pill shape for chips
                                                                                          fontWeight: 500,
                                                                                        },
                                                                                        colorPrimary: {
                                                                                          backgroundColor: '#5c2d91',
                                                                                          color: '#fff',
                                                                                        },
                                                                                        colorSecondary: {
                                                                                          backgroundColor: '#e2d7f1', // Light purple background
                                                                                          color: '#4b2173', // Dark purple text
                                                                                        },
                                                                                        MuiTooltip: {
                                                                                          styleOverrides: {
                                                                                            tooltip: {
                                                                                              backgroundColor: '#2d3748', // Dark tooltip
                                                                                              borderRadius: 4,
                                                                                              fontSize: '0.75rem', // 12px
                                                                                            },
                                                                                            arrow: {
                                                                                              color: '#2d3748',
                                                                                            },
                                                                                          },
                                                                                        },
                                                                                      },
                                                                                    },
                                                                                  },
                                                                                }

export default configuration
