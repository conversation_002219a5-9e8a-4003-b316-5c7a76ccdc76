import { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { safeNavigate } from '@/utils/helpers/navigation/navigationHelpers'
import { NavigationState } from '@/types'

const NAVIGATION_STATE_KEY = 'navigation_state'
const MAX_HISTORY_SIZE = 50

// Add openScreenById as a parameter to the hook
export const useNavigationHistory = (openScreenById: (screen: string, extra: any) => void) => {
  const navigate = useNavigate()

  const getNavigationState = (): NavigationState => {
    const defaultState: NavigationState = {
      history: [],
      currentIndex: -1,
      maxSize: MAX_HISTORY_SIZE,
    }

    try {
      const state = JSON.parse(localStorage.getItem(NAVIGATION_STATE_KEY) ?? JSON.stringify(defaultState))
      if (process.env.NODE_ENV === 'development') {
        void console.warn('Current Navigation State:', state)
      }
      return state
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to parse navigation state')
      }
      return defaultState
    }
  } // End of getNavigationState

  const saveNavigationState = (state: NavigationState) => {
    localStorage.setItem(NAVIGATION_STATE_KEY, JSON.stringify(state))
  }

  const addToHistory = useCallback((pathname: string, params: string) => {
    const state = getNavigationState()
    const currentPath = pathname.split('?')[0]

    const lastEntry = state.history[state.currentIndex]
    if (lastEntry && lastEntry.pathname.split('?')[0] === currentPath) {
      state.history[state.currentIndex] = {
        pathname,
        params,
        timestamp: Date.now(),
      }
    } else {
      state.history = state.history.slice(0, state.currentIndex + 1)
      state.history.push({
        pathname,
        params,
        timestamp: Date.now(),
      })

      if (state.history.length > state.maxSize) {
        state.history = state.history.slice(state.history.length - state.maxSize)
      }

      state.currentIndex = state.history.length - 1
    }

    saveNavigationState(state)
  }, [])

  const goBack = useCallback(() => {
    const state = getNavigationState()
    if (state.currentIndex > 0) {
      state.currentIndex--
      const entry = state.history[state.currentIndex]
      saveNavigationState(state)

      const screen = entry.pathname.split('/main/')[1].split('?')[0]
      openScreenById(screen, null)

      safeNavigate(navigate, {
        pathname: entry.pathname.split('?')[0],
        search: entry.params,
      })
      return true
    }
    return false
  }, [navigate, openScreenById])

  const goForward = useCallback(() => {
    const state = getNavigationState()
    if (state.currentIndex < state.history.length - 1) {
      state.currentIndex++
      const entry = state.history[state.currentIndex]
      saveNavigationState(state)

      const screen = entry.pathname.split('/main/')[1].split('?')[0]
      openScreenById(screen, null)

      safeNavigate(navigate, {
        pathname: entry.pathname.split('?')[0],
        search: entry.params,
      })
      return true
    }
    return false
  }, [navigate, openScreenById])

  return {
    addToHistory,
    goBack,
    goForward,
    getFullHistory: getNavigationState,
    canGoBack: () => getNavigationState().currentIndex > 0,
    canGoForward: () => {
      const state = getNavigationState()
      return state.currentIndex < state.history.length - 1
    },
  }
} // End of useNavigationHistory