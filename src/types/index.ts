export * from './UserTypes'
export * from './WorkflowTypes'
export * from './CommonTypes'
export * from './InboxTypes'
export * from './OrganizationTypes'
export * from './TableTypes'
export * from './FilterTypes'
export * from './Navigation'

// Additional shared types (migrated from shared folder)
export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasNext: boolean
  hasPrevious: boolean
}

export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
}

export type SortDirection = 'asc' | 'desc'

export interface SortOptions {
  field: string
  direction: SortDirection
}
