/**
 * Node.js global type declarations for browser/Node.js compatibility
 */

declare global {
  // Node.js global object
  const global: typeof globalThis

  // Node.js Buffer object
  const Buffer: {
    from(str: string, encoding?: string): Buffer
    alloc(size: number): Buffer
    concat(buffers: Buffer[]): Buffer
  }

  // Base64 encoding functions
  const btoa: (str: string) => string
  const atob: (str: string) => string

  interface Buffer {
    toString(encoding?: string): string
    length: number
  }
}

export {}
