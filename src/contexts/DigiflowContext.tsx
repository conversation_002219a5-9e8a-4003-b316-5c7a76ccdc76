import { IUser } from '@/types'
import React, { createContext, useContext, useState } from 'react'

interface DigiflowContextProps {
  isSysAdmin: boolean
  isMobile: boolean
  isTest: boolean
  activeUser: IUser | null
  updateIsSysAdmin: (_isSysAdmin: boolean) => void
  updateIsMobile: (_isMobile: boolean) => void
  updateIsTest: (_isTest: boolean) => void
  updateActiveUser: (_user: IUser | null) => void
}

interface DigiflowProviderProps {
  children: React.ReactNode
  defaultIsSysAdmin?: boolean
  defaultIsMobile?: boolean
  defaultIsTest?: boolean
  defaultActiveUser?: IUser | null
}

const initialContext: DigiflowContextProps = {
  isSysAdmin: false,
  isMobile: false,
  isTest: false,
  activeUser: null,
  updateIsSysAdmin: () => {},
  updateIsMobile: () => {},
  updateIsTest: () => {},
  updateActiveUser: () => {},
}

export const DigiflowContext = createContext<DigiflowContextProps>(initialContext)

export const useDigiflow = () => {
  const context = useContext(DigiflowContext)
  if (!context) throw new Error('useWorkflow must be used within a WorkflowProvider')
  return context
}

export const DigiflowProvider: React.FC<DigiflowProviderProps> = ({
  children,
  defaultIsSysAdmin = false,
  defaultIsMobile = false,
  defaultIsTest = false,
  defaultActiveUser = null,
}) => {
  const [isSysAdmin, setIsSysAdmin] = useState<boolean>(defaultIsSysAdmin)
  const [isMobile, setIsMobile] = useState<boolean>(defaultIsMobile)
  const [isTest, setIsTest] = useState<boolean>(defaultIsTest)
  const [activeUser, setActiveUser] = useState<IUser | null>(defaultActiveUser)

  const updateIsSysAdmin = (isSysAdmin: boolean) => setIsSysAdmin(isSysAdmin)
  const updateIsMobile = (isMobile: boolean) => setIsMobile(isMobile)
  const updateIsTest = (isTest: boolean) => setIsTest(isTest)
  const updateActiveUser = (user: IUser | null) => setActiveUser(user)

  return (
    <DigiflowContext.Provider
      value={{
        isSysAdmin: isSysAdmin ?? defaultIsSysAdmin,
        isMobile: isMobile || defaultIsMobile,
        isTest: isTest || defaultIsTest,
        activeUser: activeUser ?? defaultActiveUser,
        updateIsSysAdmin,
        updateIsMobile,
        updateIsTest,
        updateActiveUser,
      }}
    >
      {children}
    </DigiflowContext.Provider>
  )
}
