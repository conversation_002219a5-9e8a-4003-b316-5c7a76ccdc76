import React, { createContext, useContext, useState } from 'react'

interface ModalContextState {
  isOpen: boolean
  isProcessing: boolean
  setIsOpen: (_open: boolean) => void
  setIsProcessing: (_processing: boolean) => void
}

const ModalContext = createContext<ModalContextState | undefined>(undefined)

export const ModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  return <ModalContext.Provider value={{ isOpen, isProcessing, setIsOpen, setIsProcessing }}>{children}</ModalContext.Provider>
}

export const useModalContext = () => {
  const context = useContext(ModalContext)
  if (!context) {
    throw new Error('useModalContext must be used within ModalProvider')
  }
  return context
}
