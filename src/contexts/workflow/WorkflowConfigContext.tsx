import { createContext, useContext, useState, ReactNode, useMemo } from 'react'

export interface WorkflowConfigContext {
  workflowName: string
  wfInstanceId: number
  refInstanceId: number | null
  copyInstanceId: number | null
  schemas: any
  definitionId: number
  historyId: number
  readOnly: boolean
  setWorkflowName: (_name: string) => void
  setDefinitionId: (_id: number) => void
  setHistoryId: (_id: number) => void
  setReadOnly: (_readOnly: boolean) => void
}

const WorkflowConfigContextInstance = createContext<WorkflowConfigContext | undefined>(undefined)

export const useWorkflowConfig = () => {
  const context = useContext(WorkflowConfigContextInstance)
  if (!context) throw new Error('useWorkflowConfig must be used within a WorkflowConfigProvider')
  return context
}

interface WorkflowConfigProviderProps {
  children: ReactNode
  initialWorkflowName?: string
  initialWfInstanceId?: number
  initialRefInstanceId?: number | null
  initialCopyInstanceId?: number | null
  initialSchemas?: any
}

export const WorkflowConfigProvider: React.FC<WorkflowConfigProviderProps> = ({
  children,
  initialWorkflowName = '',
  initialWfInstanceId = 0,
  initialRefInstanceId = null,
  initialCopyInstanceId = null,
  initialSchemas = {},
}) => {
  const [workflowName, setWorkflowName] = useState(initialWorkflowName)
  const [wfInstanceId] = useState(initialWfInstanceId)
  const [refInstanceId] = useState(initialRefInstanceId)
  const [copyInstanceId] = useState(initialCopyInstanceId)
  const [schemas] = useState(initialSchemas)
  const [definitionId, setDefinitionId] = useState(0)
  const [historyId, setHistoryId] = useState(0)
  const [readOnly, setReadOnly] = useState(false)

  const value = useMemo(
    () => ({
      workflowName,
      wfInstanceId,
      refInstanceId,
      copyInstanceId,
      schemas,
      definitionId,
      historyId,
      readOnly,
      setWorkflowName,
      setDefinitionId,
      setHistoryId,
      setReadOnly,
    }),
    [workflowName, wfInstanceId, refInstanceId, copyInstanceId, schemas, definitionId, historyId, readOnly],
  )

  return <WorkflowConfigContextInstance.Provider value={value}>{children}</WorkflowConfigContextInstance.Provider>
}

// Export context for other providers to use
export { WorkflowConfigContextInstance as WorkflowConfigContext }
