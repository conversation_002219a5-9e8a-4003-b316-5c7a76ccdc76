import { createContext, useContext, useState, ReactNode, useCallback, useMemo } from 'react'

export interface WorkflowUIContext {
  loading: boolean
  submitting: boolean
  error: string | null
  successMessage: string | null
  comments: Record<string, string>
  authorization: any
  setLoading: (_loading: boolean) => void
  setSubmitting: (submitting: boolean) => void
  setError: (error: string | null) => void
  setSuccessMessage: (message: string | null) => void
  setComments: React.Dispatch<React.SetStateAction<Record<string, string>>>
  parseWorkflowAuthorization: (authData: any) => void
  isSendBackVisible: boolean
  setIsSendBackVisible: React.Dispatch<React.SetStateAction<boolean>>
  tabVisibility: Record<string, boolean>
  setTabVisibility: React.Dispatch<React.SetStateAction<Record<string, boolean>>>
}

const WorkflowUIContext = createContext<WorkflowUIContext | undefined>(undefined)

export const useWorkflowUI = () => {
  const context = useContext(WorkflowUIContext)
  if (!context) throw new Error('useWorkflowUI must be used within a WorkflowUIProvider')
  return context
}

export const WorkflowUIProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [comments, setComments] = useState<Record<string, string>>({})
  const [authorization, setAuthorization] = useState({})
  const [isSendBackVisible, setIsSendBackVisible] = useState<boolean>(false)
  const [tabVisibility, setTabVisibility] = useState<Record<string, boolean>>({})

  const parseWorkflowAuthorization = useCallback((authData: any) => {
    setAuthorization(authData)
    if (authData.TabVisibility) {
      setTabVisibility(authData.TabVisibility)
    }
  }, [])

  const value = useMemo(
    () => ({
      loading,
      submitting,
      error,
      successMessage,
      comments,
      authorization,
      setLoading,
      setSubmitting,
      setError,
      setSuccessMessage,
      setComments,
      parseWorkflowAuthorization,
      isSendBackVisible,
      setIsSendBackVisible,
      tabVisibility,
      setTabVisibility,
    }),
    [loading, submitting, error, successMessage, comments, authorization, parseWorkflowAuthorization, isSendBackVisible, tabVisibility],
  )

  return <WorkflowUIContext.Provider value={value}>{children}</WorkflowUIContext.Provider>
}

export { WorkflowUIContext }
