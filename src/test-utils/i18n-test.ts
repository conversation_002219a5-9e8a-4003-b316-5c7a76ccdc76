import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// Test translations
const resources: any = {
  en: {
    translation: {
      common: {
        save: 'Save',
        cancel: 'Cancel',
        delete: 'Delete',
        edit: 'Edit',
        close: 'Close',
        submit: 'Submit',
        approve: 'Approve',
        reject: 'Reject',
        search: 'Search',
        filter: 'Filter',
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        noData: 'No data available',
      },
      workflow: {
        title: 'Workflow',
        status: 'Status',
        pending: 'Pending',
        approved: 'Approved',
        rejected: 'Rejected',
        draft: 'Draft',
        submitted: 'Submitted',
      },
      auth: {
        login: 'Login',
        logout: 'Logout',
        username: 'Userna<PERSON>',
        password: 'Password',
        invalidCredentials: 'Invalid credentials',
      },
      errors: {
        networkError: 'Network error occurred',
        serverError: 'Server error occurred',
        validationError: 'Validation error',
        unauthorized: 'Unauthorized access',
        notFound: 'Resource not found',
      },
    },
  },
  tr: {
    translation: {
      common: {
        save: 'Kay<PERSON>',
        cancel: '<PERSON>pta<PERSON>',
        delete: 'Sil',
        edit: '<PERSON><PERSON><PERSON><PERSON>',
        close: '<PERSON>pa<PERSON>',
        submit: 'Gönder',
        approve: 'Onayla',
        reject: 'Reddet',
        search: 'Ara',
        filter: 'Filtrele',
        loading: 'Yükleniyor...',
        error: 'Hata',
        success: 'Başarılı',
        noData: 'Veri bulunamadı',
      },
      workflow: {
        title: 'İş Akışı',
        status: 'Durum',
        pending: 'Beklemede',
        approved: 'Onaylandı',
        rejected: 'Reddedildi',
        draft: 'Taslak',
        submitted: 'Gönderildi',
      },
      auth: {
        login: 'Giriş',
        logout: 'Çıkış',
        username: 'Kullanıcı Adı',
        password: 'Şifre',
        invalidCredentials: 'Geçersiz kimlik bilgileri',
      },
      errors: {
        networkError: 'Ağ hatası oluştu',
        serverError: 'Sunucu hatası oluştu',
        validationError: 'Doğrulama hatası',
        unauthorized: 'Yetkisiz erişim',
        notFound: 'Kaynak bulunamadı',
      },
    },
  },
}

void i18n.use(initReactI18next).init({
  resources: resources,
  lng: 'en',
  fallbackLng: 'en',
  debug: false,
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
})

export default i18n
