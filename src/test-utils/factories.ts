import { faker } from '@faker-js/faker'

// User factory
export const createUser = (overrides?: Partial<any>) => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  name: faker.person.fullName(),
  email: faker.internet.email(),
  isAdmin: faker.datatype.boolean(),
  department: faker.helpers.arrayElement(['IT', 'HR', 'Finance', 'Sales']),
  role: faker.helpers.arrayElement(['Admin', 'Manager', 'User', 'Analyst']),
  createdAt: faker.date.past().toISOString(),
  ...overrides,
})

// Workflow factory
export const createWorkflow = (overrides?: Partial<any>) => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  title: faker.lorem.sentence(),
  type: faker.helpers.arrayElement(['leave', 'expense', 'procurement', 'contract']),
  status: faker.helpers.arrayElement(['draft', 'pending', 'approved', 'rejected', 'submitted']),
  createdBy: createUser(),
  createdAt: faker.date.recent().toISOString(),
  updatedAt: faker.date.recent().toISOString(),
  data: {},
  ...overrides,
})

// Leave request factory
export const createLeaveRequest = (overrides?: Partial<any>) => ({
  ...createWorkflow({ type: 'leave' }),
  data: {
    startDate: faker.date.future().toISOString().split('T')[0],
    endDate: faker.date.future().toISOString().split('T')[0],
    reason: faker.lorem.paragraph(),
    type: faker.helpers.arrayElement(['annual', 'sick', 'personal', 'maternity']),
    days: faker.number.int({ min: 1, max: 30 }),
    ...overrides?.data,
  },
  ...overrides,
})

// Expense report factory
export const createExpenseReport = (overrides?: Partial<any>) => ({
  ...createWorkflow({ type: 'expense' }),
  data: {
    amount: faker.number.float({ min: 100, max: 10000, fractionDigits: 2 }),
    currency: faker.helpers.arrayElement(['USD', 'EUR', 'GBP', 'TRY']),
    description: faker.lorem.sentence(),
    items: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => ({
      description: faker.commerce.productName(),
      amount: faker.number.float({ min: 10, max: 1000, fractionDigits: 2 }),
      category: faker.helpers.arrayElement(['Travel', 'Meals', 'Accommodation', 'Other']),
      date: faker.date.recent().toISOString().split('T')[0],
    })),
    ...overrides?.data,
  },
  ...overrides,
})

// Inbox item factory
export const createInboxItem = (overrides?: Partial<any>) => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  workflowId: faker.number.int({ min: 1, max: 1000 }),
  title: faker.lorem.sentence(),
  type: faker.helpers.arrayElement(['approval', 'review', 'notification']),
  status: faker.helpers.arrayElement(['read', 'unread']),
  priority: faker.helpers.arrayElement(['low', 'medium', 'high', 'urgent']),
  assignedTo: createUser(),
  createdAt: faker.date.recent().toISOString(),
  dueDate: faker.date.future().toISOString(),
  description: faker.lorem.paragraph(),
  ...overrides,
})

// History entry factory
export const createHistoryEntry = (overrides?: Partial<any>) => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  workflowId: faker.number.int({ min: 1, max: 1000 }),
  action: faker.helpers.arrayElement(['submitted', 'approved', 'rejected', 'commented', 'updated']),
  performedBy: createUser(),
  performedAt: faker.date.recent().toISOString(),
  comment: faker.lorem.sentence(),
  changes: {},
  ...overrides,
})

// Delegation factory
export const createDelegation = (overrides?: Partial<any>) => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  fromUser: createUser(),
  toUser: createUser(),
  startDate: faker.date.future().toISOString().split('T')[0],
  endDate: faker.date.future().toISOString().split('T')[0],
  reason: faker.lorem.sentence(),
  active: faker.datatype.boolean(),
  workflows: faker.helpers.arrayElements(['leave', 'expense', 'procurement', 'all']),
  createdAt: faker.date.recent().toISOString(),
  ...overrides,
})

// Organization node factory
export const createOrganizationNode = (depth = 0, maxDepth = 3): any => ({
  id: faker.string.uuid(),
  name: faker.company.name(),
  type: faker.helpers.arrayElement(['company', 'department', 'team', 'unit']),
  manager: depth > 0 ? createUser() : null,
  members: Array.from({ length: faker.number.int({ min: 0, max: 5 }) }, () => createUser()),
  children: depth < maxDepth ? Array.from({ length: faker.number.int({ min: 0, max: 3 }) }, () => createOrganizationNode(depth + 1, maxDepth)) : [],
})

// File upload factory
export const createFileUpload = (overrides?: Partial<any>) => ({
  id: faker.string.uuid(),
  filename: `${faker.system.fileName()}.${faker.system.fileExt()}`,
  size: faker.number.int({ min: 1024, max: 10485760 }), // 1KB to 10MB
  mimeType: faker.system.mimeType(),
  uploadedAt: faker.date.recent().toISOString(),
  uploadedBy: createUser(),
  url: faker.internet.url(),
  ...overrides,
})

// Notification factory
export const createNotification = (overrides?: Partial<any>) => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  type: faker.helpers.arrayElement(['workflow', 'system', 'info', 'warning', 'error']),
  title: faker.lorem.sentence(),
  message: faker.lorem.paragraph(),
  read: faker.datatype.boolean(),
  createdAt: faker.date.recent().toISOString(),
  link: faker.helpers.maybe(() => `/${faker.word.noun()}/${faker.number.int()}`),
  ...overrides,
})

// Survey factory
export const createSurvey = (overrides?: Partial<any>) => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  title: faker.lorem.sentence(),
  description: faker.lorem.paragraph(),
  questions: Array.from({ length: faker.number.int({ min: 3, max: 10 }) }, () => ({
    id: faker.number.int({ min: 1, max: 1000 }),
    type: faker.helpers.arrayElement(['text', 'select', 'multiselect', 'radio', 'checkbox', 'date', 'number']),
    question: faker.lorem.sentence(),
    required: faker.datatype.boolean(),
    options: faker.helpers.maybe(() => Array.from({ length: faker.number.int({ min: 2, max: 5 }) }, () => faker.word.noun())),
  })),
  createdAt: faker.date.past().toISOString(),
  expiresAt: faker.date.future().toISOString(),
  ...overrides,
})

// Form data factory
export const createFormData = (fields: string[]) => {
  const data: Record<string, any> = {}

  fields.forEach((field) => {
    if (field.includes('email')) {
      data[field] = faker.internet.email()
    } else if (field.includes('name')) {
      data[field] = faker.person.fullName()
    } else if (field.includes('date')) {
      data[field] = faker.date.future().toISOString().split('T')[0]
    } else if (field.includes('amount') || field.includes('price')) {
      data[field] = faker.number.float({ min: 10, max: 1000, fractionDigits: 2 })
    } else if (field.includes('phone')) {
      data[field] = faker.phone.number()
    } else if (field.includes('address')) {
      data[field] = faker.location.streetAddress()
    } else if (field.includes('description') || field.includes('comment')) {
      data[field] = faker.lorem.paragraph()
    } else {
      data[field] = faker.lorem.word()
    }
  })

  return data
}

// Batch factories for creating multiple items
export const createUsers = (count = 5) => Array.from({ length: count }, () => createUser())

export const createWorkflows = (count = 10) => Array.from({ length: count }, () => createWorkflow())

export const createInboxItems = (count = 5) => Array.from({ length: count }, () => createInboxItem())

export const createHistoryEntries = (count = 10) => Array.from({ length: count }, () => createHistoryEntry())

// Factory builder for custom factories
export const buildFactory = <T>(generator: () => T) => ({
  create: (overrides?: Partial<T>): T => ({
    ...generator(),
    ...overrides,
  }),
  createMany: (count: number, overrides?: Partial<T>): T[] =>
    Array.from({ length: count }, () => ({
      ...generator(),
      ...overrides,
    })),
})
