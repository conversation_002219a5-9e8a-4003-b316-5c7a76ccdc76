/**
 * Performance monitoring utilities
 */

/**
 * Performance timer utility
 */
export class PerformanceTimer {
  private startTime: number
  private endTime?: number
  private label: string

  constructor(label: string) {
    this.label = label
    this.startTime = performance.now()
  }

  /**
   * Stop the timer and log the result
   */
  stop(): number | undefined {
    this.endTime = performance.now()
    const duration = this.endTime - this.startTime
    if (import.meta.env.DEV) {
      if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
        console.warn(`⏱️ ${this.label}: ${duration.toFixed(2)}ms`)
      }
    }
    return duration
  }
}

/**
 * Measure the performance of a function
 */
export const measurePerformance = <T>(label: string, fn: () => T): T => {
  const timer = new PerformanceTimer(label)
  const result = fn()
  void timer.stop()
  return result
}

/**
 * Measure the performance of an async function
 */
export const measureAsyncPerformance = async <T>(label: string, fn: () => Promise<T>): Promise<T> => {
  const timer = new PerformanceTimer(label)
  const result = await fn()
  void timer.stop()
  return result
}

/**
 * Log component render performance (React DevTools integration)
 */
export const logRenderPerformance = (componentName: string, renderTime: number) => {
  if (import.meta.env.DEV && renderTime > 16) {
    // 16ms is 60fps threshold
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      console.warn(`🐌 Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`)
    }
  }
} // End of logRenderPerformance

/**
 * Memory usage monitoring
 */
export const getMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return {
      used: Math.round(memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(memory.totalJSHeapSize / 1048576), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
    }
  }
  return null
}

/**
 * Log memory usage
 */
export const logMemoryUsage = (label?: string) => {
  const memory = getMemoryUsage()
  if (memory && import.meta.env.DEV) {
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      console.warn(`🧠 ${label ? `${label} - ` : ''}Memory: ${memory.used}MB / ${memory.total}MB (limit: ${memory.limit}MB)`)
    }
  }
} // End of logMemoryUsage

/**
 * FPS monitoring
 */
export class FPSMonitor {
  private frames = 0
  private startTime = performance.now()
  private isRunning = false

  start() {
    if (this.isRunning) return

    this.isRunning = true
    this.frames = 0
    this.startTime = performance.now()
    void this.measure()
  }

  stop() {
    this.isRunning = false
  }

  private measure() {
    if (!this.isRunning) return

    this.frames++
    const currentTime = performance.now()
    const elapsed = currentTime - this.startTime

    if (elapsed >= 1000) {
      // Every second
      const fps = Math.round((this.frames * 1000) / elapsed)

      if (import.meta.env.DEV) {
        if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
          void console.warn(`🎯 FPS: ${fps}`)
        }
      }

      this.frames = 0
      this.startTime = currentTime
    }

    requestAnimationFrame(() => this.measure())
  }
} // End of FPSMonitor

/**
 * Log bundle information
 */
export const logBundleInfo = () => {
  if (import.meta.env.DEV) {
    void console.group('📦 Bundle Information')
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      void console.warn('Environment:', import.meta.env.MODE)
    }
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      void console.warn('Base URL:', import.meta.env.BASE_URL)
    }
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      void console.warn('Development:', import.meta.env.DEV)
    }
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      void console.warn('Production:', import.meta.env.PROD)
    }
    void console.groupEnd()
  }
} // End of logBundleInfo
