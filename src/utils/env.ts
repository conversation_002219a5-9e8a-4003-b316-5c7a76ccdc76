/**
 * Environment utilities and validation
 */

/**
 * Check if we're running in development mode
 */
export const isDevelopment = (): boolean => {
  return import.meta.env.MODE === 'development' || import.meta.env.DEV
}

/**
 * Check if we're running in production mode
 */
export const isProduction = (): boolean => {
  return import.meta.env.MODE === 'production' || import.meta.env.PROD
}

/**
 * Check if we're running in test mode
 */
export const isTest = (): boolean => {
  return import.meta.env.MODE === 'test' || process.env.NODE_ENV === 'test'
}

/**
 * Get the current environment name
 */
export const getEnvironment = (): string => {
  return import.meta.env.MODE ?? 'production'
}

/**
 * Check if a feature flag is enabled via environment variable
 */
export const isFeatureEnabled = (featureName: string): boolean => {
  const envVar = `VITE_ENABLE_${featureName.toUpperCase()}`
  const value = import.meta.env[envVar]

  if (typeof value === 'string') {
    return value.toLowerCase() === 'true' || value === '1'
  }

  return Boolean(value)
}

/**
 * Get environment variable with type safety
 */
export const getEnvVar = (key: string, defaultValue?: string): string | null => {
  const value = import.meta.env[key]
  return value !== undefined ? value : (defaultValue ?? null)
}

/**
 * Get required environment variable (throws if not found)
 */
export const getRequiredEnvVar = (key: string): string => {
  const value = import.meta.env[key]
  if (value === undefined || value === '') {
    throw new Error(`Required environment variable ${key} is not set`)
  }
  return value
}

/**
 * Runtime environment info
 */
export const envInfo = {
  mode: getEnvironment(),
  isDev: isDevelopment(),
  isProd: isProduction(),
  isTest: isTest(),
  apiUrl: getEnvVar('VITE_API_URL'),
  version: getEnvVar('VITE_APP_VERSION', 'unknown'),
} as const
