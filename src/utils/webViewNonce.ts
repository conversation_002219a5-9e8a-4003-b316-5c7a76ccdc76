/**
 * WebView Nonce Validation System
 *
 * Provides cryptographically secure nonce generation and validation
 * for WebView message authentication to prevent replay attacks.
 */

import { generateSecureId } from './crypto'

// Store for active nonces with expiration
const nonceStore = new Map<
  string,
  {
    timestamp: number
    used: boolean
    origin?: string
  }
>()

// Nonce configuration
const NONCE_LENGTH = 32 // 256 bits of entropy
const NONCE_EXPIRY_MS = 5 * 60 * 1000 // 5 minutes
const MAX_NONCES = 100 // Maximum stored nonces to prevent memory issues

/**
 * Generate a secure nonce for WebView communication
 */
export function generateNonce(): string {
  // Clean up expired nonces
  cleanupExpiredNonces()

  // Generate new nonce
  const nonce = generateSecureId(NONCE_LENGTH)

  // Store with timestamp
  nonceStore.set(nonce, {
    timestamp: Date.now(),
    used: false,
  })

  // Limit store size
  if (nonceStore.size > MAX_NONCES) {
    const oldestNonces = Array.from(nonceStore.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp)
      .slice(0, nonceStore.size - MAX_NONCES)

    oldestNonces.forEach(([nonce]) => nonceStore.delete(nonce))
  }

  return nonce
}

/**
 * Validate a nonce from WebView
 */
export function validateNonce(nonce: string, origin?: string): boolean {
  if (!nonce || typeof nonce !== 'string') {
    return false
  }

  const nonceData = nonceStore.get(nonce)

  if (!nonceData) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Nonce not found:', nonce.substring(0, 8) + '...')
    }
    return false
  }

  // Check if already used
  if (nonceData.used) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Nonce already used:', nonce.substring(0, 8) + '...')
    }
    return false
  }

  // Check expiration
  const age = Date.now() - nonceData.timestamp
  if (age > NONCE_EXPIRY_MS) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Nonce expired:', nonce.substring(0, 8) + '...', `Age: ${age}ms`)
    }
    nonceStore.delete(nonce)
    return false
  }

  // Mark as used
  nonceData.used = true
  nonceData.origin = origin

  return true
}

/**
 * Clean up expired nonces
 */
function cleanupExpiredNonces(): void {
  const now = Date.now()
  const expiredNonces: string[] = []

  nonceStore.forEach((data, nonce) => {
    if (now - data.timestamp > NONCE_EXPIRY_MS) {
      expiredNonces.push(nonce)
    }
  })

  expiredNonces.forEach((nonce) => nonceStore.delete(nonce))
}

/**
 * Create a signed message with nonce
 */
export interface SignedMessage {
  type: string
  data?: unknown
  nonce: string
  timestamp: number
}

export function createSignedMessage(type: string, data?: unknown): SignedMessage {
  return {
    type,
    data,
    nonce: generateNonce(),
    timestamp: Date.now(),
  }
}

/**
 * Validate a signed message
 */
export function validateSignedMessage(message: unknown, expectedType?: string, origin?: string): { valid: boolean; error?: string } {
  // Basic structure validation
  if (!message || typeof message !== 'object') {
    return { valid: false, error: 'Invalid message structure' }
  }

  if (!message.type || typeof message.type !== 'string') {
    return { valid: false, error: 'Missing or invalid message type' }
  }

  if (!message.nonce || typeof message.nonce !== 'string') {
    return { valid: false, error: 'Missing or invalid nonce' }
  }

  if (!message.timestamp || typeof message.timestamp !== 'number') {
    return { valid: false, error: 'Missing or invalid timestamp' }
  }

  // Type validation
  if (expectedType && message.type !== expectedType) {
    return { valid: false, error: `Unexpected message type: ${message.type}` }
  }

  // Timestamp validation (prevent far future timestamps)
  const timeDiff = Math.abs(Date.now() - message.timestamp)
  if (timeDiff > NONCE_EXPIRY_MS) {
    return { valid: false, error: 'Message timestamp out of valid range' }
  }

  // Nonce validation
  if (!validateNonce(message.nonce, origin)) {
    return { valid: false, error: 'Invalid or expired nonce' }
  }

  return { valid: true }
}

/**
 * Clear all nonces (for cleanup/logout)
 */
export function clearNonces(): void {
  nonceStore.clear()
}

/**
 * Get nonce statistics (for debugging)
 */
export function getNonceStats(): {
  total: number
  used: number
  expired: number
  active: number
} {
  const now = Date.now()
  let used = 0
  let expired = 0
  let active = 0

  nonceStore.forEach((_data) => {
    if (_data.used) {
      used++
    } else if (now - _data.timestamp > NONCE_EXPIRY_MS) {
      expired++
    } else {
      active++
    }
  })

  return {
    total: nonceStore.size,
    used,
    expired,
    active,
  }
}

// Auto-cleanup interval
let cleanupInterval: NodeJS.Timeout | null = null

/**
 * Start automatic nonce cleanup
 */
export function startNonceCleanup(): void {
  if (cleanupInterval) return

  // Run cleanup every minute
  cleanupInterval = setInterval(() => {
    cleanupExpiredNonces()
  }, 60 * 1000)
}

/**
 * Stop automatic nonce cleanup
 */
export function stopNonceCleanup(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval)
    cleanupInterval = null
  }
}

// Start cleanup on module load
if (typeof window !== 'undefined') {
  startNonceCleanup()

  // Stop cleanup on page unload
  window.addEventListener('beforeunload', () => {
    stopNonceCleanup()
    clearNonces()
  })
}

export default {
  generateNonce,
  validateNonce,
  createSignedMessage,
  validateSignedMessage,
  clearNonces,
  getNonceStats,
  startNonceCleanup,
  stopNonceCleanup,
}
