// This helper function is particularly useful when you need to wait for multiple promises to settle (either fulfill or reject) and then process their results.
// It's akin to Promise.allSettled but includes additional typing for better TypeScript support.

type PromiseSettledResult<T> = { status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any }

export default function promiseAllSettledDefault<T>(promises: Promise<T>[]): Promise<PromiseSettledResult<T>[]> {
  return Promise.all(
    promises.map((p) =>
      p
        .then((_value) => ({ status: 'fulfilled', value: _value }) as PromiseSettledResult<T>)
        .catch((_reason) => ({ status: 'rejected', reason: _reason }) as PromiseSettledResult<T>),
    ),
  )
}

////Usage example

// const promise1 = Promise.resolve(3);
// const promise2 = new Promise((resolve, reject) => setTimeout(reject, 100, 'foo'));
// const promises = [promise1, promise2];

// promiseAllSettled(promises).then((results) => results.forEach((_result) =>
//   console.warn(result.status === 'fulfilled' ? result.value : result.reason)));
