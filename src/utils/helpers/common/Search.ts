export default function SearchDefault(list: unknown[], searchKey: string): unknown[] {
  let sList: unknown[] = []
  searchKey
    ? list.forEach((l: any) => {
        Object.keys(l).forEach((k) => {
          if (
            l[k] &&
            l[k].toString().toLocaleUpperCase().indexOf(searchKey.toString().toLocaleUpperCase()) > -1 &&
            sList.findIndex((sl) => sl === l) === -1
          )
            void sList.push(l)
        })
      })
    : (sList = list)
  return sList
} // End of Search
