export default function getNestedValueDefault<T, Default>(object: T, keys: (keyof T)[], defaultValue: Default): any {
  if (!object || typeof object !== 'object') {
    return defaultValue
  }

  let result: any = object
  for (const key of keys) {
    if (!result || typeof result !== 'object') {
      return defaultValue
    }
    result = result[key]
    if (result === undefined || result === null) {
      return defaultValue
    }
  }
  return result
} // End of getNestedValue
