// This function waits for a given condition to return true.
// It is useful for situations where you need to pause execution until a certain condition is met, without blocking the main thread.

export default async function waitForDefault(predicate: () => boolean, interval: number = 50, timeout: number = 5000): Promise<void> {
  const startTime = Date.now()

  while (!predicate() && Date.now() - startTime < timeout) {
    await new Promise((resolve) => setTimeout(resolve, interval))
  }
} // End of waitForDefault

// let conditionMet = false;

// setTimeout(() => { conditionMet = true; }, 3000); // Simulate an asynchronous operation

// async function exampleUsage() {
//   await waitFor(() => conditionMet);
//   console.warn('Condition met, continuing execution...');
// }

// exampleUsage();
