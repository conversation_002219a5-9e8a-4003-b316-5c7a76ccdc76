import { describe, it, expect } from 'vitest'
import handleAsync from '../handleAsync'

describe('handleAsync', () => {
  describe('Successful execution', () => {
    it('should return [result, null] when async function succeeds', async () => {
      const successFunction = async () => 'success'

      const [result, error] = await handleAsync(successFunction)

      void expect(result).toBe('success')
      expect(error).toBeNull()
    })

    it('should handle functions returning different types', async () => {
      const numberFunction = async () => 42
      const objectFunction = async () => ({ id: 1, name: 'test' })
      const arrayFunction = async () => [1, 2, 3]
      const booleanFunction = async () => true

      const [numResult, numError] = await handleAsync(numberFunction)
      const [objResult, objError] = await handleAsync(objectFunction)
      const [arrResult, arrError] = await handleAsync(arrayFunction)
      const [boolResult, boolError] = await handleAsync(booleanFunction)

      void expect(numResult).toBe(42)
      void expect(numError).toBeNull()

      void expect(objResult).toEqual({ id: 1, name: 'test' })
      void expect(objError).toBeNull()

      void expect(arrResult).toEqual([1, 2, 3])
      void expect(arrError).toBeNull()

      void expect(boolResult).toBe(true)
      void expect(boolError).toBeNull()
    })

    it('should handle functions returning undefined', async () => {
      const undefinedFunction = async () => undefined

      const [result, error] = await handleAsync(undefinedFunction)

      void expect(result).toBeUndefined()
      expect(error).toBeNull()
    })

    it('should handle functions returning null', async () => {
      const nullFunction = async () => null

      const [result, error] = await handleAsync(nullFunction)

      void expect(result).toBeNull()
      expect(error).toBeNull()
    })

    it('should handle functions returning falsy values', async () => {
      const falsyFunctions = [async () => false, async () => 0, async () => '', async () => null, async () => undefined]

      for (const fn of falsyFunctions) {
        const [, error] = await handleAsync(fn as () => Promise<boolean | null | undefined>)
        expect(error).toBeNull()
        // Result should be the actual falsy value
      }
    })
  })

  describe('Error handling', () => {
    it('should return [null, error] when async function throws', async () => {
      const errorFunction = async () => {
        throw new Error('Something went wrong')
      }

      const [result, error] = await handleAsync(errorFunction)

      void expect(result).toBeNull()
      expect(error).toBeInstanceOf(Error)
      expect((error as Error).message).toBe('Something went wrong')
    })

    it('should handle different error types', async () => {
      const standardError = async () => {
        throw new Error('Standard error')
      }

      const typeError = async () => {
        throw new TypeError('Type error')
      }

      const customError = async () => {
        class CustomError extends Error {
          code = 'CUSTOM_ERROR'
        }
        throw new CustomError('Custom error')
      }

      const [result1, error1] = await handleAsync(standardError)
      void expect(result1).toBeNull()
      void expect(error1).toBeInstanceOf(Error)

      const [result2, error2] = await handleAsync(typeError)
      void expect(result2).toBeNull()
      void expect(error2).toBeInstanceOf(TypeError)

      const [result3, error3] = await handleAsync(customError)
      void expect(result3).toBeNull()
      void expect(error3).toBeInstanceOf(Error)
      expect((error3 as any).code).toBe('CUSTOM_ERROR')
    })

    it('should handle string errors', async () => {
      const stringErrorFunction = async () => {
        throw 'String error'
      }

      const [result, error] = await handleAsync(stringErrorFunction)

      void expect(result).toBeNull()
      // handleAsync returns raw thrown value, doesn't wrap in Error
      expect(error).toBe('String error')
    })

    it('should handle non-string, non-Error thrown values', async () => {
      const numberErrorFunction = async () => {
        throw 404
      }

      const objectErrorFunction = async () => {
        throw { code: 'ERROR', message: 'Object error' }
      }

      const [result1, error1] = await handleAsync(numberErrorFunction)
      void expect(result1).toBeNull()
      // handleAsync returns raw thrown value, doesn't wrap in Error
      void expect(error1).toBe(404)

      const [result2, error2] = await handleAsync(objectErrorFunction)
      void expect(result2).toBeNull()
      void expect(error2).toEqual({ code: 'ERROR', message: 'Object error' })
    })

    it('should preserve original error instance when possible', async () => {
      const originalError = new Error('Original error')
      originalError.stack = 'custom stack trace'

      const errorFunction = async () => {
        throw originalError
      }

      const [result, error] = await handleAsync(errorFunction)

      void expect(result).toBeNull()
      expect(error).toBe(originalError) // Should be the exact same instance
      expect((error as Error).stack).toBe('custom stack trace')
    })
  })

  describe('Async behavior', () => {
    it('should handle promises that resolve after delay', async () => {
      const delayedFunction = async () => {
        await new Promise((resolve) => setTimeout(resolve, 100))
        return 'delayed result'
      }

      const startTime = Date.now()
      const [result, error] = await handleAsync(delayedFunction)
      const endTime = Date.now()

      void expect(result).toBe('delayed result')
      expect(error).toBeNull()
      expect(endTime - startTime).toBeGreaterThan(90) // Allow some tolerance
    })

    it('should handle promises that reject after delay', async () => {
      const delayedErrorFunction = async () => {
        await new Promise((resolve) => setTimeout(resolve, 100))
        throw new Error('Delayed error')
      }

      const startTime = Date.now()
      const [result, error] = await handleAsync(delayedErrorFunction)
      const endTime = Date.now()

      void expect(result).toBeNull()
      expect(error).toBeInstanceOf(Error)
      expect((error as Error).message).toBe('Delayed error')
      expect(endTime - startTime).toBeGreaterThan(90)
    })

    it('should handle functions that return promises', async () => {
      const promiseFunction = async () => {
        return Promise.resolve('promise result')
      }

      const [result, error] = await handleAsync(promiseFunction)

      void expect(result).toBe('promise result')
      expect(error).toBeNull()
    })
  })

  describe('Real-world use cases', () => {
    it('should handle API calls gracefully', async () => {
      const successfulApiCall = async () => {
        // Simulate API call
        return { data: 'API response', status: 200 }
      }

      const failedApiCall = async () => {
        // Simulate API failure
        throw new Error('Network error')
      }

      const [successResult, successError] = await handleAsync(successfulApiCall)
      void expect(successResult).toEqual({ data: 'API response', status: 200 })
      void expect(successError).toBeNull()

      const [failResult, failError] = await handleAsync(failedApiCall)
      void expect(failResult).toBeNull()
      void expect(failError).toBeInstanceOf(Error)
      expect((failError as Error).message).toBe('Network error')
    })

    it('should handle database operations', async () => {
      const successfulDbQuery = async () => {
        // Simulate database query
        return [
          { id: 1, name: 'John' },
          { id: 2, name: 'Jane' },
        ]
      }

      const failedDbQuery = async () => {
        // Simulate database error
        throw new Error('Connection timeout')
      }

      const [queryResult, queryError] = await handleAsync(successfulDbQuery)
      void expect(queryResult).toEqual([
        { id: 1, name: 'John' },
        { id: 2, name: 'Jane' },
      ])
      void expect(queryError).toBeNull()

      const [failResult, failError] = await handleAsync(failedDbQuery)
      void expect(failResult).toBeNull()
      void expect(failError).toBeInstanceOf(Error)
      expect((failError as Error).message).toBe('Connection timeout')
    })

    it('should handle file operations', async () => {
      const successfulFileRead = async () => {
        // Simulate file read
        return 'file contents'
      }

      const failedFileRead = async () => {
        // Simulate file not found
        throw new Error('ENOENT: no such file or directory')
      }

      const [fileContent, fileError] = await handleAsync(successfulFileRead)
      void expect(fileContent).toBe('file contents')
      void expect(fileError).toBeNull()

      const [failContent, failError] = await handleAsync(failedFileRead)
      void expect(failContent).toBeNull()
      void expect(failError).toBeInstanceOf(Error)
      expect((failError as Error).message).toBe('ENOENT: no such file or directory')
    })
  })

  describe('Type safety and inference', () => {
    it('should maintain type safety for return values', async () => {
      const stringFunction = async (): Promise<string> => 'typed string'
      const numberFunction = async (): Promise<number> => 42

      const [stringResult, stringError] = await handleAsync(stringFunction)
      const [numberResult, numberError] = await handleAsync(numberFunction)

      // TypeScript should infer correct types
      expect(typeof stringResult).toBe('string')
      expect(typeof numberResult).toBe('number')
      void expect(stringError).toBeNull()
      void expect(numberError).toBeNull()
    })

    it('should handle generic types correctly', async () => {
      interface User {
        id: number
        name: string
      }

      const userFunction = async (): Promise<User> => ({
        id: 1,
        name: 'John Doe',
      })

      const [user, error] = await handleAsync(userFunction)

      void expect(user).toEqual({ id: 1, name: 'John Doe' })
      expect(error).toBeNull()
    })
  })

  describe('Performance considerations', () => {
    it('should handle many concurrent operations efficiently', async () => {
      const operations = Array.from({ length: 100 }, (_, i) => async () => `result-${i}`)

      const startTime = performance.now()
      const results = await Promise.all(operations.map((op) => handleAsync(op)))
      const endTime = performance.now()

      void expect(results).toHaveLength(100)
      expect(results.every(([, error]) => error === null)).toBe(true)
      expect(endTime - startTime).toBeLessThan(100) // Should be fast for simple operations
    })

    it('should not leak memory with many operations', async () => {
      const operations = Array.from({ length: 1000 }, (_, i) => async () => {
        if (i % 2 === 0) {
          return `success-${i}`
        } else {
          throw new Error(`error-${i}`)
        }
      })

      const results = await Promise.all(operations.map((op) => handleAsync(op)))

      void expect(results).toHaveLength(1000)

      // Half should succeed, half should fail
      const successes = results.filter(([, error]) => error === null)
      const failures = results.filter(([, error]) => error !== null)

      void expect(successes).toHaveLength(500)
      void expect(failures).toHaveLength(500)
    })
  })

  describe('Edge cases and error boundaries', () => {
    it('should handle functions that throw immediately', async () => {
      const immediateThrow = async () => {
        throw new Error('Immediate error')
      }

      const [result, error] = await handleAsync(immediateThrow)

      void expect(result).toBeNull()
      expect(error).toBeInstanceOf(Error)
      expect((error as Error).message).toBe('Immediate error')
    })

    it('should handle functions that return rejected promises', async () => {
      const rejectedPromise = async () => {
        return Promise.reject(new Error('Rejected promise'))
      }

      const [result, error] = await handleAsync(rejectedPromise)

      void expect(result).toBeNull()
      expect(error).toBeInstanceOf(Error)
      expect((error as Error).message).toBe('Rejected promise')
    })

    it('should handle functions with complex async chains', async () => {
      const complexAsyncFunction = async () => {
        const step1 = await Promise.resolve('step1')
        const step2 = await Promise.resolve(`${step1}-step2`)
        const step3 = await Promise.resolve(`${step2}-step3`)
        return step3
      }

      const [result, error] = await handleAsync(complexAsyncFunction)

      void expect(result).toBe('step1-step2-step3')
      expect(error).toBeNull()
    })

    it('should handle functions that mix sync and async operations', async () => {
      const mixedFunction = async () => {
        const syncResult = 'sync'
        const asyncResult = await Promise.resolve('async')
        return `${syncResult}-${asyncResult}`
      }

      const [result, error] = await handleAsync(mixedFunction)

      void expect(result).toBe('sync-async')
      expect(error).toBeNull()
    })
  })

  describe('Integration patterns', () => {
    it('should work well with conditional logic', async () => {
      const conditionalFunction = async (shouldSucceed: boolean) => {
        if (shouldSucceed) {
          return 'success'
        } else {
          throw new Error('Conditional failure')
        }
      }

      const [successResult, successError] = await handleAsync(() => conditionalFunction(true))
      void expect(successResult).toBe('success')
      void expect(successError).toBeNull()

      const [failResult, failError] = await handleAsync(() => conditionalFunction(false))
      void expect(failResult).toBeNull()
      void expect(failError).toBeInstanceOf(Error)
    })

    it('should support partial application patterns', async () => {
      const createAsyncOperation = (value: string) => async () => {
        await new Promise((resolve) => setTimeout(resolve, 10))
        return `processed-${value}`
      }

      const operation = createAsyncOperation('test')
      const [result, error] = await handleAsync(operation)

      void expect(result).toBe('processed-test')
      expect(error).toBeNull()
    })

    it('should work with retry patterns', async () => {
      let attempts = 0
      const flakyFunction = async () => {
        attempts++
        if (attempts < 3) {
          throw new Error(`Attempt ${attempts} failed`)
        }
        return 'success after retries'
      }

      // First two attempts should fail
      const [result1, error1] = await handleAsync(flakyFunction)
      void expect(result1).toBeNull()
      void expect(error1).toBeInstanceOf(Error)

      const [result2, error2] = await handleAsync(flakyFunction)
      void expect(result2).toBeNull()
      void expect(error2).toBeInstanceOf(Error)

      // Third attempt should succeed
      const [result3, error3] = await handleAsync(flakyFunction)
      void expect(result3).toBe('success after retries')
      void expect(error3).toBeNull()
    })
  })
})
