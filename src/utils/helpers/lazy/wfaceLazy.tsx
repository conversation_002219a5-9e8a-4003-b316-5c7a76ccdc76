import React, { ComponentType, useEffect, useState } from 'react'
import { WCircularProgress } from 'wface'

/**
 * Creates a WFace-compatible lazy loading wrapper
 * This wrapper handles the async loading within a synchronous component
 */
export function wfaceLazy<T extends ComponentType<Record<string, unknown>>>(
  importFunc: () => Promise<{ default: T }>,
): ComponentType<Record<string, unknown>> {
  let LoadedComponent: T | null = null
  let loadPromise: Promise<void> | null = null

  // Return a synchronous component that handles async loading internally
  return function WfaceLazyComponent(_props: Record<string, unknown>) {
    const [isLoaded, setIsLoaded] = useState(!!LoadedComponent)
    const [error, setError] = useState<Error | null>(null)

    useEffect(() => {
      if (!LoadedComponent && !loadPromise) {
        loadPromise = importFunc()
          .then((module) => {
            LoadedComponent = module.default
            setIsLoaded(true)
          })
          .catch((_err) => {
            setError(_err)
            if (process.env.NODE_ENV === 'development') {
              console.error('Failed to load component:', _err)
            }
          })
      } else if (LoadedComponent) {
        setIsLoaded(true)
      }
    }, [])

    if (error) {
      return (
        <div style={{ padding: 20, color: 'red' }}>
          <h3>Failed to load component</h3>
          <pre>{error.message}</pre>
        </div>
      )
    }

    if (!isLoaded) {
      return (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
          }}
        >
          <WCircularProgress />
        </div>
      )
    }

    // Use createElement to avoid ref warnings
    return React.createElement(LoadedComponent!, _props)
  }
} // End of wfaceLazy
