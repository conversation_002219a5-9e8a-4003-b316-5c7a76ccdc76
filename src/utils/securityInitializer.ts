/**
 * Security Initializer
 *
 * Initializes all security measures for the DigiFlow application
 * Defense in depth approach with comprehensive security hardening
 */

import securityHeadersService from '../services/securityHeadersService'
import csrfService from '../services/csrfService'
import securityAuditService from './securityAudit'
import { isInWebView } from './webViewDetection'

export interface SecurityInitializationConfig {
  enableCSPViolationReporting: boolean
  enableSecurityAudit: boolean
  enableDevelopmentWarnings: boolean
  enablePerformanceMonitoring: boolean
  auditInterval: number // minutes
}

export class SecurityInitializer {
  private static instance: SecurityInitializer
  private config: SecurityInitializationConfig
  private auditTimer?: NodeJS.Timeout

  private constructor() {
    this.config = this.getDefaultConfig()
  }

  static getInstance(): SecurityInitializer {
    if (!SecurityInitializer.instance) {
      SecurityInitializer.instance = new SecurityInitializer()
    }
    return SecurityInitializer.instance
  }

  /**
   * Get default security configuration
   */
  private getDefaultConfig(): SecurityInitializationConfig {
    return {
      enableCSPViolationReporting: process.env.NODE_ENV === 'production',
      enableSecurityAudit: true,
      enableDevelopmentWarnings: process.env.NODE_ENV === 'development',
      enablePerformanceMonitoring: process.env.NODE_ENV === 'production',
      auditInterval: process.env.NODE_ENV === 'production' ? 60 : 15, // minutes
    }
  }

  /**
   * Initialize comprehensive security measures
   */
  async initializeSecurity(config?: Partial<SecurityInitializationConfig>): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config }
    }

    if (process.env.NODE_ENV === 'development') {
      void console.warn('🛡️ Initializing comprehensive security measures...')
    }

    try {
      // Initialize security headers
      await this.initializeSecurityHeaders()

      // Initialize CSRF protection
      await this.initializeCSRFProtection()

      // Initialize WebView security
      if (isInWebView()) {
        await this.initializeWebViewSecurity()
      }

      // Initialize security monitoring
      await this.initializeSecurityMonitoring()

      // Run initial security audit
      if (this.config.enableSecurityAudit) {
        await this.runInitialSecurityAudit()
      }

      // Start periodic security audits
      void this.startPeriodicAudits()

      if (process.env.NODE_ENV === 'development') {
        void console.warn('✅ Security initialization completed successfully')
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Security initialization failed:', _error)
      }
      throw _error
    }
  }

  /**
   * Initialize security headers
   */
  private async initializeSecurityHeaders(): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      void console.warn('🔒 Initializing security headers...')
    }

    // Set up CSP violation reporting
    if (this.config.enableCSPViolationReporting) {
      // CSP violation reporting is handled by the security headers service
    }

    // Validate current security headers
    const validation = await securityHeadersService.validateSecurityHeaders()
    if (!validation.compliant) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('⚠️ Security headers validation failed:', validation.violations)
      }

      if (this.config.enableDevelopmentWarnings) {
        validation.recommendations.forEach((_rec) => {
          if (process.env.NODE_ENV === 'development') {
            void console.warn('💡 Recommendation:', _rec)
          }
        })
      }
    }

    if (process.env.NODE_ENV === 'development') {
      void console.warn('✅ Security headers initialized')
    }
  }

  /**
   * Initialize CSRF protection
   */
  private async initializeCSRFProtection(): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      void console.warn('🛡️ Initializing CSRF protection...')
    }

    try {
      // Pre-fetch CSRF token
      const token = await csrfService.fetchToken()
      if (token) {
        if (process.env.NODE_ENV === 'development') {
          void console.warn('✅ CSRF token fetched successfully')
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
          void console.warn('⚠️ CSRF token not available')
        }
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ CSRF initialization failed:', _error)
      }
      if (process.env.NODE_ENV === 'production') {
        throw _error
      }
    }

    if (process.env.NODE_ENV === 'development') {
      void console.warn('✅ CSRF protection initialized')
    }
  }

  /**
   * Initialize WebView security
   */
  private async initializeWebViewSecurity(): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      void console.warn('📱 Initializing WebView security...')
    }

    // Validate WebView security configuration
    const hasSecureSession = !!(window as any).SECURE_SESSION_ID
    const hasJWTToken = !!(window as any).JWT_TOKEN

    if (hasJWTToken) {
      if (process.env.NODE_ENV === 'development') {
        console.error('🚨 CRITICAL: JWT token found in window object - security vulnerability!')
      }
      if (process.env.NODE_ENV === 'production') {
        throw new Error('JWT token exposure detected in production')
      }
    }

    if (!hasSecureSession) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('⚠️ No secure session ID found for WebView')
      }
    }

    // Set up WebView message handlers
    if ((window as any).ReactNativeWebView) {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('✅ WebView message interface available')
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
        void console.warn('⚠️ WebView message interface not available')
      }
    }

    if (process.env.NODE_ENV === 'development') {
      void console.warn('✅ WebView security initialized')
    }
  }

  /**
   * Initialize security monitoring
   */
  private async initializeSecurityMonitoring(): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      void console.warn('📊 Initializing security monitoring...')
    }

    // Set up performance monitoring
    if (this.config.enablePerformanceMonitoring && 'performance' in window) {
      void this.setupPerformanceMonitoring()
    }

    // Set up error monitoring
    void this.setupErrorMonitoring()

    // Set up network monitoring
    void this.setupNetworkMonitoring()

    if (process.env.NODE_ENV === 'development') {
      void console.warn('✅ Security monitoring initialized')
    }
  }

  /**
   * Set up performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    if (!('performance' in window)) return

    // Monitor navigation timing
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navTiming) {
          const loadTime = navTiming.loadEventEnd - navTiming.fetchStart
          if (process.env.NODE_ENV === 'development') {
            void console.warn(`📈 Page load time: ${loadTime}ms`)
          }

          // Report slow load times
          if (loadTime > 5000) {
            if (process.env.NODE_ENV === 'development') {
              void console.warn('⚠️ Slow page load detected:', loadTime)
            }
          }
        }
      }, 0)
    })

    // Monitor resource timing
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((_entry) => {
        if (_entry.duration > 1000) {
          if (process.env.NODE_ENV === 'development') {
            void console.warn('⚠️ Slow resource detected:', _entry.name, _entry.duration)
          }
        }
      })
    })

    observer.observe({ entryTypes: ['resource'] })
  }

  /**
   * Set up error monitoring
   */
  private setupErrorMonitoring(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      if (process.env.NODE_ENV === 'development') {
        console.error('🚨 Global error:', {
          message: event.message,
          source: event.filename,
          line: event.lineno,
          column: event.colno,
          error: event.error,
        })
      }

      // Report security-related errors
      if (event.message.includes('CSP') || event.message.includes('security')) {
        if (process.env.NODE_ENV === 'development') {
          console.error('🔒 Security-related error detected')
        }
      }
    })

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      if (process.env.NODE_ENV === 'development') {
        console.error('🚨 Unhandled promise rejection:', event.reason)
      }

      // Report security-related rejections
      if (event.reason?.toString().includes('security')) {
        if (process.env.NODE_ENV === 'development') {
          console.error('🔒 Security-related promise rejection')
        }
      }
    })
  }

  /**
   * Set up network monitoring
   */
  private setupNetworkMonitoring(): void {
    // Monitor fetch requests
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const [resource, config] = args
      const url = typeof resource === 'string' ? resource : resource instanceof URL ? resource.href : resource.url

      // Log non-HTTPS requests
      if (url.startsWith('http://') && !url.includes('localhost')) {
        if (process.env.NODE_ENV === 'development') {
          void console.warn('⚠️  Non-HTTPS request detected:', url)
        }
      }

      // Monitor for potential data exfiltration
      if (config?.method && config.method.toUpperCase() !== 'GET') {
        const domain = new URL(url).hostname
        const allowedDomains = ['digiflow.digiturk.com.tr', 'digiflowtest.digiturk.com.tr', 'localhost', '127.0.0.1']

        if (!allowedDomains.some((allowed) => domain.includes(allowed))) {
          if (process.env.NODE_ENV === 'development') {
            void console.warn('⚠️ Request to external domain:', domain)
          }
        }
      }

      return originalFetch(...args)
    }
  } // End of setupNetworkMonitoring

  /**
   * Run initial security audit
   */
  private async runInitialSecurityAudit(): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      void console.warn('🔍 Running initial security audit...')
    }

    try {
      const auditResult = await securityAuditService.performSecurityAudit()

      if (process.env.NODE_ENV === 'development') {
        console.warn(`📊 Security audit completed: ${auditResult.score}/100 (${auditResult.overall})`)
      }

      // Log critical issues
      if (auditResult.overall === 'CRITICAL') {
        if (process.env.NODE_ENV === 'development') {
          console.error('🚨 CRITICAL security issues found!')
        }
        auditResult.recommendations
          .filter((_rec) => _rec.priority === 'CRITICAL')
          .forEach((_rec) => {
            if (process.env.NODE_ENV === 'development') {
              console.error(`🔴 CRITICAL: ${_rec.title} - ${_rec.description}`)
            }
          })
      }

      // Log warnings
      if (auditResult.overall === 'WARNING') {
        if (process.env.NODE_ENV === 'development') {
          void console.warn('⚠️ Security warnings found')
        }
        auditResult.recommendations
          .filter((_rec) => _rec.priority === 'HIGH')
          .forEach((_rec) => {
            if (process.env.NODE_ENV === 'development') {
              void console.warn(`🟡 WARNING: ${_rec.title} - ${_rec.description}`)
            }
          })
      }

      // Store audit result for later access
      ;(window as any)._SECURITY_AUDIT_RESULT__ = auditResult
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Security audit failed:', _error)
      }
    }
  }

  /**
   * Start periodic security audits
   */
  private startPeriodicAudits(): void {
    if (!this.config.enableSecurityAudit) return

    const intervalMs = this.config.auditInterval * 60 * 1000

    this.auditTimer = setInterval(async () => {
      try {
        const auditResult = await securityAuditService.performSecurityAudit()

        // Only log if status changed or critical issues found
        if (auditResult.overall === 'CRITICAL') {
          if (process.env.NODE_ENV === 'development') {
            console.error('🚨 Periodic audit: CRITICAL issues detected!')
          }
        }

        // Update stored result
        ;(window as any)._SECURITY_AUDIT_RESULT__ = auditResult
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ Periodic security audit failed:', _error)
        }
      }
    }, intervalMs)

    if (process.env.NODE_ENV === 'development') {
      void console.warn(`📅 Periodic security audits scheduled every ${this.config.auditInterval} minutes`)
    }
  }

  /**
   * Stop periodic audits
   */
  stopPeriodicAudits(): void {
    if (this.auditTimer) {
      clearInterval(this.auditTimer)
      this.auditTimer = undefined
      if (process.env.NODE_ENV === 'development') {
        void console.warn('🛑 Periodic security audits stopped')
      }
    }
  }

  /**
   * Get current security status
   */
  async getCurrentSecurityStatus(): Promise<any> {
    return (window as any)._SECURITY_AUDIT_RESULT__ ?? (await securityAuditService.performSecurityAudit())
  }

  /**
   * Update security configuration
   */
  updateConfig(newConfig: Partial<SecurityInitializationConfig>): void {
    this.config = { ...this.config, ...newConfig }
    if (process.env.NODE_ENV === 'development') {
      void console.warn('🔧 Security configuration updated')
    }
  }
}

// Export singleton instance
export default SecurityInitializer.getInstance()
