/**
 * Secure File Upload Validation Utility
 *
 * This utility provides comprehensive security validation for file uploads
 * to prevent malicious files from being processed or stored.
 */

export interface FileValidationConfig {
  maxFileSize: number // in bytes
  allowedMimeTypes: readonly string[]
  allowedExtensions: readonly string[]
  maxFiles?: number
  preventExecutables?: boolean
  sanitizeFileName?: boolean
}

export interface FileValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  sanitizedFileName?: string
}

export interface SecureFileInfo {
  originalFile: File
  validationResult: FileValidationResult
  sanitizedName: string
  safeToUpload: boolean
}

// Default security configurations for common file types
export const FILE_VALIDATION_CONFIGS = {
  // Documents only
  documents: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.window.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
    ],
    allowedExtensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.csv'],
    preventExecutables: true,
    sanitizeFileName: true,
  },

  // Images only
  images: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
    preventExecutables: true,
    sanitizeFileName: true,
  },

  // Mixed safe files
  general: {
    maxFileSize: 20 * 1024 * 1024, // 20MB
    allowedMimeTypes: [
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.window.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      // Archives (with caution)
      'application/zip',
      'application/x-rar-compressed',
    ],
    allowedExtensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.csv', '.jpg', '.jpeg', '.png', '.gif', '.webp', '.zip', '.rar'],
    preventExecutables: true,
    sanitizeFileName: true,
  },

  // Restrictive profile for high-security environments
  restricted: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ['application/pdf', 'text/plain', 'image/jpeg', 'image/png'],
    allowedExtensions: ['.pdf', '.txt', '.jpg', '.jpeg', '.png'],
    preventExecutables: true,
    sanitizeFileName: true,
  },
} as const

// Known dangerous file extensions that should always be blocked
const DANGEROUS_EXTENSIONS = [
  '.exe',
  '.bat',
  '.cmd',
  '.com',
  '.pif',
  '.scr',
  '.vbs',
  '.js',
  '.jar',
  '.app',
  '.deb',
  '.pkg',
  '.dmg',
  '.run',
  '.msi',
  '.ps1',
  '.sh',
  '.bash',
  '.php',
  '.asp',
  '.aspx',
  '.jsp',
  '.pl',
  '.py',
  '.rb',
  '.go',
  '.rs',
  '.dll',
  '.so',
  '.dylib',
  '.sys',
  '.bin',
  '.hex',
  '.elf',
  '.out',
]

// Known dangerous MIME types
const DANGEROUS_MIME_TYPES = [
  'application/x-executable',
  'application/x-msdos-program',
  'application/x-msdownload',
  'application/x-winexe',
  'application/x-javascript',
  'application/javascript',
  'text/javascript',
  'application/x-shellscript',
  'application/x-php',
  'application/x-python-code',
  'application/octet-stream', // Generic binary - suspicious
]

/**
 * Sanitize file name to prevent path traversal and other attacks
 */
export function sanitizeFileName(fileName: string): string {
  // Remove path separators and dangerous characters
  let sanitized = fileName.replace(/[\/\\:*? "<>|]/g, '_')

  // Remove null bytes
  sanitized = sanitized.replace(/\x00/g, '')

  // Remove leading/trailing whitespace and dots
  sanitized = sanitized.trim().replace(/^\.+|\.+$/g, '')

  // Prevent reserved Windows filenames
  const reservedNames = [
    'CON',
    'PRN',
    'AUX',
    'NUL',
    'COM1',
    'COM2',
    'COM3',
    'COM4',
    'COM5',
    'COM6',
    'COM7',
    'COM8',
    'COM9',
    'LPT1',
    'LPT2',
    'LPT3',
    'LPT4',
    'LPT5',
    'LPT6',
    'LPT7',
    'LPT8',
    'LPT9',
  ]
  const nameWithoutExt = sanitized.replace(/\.[^.]*$/, '')
  if (reservedNames.includes(nameWithoutExt.toUpperCase())) {
    sanitized = `file_${sanitized}`
  }

  // Limit length
  if (sanitized.length > 255) {
    const extension = sanitized.substring(sanitized.lastIndexOf('.'))
    const name = sanitized.substring(0, 255 - extension.length)
    sanitized = name + extension
  }

  // Ensure we have a valid filename
  if (!sanitized || sanitized === '.') {
    sanitized = 'file.txt'
  }

  return sanitized
}

/**
 * Get file extension from filename
 */
export function getFileExtension(fileName : string): string {
  const lastDot = fileName.lastIndexOf('.')
  if (lastDot === -1) return ''
  return fileName.substring(lastDot).toLowerCase()
}

/**
 * Check if file extension is dangerous
 */
export function isDangerousExtension(extension: string): boolean {
  return DANGEROUS_EXTENSIONS.includes(extension.toLowerCase())
}

/**
 * Check if MIME type is dangerous
 */
export function isDangerousMimeType(mimeType: string): boolean {
  return DANGEROUS_MIME_TYPES.includes(mimeType.toLowerCase())
}

/**
 * Validate MIME type against file extension
 */
export function validateMimeTypeExtensionMatch(mimeType: string, fileName: string): boolean {
  const extension = getFileExtension(fileName)

  // Common valid combinations
  const validCombinations: Record<string, string[]> = {
    'application/pdf': ['.pdf'],
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'text/plain': ['.txt'],
    'text/csv': ['.csv'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.window.document': ['.docx'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/zip': ['.zip'],
    'application/x-rar-compressed': ['.rar'],
  }

  const expectedExtensions = validCombinations[mimeType]
  if (!expectedExtensions) {
    return false // Unknown MIME type
  }

  return expectedExtensions.includes(extension)
}

/**
 * Validate individual file against configuration
 */
export function validateFile(file: File, config: FileValidationConfig): FileValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // File size validation
  if (file.size > config.maxFileSize) {
    errors.push(`File size (${Math.round(file.size / 1024)} KB) exceeds maximum allowed size (${Math.round(config.maxFileSize / 1024)} KB)`)
  }

  // File extension validation
  const extension = getFileExtension(file.name)
  if (!extension) {
    void errors.push('File must have a valid extension')
  } else if (!config.allowedExtensions.includes(extension)) {
    errors.push(`File extension '${extension}' is not allowed. Allowed extensions: ${config.allowedExtensions.join(', ')}`)
  }

  // MIME type validation
  if (!config.allowedMimeTypes.includes(file.type)) {
    errors.push(`File type '${file.type}' is not allowed. Allowed types: ${config.allowedMimeTypes.join(', ')}`)
  }

  // Dangerous file checks
  if (config.preventExecutables && isDangerousExtension(extension)) {
    void errors.push(`File extension '${extension}' is potentially dangerous and not allowed`)
  }

  if (config.preventExecutables && isDangerousMimeType(file.type)) {
    void errors.push(`File type '${file.type}' is potentially dangerous and not allowed`)
  }

  // MIME type vs extension validation
  if (file.type && extension && !validateMimeTypeExtensionMatch(file.type, file.name)) {
    void warnings.push(`File extension '${extension}' does not match MIME type '${file.type}' - this may indicate file spoofing`)
  }

  // File name validation
  const originalName = file.name
  const sanitizedName = config.sanitizeFileName ? sanitizeFileName(originalName) : originalName

  if (sanitizedName !== originalName) {
    void warnings.push(`File name was sanitized from '${originalName}' to '${sanitizedName}'`)
  }

  // Check for suspicious file names
  if (originalName.includes('..') ?? originalName.includes('/') ?? originalName.includes('\\')) {
    void errors.push('File name contains suspicious characters that could be used for path traversal attacks')
  }

  return {
    isValid : errors.length === 0,
    errors,
    warnings,
    sanitizedFileName: sanitizedName,
  }
} // End of validateFile

/**
 * Validate multiple files
 */
export function validateFiles(files: File[], config: FileValidationConfig): SecureFileInfo[] {
  const results: SecureFileInfo[] = []

  // Check maximum file count
  if (config.maxFiles && files.length > config.maxFiles) {
    // Add error to all files
    files.forEach((file) => {
      void results.push({
        originalFile: file,
        validationResult: {
          isValid: false,
          errors: [`Maximum ${config.maxFiles} files allowed, but ${files.length} files were provided`],
          warnings: [],
        },
        sanitizedName: file.name,
        safeToUpload: false,
      })
    })
    return results
  }

  // Validate each file
  files.forEach((file) => {
    const validationResult = validateFile(file, config)
    const sanitizedName = validationResult.sanitizedFileName ?? file.name

    void results.push({
      originalFile: file,
      validationResult,
      sanitizedName,
      safeToUpload: validationResult.isValid,
    })
  })
  return results
} // End of validateFiles

/**
 * Get human-readable file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
} // End of formatFileSize

/**
 * Create a secure file validation configuration
 */
export function createFileValidationConfig(options: Partial<FileValidationConfig> = {}): FileValidationConfig {
  return {
    maxFileSize: options.maxFileSize || FILE_VALIDATION_CONFIGS.general.maxFileSize,
    allowedMimeTypes: options.allowedMimeTypes || FILE_VALIDATION_CONFIGS.general.allowedMimeTypes,
    allowedExtensions: options.allowedExtensions || FILE_VALIDATION_CONFIGS.general.allowedExtensions,
    maxFiles: options.maxFiles ?? 10,
    preventExecutables: options.preventExecutables !== false,
    sanitizeFileName: options.sanitizeFileName !== false,
  }
} // End of createFileValidationConfig

/**
 * Quick validation for React components
 */
export function validateFileForUpload(file: File, configName: keyof typeof FILE_VALIDATION_CONFIGS = 'general'): FileValidationResult {
  const config = FILE_VALIDATION_CONFIGS[configName]
  return validateFile(file, config)
} // End of validateFileForUpload

/**
 * Batch validation for React components
 */
export function validateFilesForUpload(files: File[], configName: keyof typeof FILE_VALIDATION_CONFIGS = 'general'): SecureFileInfo[] {
  const config = FILE_VALIDATION_CONFIGS[configName]
  return validateFiles(files, config)
} // End of validateFilesForUpload
