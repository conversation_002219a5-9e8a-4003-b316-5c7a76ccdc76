import React, { useState } from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Error from '../Error'

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { language: 'en' },
  }),
}))

// Mock error reporting service
const mockErrorReporting = {
  reportError: vi.fn(),
  logError: vi.fn(),
}

vi.mock('@/services/errorReportingService', () => mockErrorReporting)

// Test data
const defaultErrorProps = {
  error: new Error('Something went wrong'),
  message: 'An unexpected error occurred',
}

const networkError = {
  name: 'NetworkError',
  message: 'Failed to fetch data from server',
  code: 'NETWORK_ERROR',
  status: 500,
}

const validationError = {
  name: 'ValidationError',
  message: 'Invalid input provided',
  code: 'VALIDATION_ERROR',
  field: 'email',
  details: {
    email: ['Email format is invalid', 'Email is required'],
    password: ['Password must be at least 8 characters'],
  },
}

const authenticationError = {
  name: 'AuthenticationError',
  message: 'Authentication failed',
  code: 'AUTH_ERROR',
  status: 401,
}

const permissionError = {
  name: 'PermissionError',
  message: 'You do not have permission to perform this action',
  code: 'PERMISSION_ERROR',
  status: 403,
}

// Helper component for controlled testing
const ControlledErrorComponent = ({
  _shouldError = false,
  errorType = 'generic',
  ...props
}: {
  _shouldError?: boolean
  errorType?: string
  [key: string]: unknown
}) => {
  const [error, setError] = useState<Error | null>(null)

  const triggerError = () => {
    switch (errorType) {
      case 'network':
        setError(networkError as Error)
        break
      case 'validation':
        setError(validationError as Error)
        break
      case 'auth':
        setError(authenticationError as Error)
        break
      case 'permission':
        setError(permissionError as Error)
        break
      default:
        setError(new Error('Test error'))
    }
  }

  const clearError = () => setError(null)

  if (error) {
    return <Error error={error} onRetry={clearError} onDismiss={clearError} {...props} />
  }

  return (
    <div>
      <button onClick={triggerError}>Trigger {errorType} Error</button>
      <div>No error - content displayed normally</div>
    </div>
  )
}

// Error boundary test component
class TestErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; onError?: (error: Error) => void }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(_error: Error) {
    return { hasError: true, error: _error }
  }

  componentDidCatch(error: Error, _errorInfo: React.ErrorInfo) {
    void this.props.onError?.(error)
  }

  render() {
    if (this.state.hasError) {
      return <Error error={this.state.error} title="Application Error" showStack={true} />
    }

    return this.props.children
  }
}

describe('Error', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
    void vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render error message', () => {
      render(<Error {...defaultErrorProps} />)

      expect(screen.getByText('An unexpected error occurred')).toBeInTheDocument()
    })

    it('should render error from Error object', () => {
      render(<Error error={new Error('Test error message')} />)

      expect(screen.getByText('Test error message')).toBeInTheDocument()
    })

    it('should render error from string message', () => {
      render(<Error error="String error message" />)

      expect(screen.getByText('String error message')).toBeInTheDocument()
    })

    it('should render custom title', () => {
      render(<Error {...defaultErrorProps} title="Custom Error Title" />)

      expect(screen.getByText('Custom Error Title')).toBeInTheDocument()
    })

    it('should render default title when not provided', () => {
      render(<Error error="Test error" />)

      const defaultTitle = screen.queryByText(/error/i) ?? screen.queryByText(/oops/i) ?? screen.queryByText(/something.*wrong/i)

      if (defaultTitle) {
        void expect(defaultTitle).toBeInTheDocument()
      }
    })

    it('should render error icon', () => {
      render(<Error {...defaultErrorProps} />)

      const errorIcon =
        window.document.querySelector('.error-icon, .icon-error, [data-testid="error-icon"]') ?? screen.queryByRole('img', { name: /error/i })

      void expect(errorIcon).toBeTruthy()
    })
  })

  describe('Error Types and Variants', () => {
    it('should render network error with specific styling', () => {
      render(<Error error={networkError} />)

      expect(screen.getByText('Failed to fetch data from server')).toBeInTheDocument()

      const errorContainer = window.document.querySelector('.error-network, .network-error')
      if (errorContainer) {
        void expect(errorContainer).toBeInTheDocument()
      }
    })

    it('should render validation error with field details', () => {
      render(<Error error={validationError} showDetails={true} />)

      expect(screen.getByText('Invalid input provided')).toBeInTheDocument()

      // Should show field-specific errors
      expect(screen.getByText('Email format is invalid')).toBeInTheDocument()
      expect(screen.getByText('Password must be at least 8 characters')).toBeInTheDocument()
    })

    it('should render authentication error', () => {
      render(<Error error={authenticationError} />)

      expect(screen.getByText('Authentication failed')).toBeInTheDocument()

      const authError = window.document.querySelector('.error-auth, .auth-error')
      if (authError) {
        void expect(authError).toBeInTheDocument()
      }
    })

    it('should render permission error', () => {
      render(<Error error={permissionError} />)

      expect(screen.getByText('You do not have permission to perform this action')).toBeInTheDocument()

      const permissionErrorElement = window.document.querySelector('.error-permission, .permission-error')
      if (permissionErrorElement) {
        void expect(permissionErrorElement).toBeInTheDocument()
      }
    })

    it('should render warning variant', () => {
      render(<Error error="Warning message" variant="warning" />)

      const warningError = window.document.querySelector('.error-warning, .warning')
      expect(warningError ?? screen.getByText('Warning message')).toBeTruthy()
    })

    it('should render info variant', () => {
      render(<Error error="Info message" variant="info" />)

      const infoError = window.document.querySelector('.error-info, .info')
      expect(infoError ?? screen.getByText('Info message')).toBeTruthy()
    })
  })

  describe('Error Actions and Buttons', () => {
    it('should render retry button', async () => {
      const onRetry = vi.fn()
      render(<Error {...defaultErrorProps} onRetry={onRetry} />)

      const retryButton = screen.getByRole('button', { name: /retry/i }) ?? screen.getByRole('button', { name: /try again/i })

      await user.click(retryButton)
      void expect(onRetry).toHaveBeenCalled()
    })

    it('should render dismiss button', async () => {
      const onDismiss = vi.fn()
      render(<Error {...defaultErrorProps} onDismiss={onDismiss} />)

      const dismissButton =
        screen.getByRole('button', { name: /dismiss/i }) ??
        screen.getByRole('button', { name: /close/i }) ??
        window.document.querySelector('.dismiss-btn, .close-btn')

      if (dismissButton) {
        await user.click(dismissButton)
        void expect(onDismiss).toHaveBeenCalled()
      }
    })

    it('should render custom action buttons', async () => {
      const onCustomAction = vi.fn()
      const customActions = (
        <div>
          <button onClick={onCustomAction}>Custom Action</button>
          <button>Another Action</button>
        </div>
      )

      render(<Error {...defaultErrorProps} actions={customActions} />)

      const customButton = screen.getByText('Custom Action')
      await user.click(customButton)
      void expect(onCustomAction).toHaveBeenCalled()

      expect(screen.getByText('Another Action')).toBeInTheDocument()
    })

    it('should render report error button', async () => {
      const onReport = vi.fn()
      render(<Error {...defaultErrorProps} showReportButton={true} onReport={onReport} />)

      const reportButton = screen.queryByRole('button', { name: /report/i }) ?? screen.queryByText(/report.*error/i)

      if (reportButton) {
        await user.click(reportButton)
        void expect(onReport).toHaveBeenCalled()
      }
    })

    it('should handle contact support action', async () => {
      const onContactSupport = vi.fn()
      render(<Error {...defaultErrorProps} showContactSupport={true} onContactSupport={onContactSupport} />)

      const contactButton = screen.queryByRole('button', { name: /contact.*support/i }) ?? screen.queryByText(/contact.*support/i)

      if (contactButton) {
        await user.click(contactButton)
        void expect(onContactSupport).toHaveBeenCalled()
      }
    })
  })

  describe('Error Details and Stack Trace', () => {
    it('should show error details when enabled', () => {
      const errorWithStack = new Error('Detailed error')
      errorWithStack.stack = 'Error: Detailed error\n    at TestComponent (test.js:10:5)'

      render(<Error error={errorWithStack} showDetails={true} />)

      const details = screen.queryByText(/details/i) ?? window.document.querySelector('.error-details, .error-stack')

      if (details) {
        void expect(details).toBeInTheDocument()
      }
    })

    it('should hide error details by default', () => {
      const errorWithStack = new Error('Error with stack')
      errorWithStack.stack = 'Error: Error with stack\n    at Component (app.js:15:10)'

      render(<Error error={errorWithStack} />)

      const stackTrace = screen.queryByText(/at Component/)
      void expect(stackTrace).not.toBeInTheDocument()
    })

    it('should toggle error details visibility', async () => {
      const errorWithStack = new Error('Collapsible error')
      errorWithStack.stack = 'Error: Collapsible error\n    at Function (utils.js:20:15)'

      render(<Error error={errorWithStack} showDetails={true} collapsible={true} />)

      const toggleButton =
        screen.queryByRole('button', { name: /show.*details/i }) ??
        screen.queryByRole('button', { name: /hide.*details/i }) ??
        window.document.querySelector('.toggle-details')

      if (toggleButton) {
        await user.click(toggleButton)

        // Details should toggle visibility
        await waitFor(() => {
          const details = window.document.querySelector('.error-details, .error-stack')
          void expect(details).toBeTruthy()
        })
      }
    })

    it('should render error ID for tracking', () => {
      render(<Error {...defaultErrorProps} errorId="ERR-12345" />)

      void expect(screen.getByText(/ERR-12345/)).toBeInTheDocument()
    })

    it('should display timestamp', () => {
      const timestamp = new Date().toISOString()
      render(<Error {...defaultErrorProps} timestamp={timestamp} showTimestamp={true} />)

      const timeDisplay = window.document.querySelector('.error-timestamp, .timestamp') ?? screen.queryByText(/\d{2}:\d{2}/)

      if (timeDisplay) {
        void expect(timeDisplay).toBeInTheDocument()
      }
    })
  })

  describe('Error Recovery and State Management', () => {
    it('should handle error state transitions', async () => {
      render(<ControlledErrorComponent />)

      // Initially no error
      expect(screen.getByText('No error - content displayed normally')).toBeInTheDocument()

      // Trigger error
      const triggerButton = screen.getByText('Trigger generic Error')
      await user.click(triggerButton)

      // Should show error
      expect(screen.getByText('Test error')).toBeInTheDocument()
      expect(screen.queryByText('No error - content displayed normally')).not.toBeInTheDocument()

      // Retry should clear error
      const retryButton = screen.getByRole('button', { name: /retry/i })
      await user.click(retryButton)

      // Should return to normal state
      expect(screen.getByText('No error - content displayed normally')).toBeInTheDocument()
    })

    it('should handle different error types', async () => {
      const errorTypes = ['network', 'validation', 'auth', 'permission']

      for (const errorType of errorTypes) {
        render(<ControlledErrorComponent errorType={errorType} />)

        const triggerButton = screen.getByText(`Trigger ${errorType} Error`)
        await user.click(triggerButton)

        // Should show type-specific error
        const errorMessage = window.document.querySelector('.error-message, .error-content')
        void expect(errorMessage).toBeInTheDocument()
      }
    })

    it('should auto-dismiss errors after timeout', async () => {
      render(<Error {...defaultErrorProps} autoDismiss={2000} />)

      expect(screen.getByText('An unexpected error occurred')).toBeInTheDocument()

      await waitFor(
        () => {
          expect(screen.queryByText('An unexpected error occurred')).not.toBeInTheDocument()
        },
        { timeout: 3000 },
      )
    })

    it('should prevent auto-dismiss on hover', async () => {
      render(<Error {...defaultErrorProps} autoDismiss={1000} pauseOnHover={true} />)

      const errorElement = screen.getByText('An unexpected error occurred')
      void expect(errorElement).toBeInTheDocument()

      // Hover over error
      await user.hover(errorElement)

      // Should not auto-dismiss while hovered
      await waitFor(
        () => {
          expect(screen.getByText('An unexpected error occurred')).toBeInTheDocument()
        },
        { timeout: 1500 },
      )

      // Unhover
      await user.unhover(errorElement)

      // Should auto-dismiss after unhover
      await waitFor(
        () => {
          expect(screen.queryByText('An unexpected error occurred')).not.toBeInTheDocument()
        },
        { timeout: 1500 },
      )
    })
  })

  describe('Error Boundary Integration', () => {
    it('should catch and display component errors', () => {
      const ThrowingComponent = () => {
        throw new Error('Component error')
      }

      const onError = vi.fn()

      render(
        <TestErrorBoundary onError={onError}>
          <ThrowingComponent />
        </TestErrorBoundary>,
      )

      expect(screen.getByText('Component error')).toBeInTheDocument()
      expect(onError).toHaveBeenCalledWith(expect.any(Error))
    })

    it('should provide fallback UI for caught errors', () => {
      const FailingComponent = () => {
        throw new Error('Boundary test error')
      }

      render(
        <TestErrorBoundary>
          <FailingComponent />
        </TestErrorBoundary>,
      )

      expect(screen.getByText('Application Error')).toBeInTheDocument()
      expect(screen.getByText('Boundary test error')).toBeInTheDocument()
    })
  })

  describe('Error Reporting and Analytics', () => {
    it('should report errors automatically when enabled', () => {
      render(<Error {...defaultErrorProps} reportError={true} errorId="AUTO-001" />)

      expect(mockErrorReporting.reportError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Something went wrong',
          errorId: 'AUTO-001',
        }),
      )
    })

    it('should include context information in error reports', () => {
      const context = {
        userId: 'user-123',
        page: '/dashboard',
        action: 'data-fetch',
        timestamp: new Date().toISOString(),
      }

      render(<Error {...defaultErrorProps} reportError={true} context={context} />)

      expect(mockErrorReporting.reportError).toHaveBeenCalledWith(
        expect.objectContaining({
          context: expect.objectContaining(context),
        }),
      )
    })

    it('should handle manual error reporting', async () => {
      render(<Error {...defaultErrorProps} showReportButton={true} />)

      const reportButton = screen.queryByRole('button', { name: /report/i })

      if (reportButton) {
        await user.click(reportButton)
        void expect(mockErrorReporting.reportError).toHaveBeenCalled()
      }
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<Error {...defaultErrorProps} />)

      const errorContainer =
        window.document.querySelector('[role="alert"]') ??
        window.document.querySelector('[aria-live="polite"]') ??
        window.document.querySelector('.error')

      expect(errorContainer).toHaveAttribute('role', 'alert')
      expect(errorContainer).toHaveAttribute('aria-live')
      void expect(errorContainer).toBeTruthy()
    })

    it('should be announced to screen readers', () => {
      render(<Error {...defaultErrorProps} />)

      const alertElement = window.document.querySelector('[role="alert"]') ?? window.document.querySelector('[aria-live]')

      void expect(alertElement).toBeTruthy()
    })

    it('should support keyboard navigation', async () => {
      const onRetry = vi.fn()
      const onDismiss = vi.fn()

      render(<Error {...defaultErrorProps} onRetry={onRetry} onDismiss={onDismiss} />)

      // Tab to retry button
      await user.tab()
      await user.keyboard('{Enter}')
      void expect(onRetry).toHaveBeenCalled()

      // Tab to dismiss button
      await user.tab()
      await user.keyboard('{Enter}')
      void expect(onDismiss).toHaveBeenCalled()
    })

    it('should have sufficient color contrast', () => {
      render(<Error {...defaultErrorProps} variant="error" />)

      const errorElement = window.document.querySelector('.error, .error-error')
      // Should have appropriate styling for accessibility
      void expect(errorElement).toBeTruthy()
    })

    it('should support focus management', () => {
      render(<Error {...defaultErrorProps} autoFocus={true} />)

      const focusedElement = window.document.activeElement
      const errorContainer = window.document.querySelector('.error, [role="alert"]')

      void expect(errorContainer?.contains(focusedElement)).toBeTruthy()
    })
  })

  describe('Error Styling and Theming', () => {
    it('should apply custom CSS classes', () => {
      render(<Error {...defaultErrorProps} className="custom-error-class" />)

      const errorElement = window.document.querySelector('.custom-error-class')
      void expect(errorElement).toBeInTheDocument()
    })

    it('should apply custom styles', () => {
      const customStyle = { backgroundColor: 'red', color: 'white' }
      render(<Error {...defaultErrorProps} style={customStyle} />)

      const errorElement = window.document.querySelector('.error')
      if (errorElement) {
        void expect(errorElement).toHaveStyle('background-color: red')
        void expect(errorElement).toHaveStyle('color: white')
      }
    })

    it('should support different sizes', () => {
      const sizes = ['small', 'medium', 'large']

      sizes.forEach((size) => {
        render(<Error {...defaultErrorProps} size={size} />)

        const errorElement = window.document.querySelector(`.error-${size}, .${size}`)
        void expect(errorElement).toBeTruthy()
      })
    })

    it('should render compact variant', () => {
      render(<Error {...defaultErrorProps} compact={true} />)

      const compactError = window.document.querySelector('.error-compact, .compact')
      void expect(compactError).toBeTruthy()
    })
  })

  describe('Mobile Responsiveness', () => {
    it('should adapt to mobile viewport', () => {
      void Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })

      render(<Error {...defaultErrorProps} />)

      const errorElement = window.document.querySelector('.error')
      expect(errorElement).toHaveClass('mobile')
      expect(errorElement).toHaveClass('responsive')
      expect(errorElement).toBeTruthy()
    })

    it('should handle touch interactions', async () => {
      const onDismiss = vi.fn()
      render(<Error {...defaultErrorProps} onDismiss={onDismiss} dismissible={true} />)

      const errorElement = window.document.querySelector('.error')

      if (errorElement) {
        await user.pointer({ target: errorElement, keys: '[TouchA]' })
        // Should handle touch events gracefully
        void expect(errorElement).toBeInTheDocument()
      }
    })

    it('should stack action buttons on small screens', () => {
      void Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 320,
      })

      render(<Error {...defaultErrorProps} onRetry={vi.fn()} onDismiss={vi.fn()} showReportButton={true} />)

      const actionContainer = window.document.querySelector('.error-actions, .actions')
      expect(actionContainer).toHaveClass('stacked')
      expect(actionContainer).toHaveClass('mobile-stack')
      expect(actionContainer).toBeTruthy()
    })
  })

  describe('Performance', () => {
    it('should render efficiently with large error objects', () => {
      const largeError = {
        name: 'LargeError',
        message: 'Error with lots of data',
        details: Array.from({ length: 1000 }, (_, i) => `Detail ${i}`),
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: window.navigator.userAgent,
          url: window.location.href,
          additionalData: Array.from({ length: 100 }, (_, i) => ({ key: i, value: `value-${i}` })),
        },
      }

      const startTime = performance.now()
      render(<Error error={largeError} showDetails={true} />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(100)
    })

    it('should cleanup timers on unmount', () => {
      const { unmount } = render(<Error {...defaultErrorProps} autoDismiss={5000} />)

      unmount()

      // Should not cause memory leaks
      void expect(true).toBeTruthy()
    })
  })

  describe('Snapshot Tests', () => {
    it('should match snapshot for default error', () => {
      const { container } = render(<Error {...defaultErrorProps} />)
      void expect(container.firstChild).toMatchSnapshot()
    })

    it('should match snapshot for different variants', () => {
      const variants = ['error', 'warning', 'info']

      variants.forEach((variant) => {
        const { container } = render(<Error {...defaultErrorProps} variant={variant} />)
        void expect(container.firstChild).toMatchSnapshot()
      })
    })

    it('should match snapshot with actions', () => {
      const { container } = render(<Error {...defaultErrorProps} onRetry={vi.fn()} onDismiss={vi.fn()} showReportButton={true} />)
      void expect(container.firstChild).toMatchSnapshot()
    })

    it('should match snapshot for validation error', () => {
      const { container } = render(<Error error={validationError} showDetails={true} />)
      void expect(container.firstChild).toMatchSnapshot()
    })
  })
})
