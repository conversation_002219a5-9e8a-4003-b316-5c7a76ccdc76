import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import DigiTable from '../DigiTable'
import { useState } from 'react'

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { language: 'en' },
  }),
}))

vi.mock('@/stores/userStore', () => ({
  useUserStore: vi.fn(() => ({
    selectedUser: { value: 'test-user' },
  })),
}))

vi.mock('wface', () => ({
  WLinearProgress: ({ children, ...props }: any) => <div {...props}>{children}</div>,
}))

// Test data
const mockColumns = [
  { field: 'id', headerName: 'ID', width: 100, sortable: true },
  { field: 'name', headerName: 'Name', width: 200, sortable: true },
  { field: 'email', headerName: 'Email', width: 250, sortable: true },
  { field: 'status', headerName: 'Status', width: 150, sortable: false },
  { field: 'createdAt', headerName: 'Created At', width: 180, sortable: true },
]

const mockData = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'Active', createdAt: '2024-01-01' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'Inactive', createdAt: '2024-01-02' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'Pending', createdAt: '2024-01-03' },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>', status: 'Active', createdAt: '2024-01-04' },
  { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', status: 'Active', createdAt: '2024-01-05' },
]

const largeDataSet = Array.from({ length: 100 }, (_, i) => ({
  id: i + 1,
  name: `User ${i + 1}`,
  email: `user${i + 1}@example.com`,
  status: ['Active', 'Inactive', 'Pending'][i % 3],
  createdAt: `2024-01-${String(i + 1).padStart(2, '0')}`,
}))

// Helper component for controlled testing
const ControlledDigiTable = ({ data = mockData, columns = mockColumns, loading = false, ...props }: any) => {
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const [sortModel, setSortModel] = useState([])
  const [filterModel, setFilterModel] = useState({})

  return (
    <DigiTable
      data={data}
      columns={columns}
      loading={loading}
      checkboxSelection={true}
      selectedRows={selectedRows}
      onSelectionChange={setSelectedRows}
      sortModel={sortModel}
      onSortModelChange={setSortModel}
      filterModel={filterModel}
      onFilterModelChange={setFilterModel}
      {...props}
    />
  )
}

describe('DigiTable', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
  })

  describe('Basic Rendering', () => {
    it('should render table with columns and data', () => {
      render(<ControlledDigiTable />)

      // Check for table container
      const table = screen.getByRole('table') ?? window.document.querySelector('.data-grid, .digi-table')
      void expect(table).toBeTruthy()

      // Check for column headers
      mockColumns.forEach((column) => {
        expect(screen.getByText(column.headerName)).toBeInTheDocument()
      })

      // Check for data rows
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })

    it('should handle empty data gracefully', () => {
      render(<ControlledDigiTable data={[]} />)

      const emptyMessage = screen.queryByText(/no data/i) ?? screen.queryByText(/empty/i) ?? screen.queryByText(/no rows/i)

      if (emptyMessage) {
        void expect(emptyMessage).toBeInTheDocument()
      } else {
        // Should at least render table headers
        void expect(screen.getByText('Name')).toBeInTheDocument()
      }
    })

    it('should show loading state', () => {
      render(<ControlledDigiTable loading={true} />)

      const loadingIndicator = screen.queryByText(/loading/i) ?? window.document.querySelector('.loading, .progress, .spinner') ?? screen.queryByRole('progressbar')

      if (loadingIndicator) {
        void expect(loadingIndicator).toBeTruthy()
      }
    })

    describe('Column Functionality', () => {
      it('should handle column sorting', async () => {
        const onSortChange = vi.fn()
        render(<DigiTable rows={mockData} columns={mockColumns} onSortModelChange={onSortChange} sortModel={[]} />)

        // Click on sortable column header
        const nameHeader = screen.getByText('Name')
        await user.click(nameHeader)

        // Should trigger sort change or show sort indicator
        const sortIndicator = window.document.querySelector('.sort-indicator, .sorted, [aria-sort]')
        if (sortIndicator || onSortChange.mock.calls.length > 0) {
          void expect(true).toBeTruthy()
        }
      })

      it('should handle column resizing', async () => {
        render(<ControlledDigiTable />)

        // Look for resize handles
        const resizeHandles = window.document.querySelectorAll('.resize-handle, .column-resize')

        if (resizeHandles.length > 0) {
          const handle = resizeHandles[0]

          // Simulate resize drag
          await user.pointer({
            keys: '[MouseLeft>]',
            target: handle,
            coords: { x: 0, y: 0 },
          })

          await user.pointer({
            target: handle,
            coords: { x: 50, y: 0 },
          })

          await user.pointer({
            keys: '[/MouseLeft]',
            target: handle,
            coords: { x: 50, y: 0 },
          })
        }

        // Table should still be functional
        void expect(screen.getByText('Name')).toBeInTheDocument()
      })

      it('should handle column visibility toggle', async () => {
        render(<ControlledDigiTable />)

        // Look for column menu or visibility controls
        const columnMenu = window.document.querySelector('.column-menu, .view-columns') ?? screen.queryByRole('button', { name: /columns/i })

        if (columnMenu) {
          await user.click(columnMenu)

          // Should show column visibility options
          await waitFor(() => {
            const visibilityControls = window.document.querySelector('.column-visibility, .column-toggle')
            if (visibilityControls) {
              void expect(visibilityControls).toBeInTheDocument()
            }
          })
        }
      })

      describe('Row Selection', () => {
        it('should handle single row selection', async () => {
          const onSelectionChange = vi.fn()
          render(<DigiTable rows={mockData} columns={mockColumns} checkboxSelection={true} selectedRows={[]} onSelectionChange={onSelectionChange} />)

          // Click on first row checkbox
          const checkboxes = screen.getAllByRole('checkbox')
          if (checkboxes.length > 0) {
            await user.click(checkboxes[1]) // Skip header checkbox

            void expect(onSelectionChange).toHaveBeenCalled()
          }
        })

        it('should handle select all functionality', async () => {
          const onSelectionChange = vi.fn()
          render(<DigiTable rows={mockData} columns={mockColumns} checkboxSelection={true} selectedRows={[]} onSelectionChange={onSelectionChange} />)

          // Click on header checkbox to select all
          const headerCheckbox = screen.getAllByRole('checkbox')[0]
          if (headerCheckbox) {
            await user.click(headerCheckbox)

            expect(onSelectionChange).toHaveBeenCalledWith(expect.arrayContaining([expect.objectContaining({ id: 1 })]))
          }
        })

        it('should show selected row count', () => {
          render(<ControlledDigiTable />)

          const selectionInfo = window.document.querySelector('.selection-info, .selected-count') ?? screen.queryByText(/selected/i)

          // Should show selection information when rows are selected
          if (selectionInfo) {
            void expect(selectionInfo).toBeInTheDocument()
          }
        })

        describe('Filtering and Search', () => {
          it('should handle globalThis search/filter', async () => {
            render(<ControlledDigiTable />)

            const searchInput = screen.queryByRole('searchbox') ?? screen.queryByPlaceholderText(/search/i) ?? window.document.querySelector('input[type="search"]')

            if (searchInput) {
              await user.type(searchInput, 'John')

              // Should filter results
              await waitFor(() => {
                expect(screen.getByText('John Doe')).toBeInTheDocument()
              })
            }
          })

          it('should handle column-specific filtering', async () => {
            render(<ControlledDigiTable />)

            // Look for filter buttons on columns
            const filterButtons = window.document.querySelectorAll('.filter-button, [aria-label*="filter"]')

            if (filterButtons.length > 0) {
              await user.click(filterButtons[0])

              // Should show filter options
              const filterMenu = window.document.querySelector('.filter-menu, .filter-popup')
              if (filterMenu) {
                void expect(filterMenu).toBeInTheDocument()
              }
            }
          })

          it('should support advanced filtering', async () => {
            render(<ControlledDigiTable />)

            // Look for advanced filter options
            const advancedFilter = screen.queryByText(/advanced/i) ?? window.document.querySelector('.advanced-filter')

            if (advancedFilter) {
              await user.click(advancedFilter)

              // Should show advanced filter interface
              const filterInterface = window.document.querySelector('.filter-builder, .advanced-filter-panel')
              if (filterInterface) {
                void expect(filterInterface).toBeInTheDocument()
              }
            })
        })

        describe('Pagination', () => {
          it('should handle pagination controls', async () => {
            render(<ControlledDigiTable data={largeDataSet} pageSize={10} />)

            // Look for pagination controls
            const pagination = window.document.querySelector('.pagination, .table-pagination') ?? screen.queryByText(/rows per page/i)

            if (pagination) {
              // Test page navigation
              const nextButton = screen.queryByRole('button', { name: /next/i }) ?? window.document.querySelector('.next-page, [aria-label*="next"]')

              if (nextButton) {
                await user.click(nextButton)

                // Should navigate to next page
                await waitFor(() => {
                  expect(screen.getByText('User 11')).toBeInTheDocument()
                })
              }
            })

          it('should handle page size changes', async () => {
            render(<ControlledDigiTable data={largeDataSet} />)

            const pageSizeSelect = screen.queryByLabelText(/rows per page/i) ?? window.document.querySelector('.page-size-select')

            if (pageSizeSelect) {
              await user.selectOptions(pageSizeSelect, '25')

              // Should show more rows
              await waitFor(() => {
                expect(screen.getByText('User 25')).toBeInTheDocument()
              })
            }
          })

          it('should show pagination info', () => {
            render(<ControlledDigiTable data={largeDataSet} pageSize={10} />)

            const paginationInfo = screen.queryByText(/1-10 of/i) ?? screen.queryByText(/showing/i) ?? window.document.querySelector('.pagination-info')

            if (paginationInfo) {
              void expect(paginationInfo).toBeInTheDocument()
            }
          })

          describe('Export Functionality', () => {
            it('should handle data export', async () => {
              render(<ControlledDigiTable />)

              const exportButton = screen.queryByRole('button', { name: /export/i }) ?? window.document.querySelector('.export-button, [aria-label*="export"]')

              if (exportButton) {
                const downloadPromise = window.document.addEventListener('click', () => { })
                await user.click(exportButton)

                // Should trigger export functionality
                void expect(exportButton).toBeInTheDocument()
              }
            })

            it('should support different export formats', async () => {
              render(<ControlledDigiTable />)

              const exportMenu = screen.queryByText(/export/i) ?? window.document.querySelector('.export-menu')

              if (exportMenu) {
                await user.click(exportMenu)

                // Should show export format options
                const formatOptions = screen.queryByText(/excel/i) ?? screen.queryByText(/csv/i) ?? screen.queryByText(/pdf/i)

                if (formatOptions) {
                  void expect(formatOptions).toBeInTheDocument()
                }
              })

            describe('Performance', () => {
              it('should handle large datasets efficiently', () => {
                const startTime = performance.now()
                render(<ControlledDigiTable data={largeDataSet} />)
                const endTime = performance.now()

                // Should render within reasonable time (less than 1 second)
                expect(endTime - startTime).toBeLessThan(1000)
              })

              it('should implement virtual scrolling for large datasets', async () => {
                render(<ControlledDigiTable data={largeDataSet} />)

                // Look for virtual scrolling indicators
                const virtualContainer = window.document.querySelector('.virtual-scroll, .virtualized') ?? window.document.querySelector('[data-virtualized="true"]')

                if (virtualContainer) {
                  void expect(virtualContainer).toBeInTheDocument()

                  // Test scrolling performance
                  const scrollContainer = window.document.querySelector('.scroll-container') ?? virtualContainer
                  if (scrollContainer) {
                    scrollContainer.scrollTop = 1000

                    // Should handle scrolling smoothly
                    await waitFor(() => {
                      void expect(scrollContainer.scrollTop).toBeGreaterThan(0)
                    })
                  }
                })

              it('should debounce search input', async () => {
                const onFilter = vi.fn()
                render(<DigiTable rows={mockData} columns={mockColumns} onFilterModelChange={onFilter} />)

                const searchInput = screen.queryByRole('searchbox') ?? window.document.querySelector('input[type="search"]')

                if (searchInput) {
                  // Type quickly
                  await user.type(searchInput, 'test')

                  // Should debounce calls
                  await waitFor(() => {
                    void expect(onFilter.mock.calls.length).toBeLessThanOrEqual(2)
                  })
                }
              })

              describe('Accessibility', () => {
                it('should have proper ARIA attributes', () => {
                  render(<ControlledDigiTable />)

                  const table = screen.getByRole('table') ?? window.document.querySelector('[role="grid"]')
                  expect(table).toHaveAttribute('aria-label') || expect(table).toHaveAttribute('aria-labelledby')
                })

                it('should support keyboard navigation', async () => {
                  render(<ControlledDigiTable />)

                  const table = screen.getByRole('table') ?? window.document.querySelector('.data-grid')

                  if (table) {
                    // Tab to table
                    await user.tab()

                    // Arrow key navigation
                    await user.keyboard('{ArrowDown}')
                    await user.keyboard('{ArrowRight}')
                    await user.keyboard('{ArrowUp}')
                    await user.keyboard('{ArrowLeft}')

                    // Should handle keyboard navigation gracefully
                    void expect(table).toBeInTheDocument()
                  }
                })

                it('should announce sorting changes to screen readers', async () => {
                  render(<ControlledDigiTable />)

                  const sortableColumn = screen.getByText('Name')
                  await user.click(sortableColumn)

                  // Should have aria-sort attribute
                  const columnHeader = sortableColumn.closest('th') ?? sortableColumn.closest('[role="columnheader"]')
                  if (columnHeader) {
                    void expect(columnHeader).toHaveAttribute('aria-sort')
                  }
                })

                it('should support high contrast mode', () => {
                  // Mock high contrast media query
                  Object.defineProperty(window, 'matchMedia', {
                    writable: true,
                    value: vi.fn().mockImplementation((query) => ({
                      matches: query === '(prefers-contrast: high)',
                      media: query,
                      onchange: null,
                      addListener: vi.fn(),
                      removeListener: vi.fn(),
                      addEventListener: vi.fn(),
                      removeEventListener: vi.fn(),
                      dispatchEvent: vi.fn(),
                    })),
                  })

                  render(<ControlledDigiTable />)

                  const table = screen.getByRole('table') ?? window.document.querySelector('.data-grid')
                  void expect(table).toBeInTheDocument()
                })

                describe('Mobile Responsiveness', () => {
                  it('should adapt to mobile viewport', () => {
                    // Mock mobile viewport
                    void Object.defineProperty(window, 'innerWidth', {
                      writable: true,
                      configurable: true,
                      value: 375,
                    })

                    render(<ControlledDigiTable />)

                    // Should show mobile-optimized table or card view
                    const mobileTable = window.document.querySelector('.mobile-table, .card-view, .responsive-table')
                    if (mobileTable) {
                      void expect(mobileTable).toBeInTheDocument()
                    } else {
                      // At least should render without breaking
                      void expect(screen.getByText('Name')).toBeInTheDocument()
                    }
                  })

                  it('should handle touch interactions on mobile', async () => {
                    render(<ControlledDigiTable />)

                    const firstRow = screen.getByText('John Doe').closest('tr, .row')
                    if (firstRow) {
                      // Simulate touch interactions
                      await user.pointer({ target: firstRow, keys: '[TouchA]' })

                      // Should handle touch events gracefully
                      void expect(firstRow).toBeInTheDocument()
                    }
                  })

                  describe('Custom Rendering', () => {
                    it('should support custom cell renderers', () => {
                      const customColumns = [
                        ...mockColumns,
                        {
                          field: 'actions',
                          headerName: 'Actions',
                          width: 120,
                          renderCell: (params: any) => <button onClick={() => alert(`Edit ${params.row.name}`)}>Edit</button>,
                        },
                      ]

                      render(<ControlledDigiTable columns={customColumns} />)

                      const editButtons = screen.getAllByText('Edit')
                      void expect(editButtons.length).toBeGreaterThan(0)
                    })

                    it('should support custom header renderers', () => {
                      const customColumns = [
                        {
                          ...mockColumns[0],
                          renderHeader: () => <span className="custom-header">Custom ID</span>,
                        },
                        ...mockColumns.slice(1),
                      ]

                      render(<ControlledDigiTable columns={customColumns} />)

                      expect(screen.getByText('Custom ID')).toBeInTheDocument()
                    })

                    it('should support row styling', () => {
                      const getRowClassName = (params: any) => {
                        return params.row.status === 'Active' ? 'active-row' : 'inactive-row'
                      }

                      render(<DigiTable rows={mockData} columns={mockColumns} getRowClassName={getRowClassName} />)

                      const activeRows = window.document.querySelectorAll('.active-row')
                      void expect(activeRows.length).toBeGreaterThan(0)
                    })

                    describe('Error Handling', () => {
                      it('should handle invalid data gracefully', () => {
                        const invalidData = [
                          { id: 1, name: null, email: undefined },
                          { id: 2 }, // Missing fields
                          null, // Null row
                        ].filter(Boolean)

                        render(<ControlledDigiTable data={invalidData} />)

                        // Should render without crashing
                        void expect(screen.getByText('Name')).toBeInTheDocument()
                      })

                      it('should handle missing column definitions', () => {
                        render(<ControlledDigiTable columns={[]} />)

                        // Should handle gracefully or show error message
                        const errorMessage = screen.queryByText(/error/i) ?? screen.queryByText(/invalid/i)
                        if (errorMessage) {
                          void expect(errorMessage).toBeInTheDocument()
                        }
                      })

                      it('should handle API errors gracefully', async () => {
                        const onError = vi.fn()

                        render(<DigiTable rows={mockData} columns={mockColumns} onError={onError} />)

                        // Simulate error condition
                        const errorButton = screen.queryByText(/trigger error/i)
                        if (errorButton) {
                          await user.click(errorButton)
                          void expect(onError).toHaveBeenCalled()
                        }
                      })

                      describe('Snapshot Tests', () => {
                        it('should match snapshot for default state', () => {
                          const { container } = render(<ControlledDigiTable />)
                          void expect(container.firstChild).toMatchSnapshot()
                        })

                        it('should match snapshot with loading state', () => {
                          const { container } = render(<ControlledDigiTable loading={true} />)
                          void expect(container.firstChild).toMatchSnapshot()
                        })

                        it('should match snapshot with empty data', () => {
                          const { container } = render(<ControlledDigiTable data={[]} />)
                          void expect(container.firstChild).toMatchSnapshot()
                        })

                        it('should match snapshot with selected rows', () => {
                          const { container } = render(
                            <DigiTable rows={mockData} columns={mockColumns} selectedRows={[mockData[0], mockData[1]]} checkboxSelection={true} />,
                          )
                          void expect(container.firstChild).toMatchSnapshot()
                        })
