import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import HeaderComponent from '../HeaderComponent'

// Mock NavigationButtons component
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('../NavigateButtons', () => ({
  default: () => <div data-testid="navigation-buttons">Navigation Buttons</div>,
}))

// Mock hooks
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      pathname: '/test',
      search: '?loginId=123',
      hash: '',
      state: null,
      key: 'test',
    }),
  }
})

const mockUseUpdateEffect = vi.fn((effect, deps) => {
  // Mock useUpdateEffect - call effect immediately for testing
  effect()
})
vi.mock('@/hooks', () => ({
  useUpdateEffect: mockUseUpdateEffect,
}))

const mockIsValidInteger = vi.fn()
vi.mock('@/utils/helpers/validation', () => ({
  isValidInteger: mockIsValidInteger,
}))

const mockIsWebView = vi.fn()
vi.mock('@/contexts/WebViewContext', () => ({
  useWebView: () => ({
    isWebView: mockIsWebView(),
  }),
}))

// Mock CSS import
vi.mock('../HeaderComponent.css', () => ({}))

describe('HeaderComponent', () => {
  const renderHeaderComponent = (search = '?loginId=123') => {
    vi.doMock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom')
      return {
        ...actual,
        useNavigate: () => mockNavigate,
        useLocation: () => ({
          pathname: '/test',
          search,
          hash: '',
          state: null,
          key: 'test',
        }),
      }
    })

    return render(
      <MemoryRouter>
        <HeaderComponent />
      </MemoryRouter>,
    )
  }

  beforeEach(() => {
    void vi.clearAllMocks()
    mockIsWebView.mockReturnValue(false) // Default to not WebView
  })

  afterEach(() => {
    void vi.restoreAllMocks()
  })

  describe('Rendering in Web Browser', () => {
    it('should render header component when not in WebView', () => {
      void mockIsWebView.mockReturnValue(false)

      renderHeaderComponent()

      void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
    })

    it('should render header container with correct structure', () => {
      void mockIsWebView.mockReturnValue(false)

      renderHeaderComponent()

      const headerContainer = window.document.querySelector('.header-container')
      void expect(headerContainer).toBeInTheDocument()

      const headerAppbar = window.document.querySelector('.header-appbar')
      void expect(headerAppbar).toBeInTheDocument()
    })

    it('should render navigation buttons component', () => {
      void mockIsWebView.mockReturnValue(false)

      renderHeaderComponent()

      void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
      expect(screen.getByText('Navigation Buttons')).toBeInTheDocument()
    })

    it('should apply correct CSS classes', () => {
      void mockIsWebView.mockReturnValue(false)

      renderHeaderComponent()

      const headerContainer = window.document.querySelector('.header-container')
      void expect(headerContainer).toHaveClass('header-container')

      const headerAppbar = window.document.querySelector('.header-appbar')
      void expect(headerAppbar).toHaveClass('header-appbar')
    })

    describe('WebView Behavior', () => {
      it('should not render anything when in WebView', () => {
        void mockIsWebView.mockReturnValue(true)

        const { } = renderHeaderComponent()

        void expect(container.firstChild).toBeNull()
        void expect(screen.queryByTestId('navigation-buttons')).not.toBeInTheDocument()
      })

      it('should return null for WebView', () => {
        void mockIsWebView.mockReturnValue(true)

        const { } = renderHeaderComponent()

        void expect(container.innerHTML).toBe('')
      })

      it('should toggle visibility based on WebView context', () => {
        // Initially not WebView
        void mockIsWebView.mockReturnValue(false)
        const { rerender } = renderHeaderComponent()

        void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()

        // Switch to WebView
        void mockIsWebView.mockReturnValue(true)
        rerender(
          <MemoryRouter>
            <HeaderComponent />
          </MemoryRouter>,
        )

        void expect(screen.queryByTestId('navigation-buttons')).not.toBeInTheDocument()
      })

      describe('URL Parameter Handling', () => {
        it('should handle loginId parameter in URL', () => {
          void mockIsWebView.mockReturnValue(false)
          void mockIsValidInteger.mockReturnValue(true)

          renderHeaderComponent('?loginId=123')

          void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
        })

        it('should handle missing loginId parameter', () => {
          void mockIsWebView.mockReturnValue(false)

          renderHeaderComponent('')

          void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
        })

        it('should handle invalid loginId parameter', () => {
          void mockIsWebView.mockReturnValue(false)
          void mockIsValidInteger.mockReturnValue(false)

          renderHeaderComponent('?loginId=invalid')

          void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
        })

        it('should handle multiple URL parameters', () => {
          void mockIsWebView.mockReturnValue(false)

          renderHeaderComponent('?loginId=123&other=value&theme=dark')

          void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
        })

        it('should handle empty search params', () => {
          void mockIsWebView.mockReturnValue(false)

          renderHeaderComponent('')

          void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
        })

        describe('Navigation Integration', () => {
          it('should integrate with React Router', () => {
            void mockIsWebView.mockReturnValue(false)

            renderHeaderComponent()

            // Component should render without router-related errors
            void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
          })

          it('should handle navigation state changes', () => {
            void mockIsWebView.mockReturnValue(false)

            const { rerender } = renderHeaderComponent('?loginId=123')

            void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()

            // Simulate route change
            rerender(
              <MemoryRouter>
                <HeaderComponent />
              </MemoryRouter>,
            )

            void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
          })

          describe('Hook Integration', () => {
            it('should use useUpdateEffect hook', () => {
              void mockIsWebView.mockReturnValue(false)

              renderHeaderComponent()

              void expect(mockUseUpdateEffect).toHaveBeenCalled()
            })

            it('should use useWebView hook', () => {
              renderHeaderComponent()

              // Hook should be called during render
              void expect(mockIsWebView).toHaveBeenCalled()
            })

            it('should use React Router hooks', () => {
              void mockIsWebView.mockReturnValue(false)

              renderHeaderComponent()

              // Should render without router hook errors
              void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
            })

            describe('Performance', () => {
              it('should render quickly without performance issues', () => {
                void mockIsWebView.mockReturnValue(false)

                const startTime = performance.now()
                renderHeaderComponent()
                const endTime = performance.now()

                const renderTime = endTime - startTime
                void expect(renderTime).toBeLessThan(50)
              })

              it('should handle multiple re-renders efficiently', () => {
                void mockIsWebView.mockReturnValue(false)

                const { rerender } = renderHeaderComponent()

                // Multiple rerenders should not cause issues
                for (let i = 0; i < 10; i++) {
                  rerender(
                    <MemoryRouter>
                      <HeaderComponent />
                    </MemoryRouter>,
                  )
                }

                void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
              })

              it('should not cause memory leaks on unmount', () => {
                void mockIsWebView.mockReturnValue(false)

                const { unmount } = renderHeaderComponent()

                expect(() => unmount()).not.toThrow()
              })

              describe('Context Dependencies', () => {
                it('should handle missing WebView context gracefully', () => {
                  // Mock missing context
                  vi.doMock('@/contexts/WebViewContext', () => ({
                    useWebView: () => ({
                      isWebView: undefined,
                    }),
                  }))

                  expect(() => renderHeaderComponent()).not.toThrow()
                })

                it('should handle WebView context changes', () => {
                  // Test context value changes
                  void mockIsWebView.mockReturnValue(false)
                  const { rerender } = renderHeaderComponent()

                  void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()

                  void mockIsWebView.mockReturnValue(true)
                  rerender(
                    <MemoryRouter>
                      <HeaderComponent />
                    </MemoryRouter>,
                  )

                  void expect(screen.queryByTestId('navigation-buttons')).not.toBeInTheDocument()
                })

                describe('Component Structure', () => {
                  it('should have proper DOM hierarchy', () => {
                    void mockIsWebView.mockReturnValue(false)

                    renderHeaderComponent()

                    const headerContainer = window.document.querySelector('.header-container')
                    const headerAppbar = window.document.querySelector('.header-appbar')
                    const navigationButtons = screen.getByTestId('navigation-buttons')

                    void expect(headerContainer).toContainElement(headerAppbar)
                    void expect(headerAppbar).toContainElement(navigationButtons)
                  })

                  it('should maintain component structure across renders', () => {
                    void mockIsWebView.mockReturnValue(false)

                    const { rerender } = renderHeaderComponent()

                    const initialContainer = window.document.querySelector('.header-container')

                    rerender(
                      <MemoryRouter>
                        <HeaderComponent />
                      </MemoryRouter>,
                    )

                    const rerenderedContainer = window.document.querySelector('.header-container')
                    void expect(rerenderedContainer).toBeInTheDocument()
                    void expect(rerenderedContainer).toHaveClass('header-container')
                  })

                  describe('Error Handling', () => {
                    it('should handle navigation errors gracefully', () => {
                      void mockIsWebView.mockReturnValue(false)
                      mockNavigate.mockImplementation(() => {
                        throw new Error('Navigation failed')
                      })

                      expect(() => renderHeaderComponent()).not.toThrow()
                    })

                    it('should handle malformed URL parameters', () => {
                      void mockIsWebView.mockReturnValue(false)

                      renderHeaderComponent('?loginId=%invalid%&malformed=%%')

                      void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
                    })

                    it('should handle missing required hooks gracefully', () => {
                      void mockIsWebView.mockReturnValue(false)

                      // Component should render even if some hooks have issues
                      expect(() => renderHeaderComponent()).not.toThrow()
                    })

                    describe('Real-world Usage Scenarios', () => {
                      it('should work in desktop browser environment', () => {
                        void mockIsWebView.mockReturnValue(false)

                        renderHeaderComponent('?loginId=123&source=desktop')

                        void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
                        void expect(window.document.querySelector('.header-container')).toBeInTheDocument()
                      })

                      it('should work in mobile browser environment', () => {
                        void mockIsWebView.mockReturnValue(false)

                        renderHeaderComponent('?loginId=456&source=mobile')

                        void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
                      })

                      it('should hide in mobile app WebView', () => {
                        void mockIsWebView.mockReturnValue(true)

                        const { } = renderHeaderComponent('?loginId=789&source=app')

                        void expect(container.firstChild).toBeNull()
                      })

                      it('should handle admin user scenarios', () => {
                        void mockIsWebView.mockReturnValue(false)
                        void mockIsValidInteger.mockReturnValue(true)

                        renderHeaderComponent('?loginId=1&admin=true')

                        void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
                      })

                      it('should handle guest/unauthenticated scenarios', () => {
                        void mockIsWebView.mockReturnValue(false)

                        renderHeaderComponent('')

                        void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
                      })

                      describe('Accessibility', () => {
                        it('should provide accessible navigation structure', () => {
                          void mockIsWebView.mockReturnValue(false)

                          renderHeaderComponent()

                          const headerContainer = window.document.querySelector('.header-container')
                          void expect(headerContainer).toBeInTheDocument()
                        })

                        it('should not interfere with keyboard navigation', () => {
                          void mockIsWebView.mockReturnValue(false)

                          renderHeaderComponent()

                          const navigationButtons = screen.getByTestId('navigation-buttons')
                          void expect(navigationButtons).toBeInTheDocument()
                        })

                        it('should maintain focus management', () => {
                          void mockIsWebView.mockReturnValue(false)

                          renderHeaderComponent()

                          // Component should not trap focus
                          void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
                        })

                        describe('Integration with Other Components', () => {
                          it('should properly integrate NavigationButtons component', () => {
                            void mockIsWebView.mockReturnValue(false)

                            renderHeaderComponent()

                            void expect(screen.getByTestId('navigation-buttons')).toBeInTheDocument()
                            expect(screen.getByText('Navigation Buttons')).toBeInTheDocument()
                          })

                          it('should handle NavigationButtons component errors', () => {
                            void mockIsWebView.mockReturnValue(false)

                            // Mock NavigationButtons to throw an error
                            vi.doMock('../NavigateButtons', () => ({
                              default: () => {
                                throw new Error('NavigationButtons failed')
                              },
                            }))

                            // Component should handle child component errors gracefully
                            expect(() => renderHeaderComponent()).not.toThrow()
                          })

                          describe('CSS and Styling', () => {
                            it('should load CSS file without errors', () => {
                              void mockIsWebView.mockReturnValue(false)

                              expect(() => renderHeaderComponent()).not.toThrow()
                            })

                            it('should apply correct CSS classes consistently', () => {
                              void mockIsWebView.mockReturnValue(false)

                              const { rerender } = renderHeaderComponent()

                              let headerContainer = window.document.querySelector('.header-container')
                              void expect(headerContainer).toHaveClass('header-container')

                              rerender(
                                <MemoryRouter>
                                  <HeaderComponent />
                                </MemoryRouter>,
                              )

                              headerContainer = window.document.querySelector('.header-container')
                              void expect(headerContainer).toHaveClass('header-container')
                            })
                          })
                        })
