import { W<PERSON><PERSON>, W<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WBox } from 'wface'
import { useTranslation } from 'react-i18next'
import useMediaQuery from '@/hooks/useMediaQuery'
import { useState, useEffect, useCallback } from 'react'
import useAppHelper from '@/services/wface/appHelper'
import { ChevronRight, Menu, X } from 'lucide-react'

// Define styles as objects instead of using makeStyles
const styles = {
  buttonGrid: {
    display: 'grid',
    gap: '8px',
    gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
    padding: '8px',
    width: '100%',
    alignItems: 'start',
  },
  button: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: '6px 12px',
    fontSize: '13px',
    width: '100%',
    minHeight: '32px',
    cursor: 'pointer',
    textTransform: 'none' as const,
    borderRadius: '4px',
    transition: 'background-color 0.2s',
    color: 'white',
    backgroundColor: 'transparent',
    border: 'none',
    whiteSpace: 'nowrap',
  },
  buttonHover: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  icon: {
    marginRight: 8,
    color: 'white',
    fontSize: '16px',
  },
  hamburgerButton: {
    top: 0,
    right: 0,
    width: '100%',
    color: 'white',
    minWidth: 'unset',
    padding: '8px',
    borderRadius: '8px',
    background: 'linear-gradient(135deg, #01248a 0%, #662E85 100%)',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    zIndex: 1000,
    border: 'none',
    cursor: 'pointer',
  },
  container: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    justifyContent: 'flex-start',
    overflow: 'hidden',
    zIndex: 999999,
  },
  modalContent: {
    background: 'linear-gradient(135deg, #01248a 0%, #662E85 100%)',
    width: '100%',
    height: '100%',
    paddingTop: '55px',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    position: 'relative' as const,
    overflowY: 'auto' as const,
  },
  closeButton: {
    color: 'white',
    margin: '0 auto',
    top: '25px',
    padding: '4px',
    minWidth: 'unset',
    borderRadius: '50%',
    background: 'rgba(255, 255, 255, 0.1)',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    border: 'none',
    cursor: 'pointer',
  },
  mobileNavContainer: {
    width: '100%',
    maxWidth: '250px',
    marginRight: '32px',
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '12px',
    marginTop: '30px',
    padding: '0 16px',
  },
  mobileLink: {
    display: 'flex',
    alignItems: 'center',
    padding: '16px',
    fontSize: '15px',
    color: 'rgba(255, 255, 255, 0.9)',
    width: '100%',
    textTransform: 'none' as const,
    borderRadius: '12px',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(8px)',
    transition: 'all 0.3s ease',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    cursor: 'pointer',
  },
  mobileIcon: {
    marginRight: 12,
    color: 'white',
    fontSize: '18px',
    flexShrink: 0,
  },
  modalOverlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    backdropFilter: 'blur(4px)',
  },
}

const buttonItems = [
  { id: 'inbox', key: 'inbox' },
  { id: 'history', key: 'history' },
  { id: 'suspended-inbox', key: 'suspended_inbox' },
  { id: 'workflow-management', key: 'workflow_management' },
  { id: 'workflow', key: 'request_delegation', params: new URLSearchParams({ name: 'delegation' }) },
  { id: 'end-delegation', key: 'end_delegation' },
  { id: 'workflow', key: 'request_monitoring', params: new URLSearchParams({ name: 'monitoring' }) },
  { id: 'end-monitoring', key: 'end_monitoring' },
]

const NavigationButtons = () => {
  const { t } = useTranslation('common')
  const { openScreen } = useAppHelper()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isMobileView, setIsMobileView] = useState(false)

  const isLargeScreen = useMediaQuery('(min-width: 1400px)')
  const isMediumScreen = useMediaQuery('(min-width: 1300px)')
  const isSmallScreen = useMediaQuery('(min-width: 1113px)')
  const isXSmallScreen = useMediaQuery('(min-width: 902px)')
  const isMobile = useMediaQuery('(max-width: 901px)')

  // Wrap openScreen in useCallback to prevent re-renders
  const handleOpenScreen = useCallback(
    (id: string, params?: URLSearchParams) => {
      openScreen(id, params)
    },
    [openScreen],
  )

  useEffect(() => {
    setIsMobileView(isMobile)
    setIsLoading(false)
  }, [isMobile])

  const handleOpenModal = () => setIsModalOpen(true)
  const handleCloseModal = () => setIsModalOpen(false)

  const getGridColumns = () => {
    if (isLargeScreen) return 'repeat(8, 1fr)'
    if (isMediumScreen) return 'repeat(4, 1fr)'
    if (isSmallScreen) return 'repeat(4, 1fr)'
    if (isXSmallScreen) return 'repeat(2, 1fr)'
    return '1fr'
  }

  const DesktopNavigation = () => (
    <WGrid
      item
      xs={12}
      style={{
        ...styles.buttonGrid,
        gridTemplateColumns: getGridColumns(),
        minHeight: 'auto',
        height: 'auto',
      }}
    >
      {buttonItems.map((item: any, index) => (
        <WBox
          key={index}
          color="inherit"
          onClick={() => handleOpenScreen(item.id, item.params)}
          style={styles.button}
          fontWeight="bold"
          onMouseEnter={(e: any) => {
            e.currentTarget.style.backgroundColor = styles.buttonHover.backgroundColor
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.backgroundColor = 'transparent'
          }}
        >
          <ChevronRight style={styles.icon} size={16} />
          <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>{t(item.key)}</span>
        </WBox>
      ))}
    </WGrid>
  )

  const MobileNavigation = () => (
    <>
      <WButton style={styles.hamburgerButton} onClick={handleOpenModal}>
        <Menu style={{ color: 'white' }} size={20} />
      </WButton>
      <WDialog
        open={isModalOpen}
        onClose={handleCloseModal}
        fullScreen
        sx={{
          ...styles.modalOverlay,
          '& .MuiDialog-root': {
            zIndex: 999999999,
          },
        }}
      >
        <div style={styles.modalContent}>
          <WButton style={{ top: '3px' }} onClick={handleCloseModal}>
            <X style={styles.closeButton} size={20} />
          </WButton>
          <div style={styles.mobileNavContainer}>
            {buttonItems.map((item, index) => (
              <WBox
                key={index}
                onClick={() = key = { Math.random() } > {
                  handleOpenScreen(item.id, item.params)
                  handleCloseModal()
                }}
            style={styles.mobileLink}
            color="inherit"
            onMouseEnter={(e: any) => {
              const e = undefined.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.15)'
              const e = undefined.currentTarget.style.transform = 'translateX(8px)'
              const e = undefined.currentTarget.style.border = '1px solid rgba(255, 255, 255, 0.2)'
            }}
            onMouseLeave={(e: any) => {
              const e = undefined.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
              const e = undefined.currentTarget.style.transform = 'translateX(0px)'
              const e = undefined.currentTarget.style.border = '1px solid rgba(255, 255, 255, 0.1)'
            }}
              >
            <ChevronRight style={styles.mobileIcon} size={18} />
            <span
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                fontWeight: 'bold',
              }}
            >
              {t(item.key)}
            </span>
          </WBox>
            ))}
        </div>
      </WDialog>
    </>
  )

  if (isLoading) {
    return null
  }

  return isMobileView ? <MobileNavigation /> : <DesktopNavigation />
}

export default NavigationButtons
