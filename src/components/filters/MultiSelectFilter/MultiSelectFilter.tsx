import React from 'react'
import { DigiTextField } from '@/components/formElements/DigiTextField/DigiTextField'
import { SelectBox } from '@/components/formElements'
import { WBox } from 'wface'
import { useTranslation } from 'react-i18next'
import { FilterComponentProps, FilterValueRange, IOption } from '@/types'

export const TextFilter: React.FC<FilterComponentProps> = ({ value, onChange }) => (
  <DigiTextField fullWidth value={value ?? ''} onChange={(e) => onChange(e)} label={''} />
)

export const NumberFilter: React.FC<FilterComponentProps> = ({ value, onChange }) => (
  <DigiTextField fullWidth type="number" value={value ?? ''} onChange={(e) => onChange(e)} label={''} />
)

export const DateFilter: React.FC<FilterComponentProps> = ({ value, onChange }) => (
  <DigiTextField fullWidth type="date" value={value ?? ''} onChange={(e) => onChange(e)} label={''} />
)

export const DateRangeFilter: React.FC<FilterComponentProps> = ({ value, onChange }) => {
  const { t } = useTranslation()
  const range = (value as FilterValueRange) || { start: '', end: '' }

  return (
    <WBox display="flex" flexDirection="column" gap={2}>
      <DigiTextField fullWidth type="date" label={t('startDate')} value={range.start} onChange={(e) => onChange({ ...range, start: e })} />
      <DigiTextField fullWidth type="date" label={t('endDate')} value={range.end} onChange={(e) => onChange({ ...range, end: e })} />
    </WBox>
  )
}

export const MultiSelectFilter: React.FC<FilterComponentProps> = ({ columnConfig, value, onChange }) => {
  const options: IOption[] = columnConfig.uniqueValues.map((val) => ({
    value: val,
    label: String(val),
    labelEn: String(val),
  }))

  return <SelectBox label="" value={value ?? []} options={options} onChange={(selected) => onChange(selected)} multiple fullWidth />
}
