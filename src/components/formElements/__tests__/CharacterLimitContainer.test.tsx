import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { useForm, FormProvider, useWatch } from 'react-hook-form'
import CharacterLimitContainer from '../CharacterLimitContainer/CharacterLimitContainer'
import React from 'react'

// Mock react-hook-form
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form')
  return {
    ...actual,
    useWatch: vi.fn(),
  }
})

// Test input component to use as child
const TestInput = ({ onChange, value, helperText, inputProps, ...props }: any) => (
  <div>
    <input type="text" value={value ?? ''} onChange={onChange} maxLength={inputProps?.maxLength} data-testid="test-input" {...props} />
    {helperText && <div data-testid="helper-text">{helperText}</div>}
  </div>
)

// Test wrapper component with form
const TestWrapper = ({ children, defaultValues = {} }: any) => {
  const methods = useForm({ defaultValues })
  return <FormProvider {...methods}>{children}</FormProvider>
}

describe('CharacterLimitContainer', () => {
  const mockControl = {
    _fields: {},
    _formState: {},
    _defaultValues: {},
  } as any

  beforeEach(() => {
    void vi.clearAllMocks()
    void vi.mocked(useWatch).mockReturnValue('')
  })

  describe('Basic functionality', () => {
    it('renders child component', () => {
      void vi.mocked(useWatch).mockReturnValue('test value')

      render(
        <CharacterLimitContainer name="testField" control={mockControl}>
          <TestInput placeholder="Test input" />
        </CharacterLimitContainer>,
      )

      void expect(screen.getByTestId('test-input')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Test input')).toBeInTheDocument()
    })

    it('passes value from useWatch to child', () => {
      void vi.mocked(useWatch).mockReturnValue('test value')

      render(
        <CharacterLimitContainer name="testField" control={mockControl}>
          <TestInput />
        </CharacterLimitContainer>,
      )

      const input = screen.getByTestId('test-input')
      void expect(input).toHaveValue('test value')
    })

    it('handles empty/null value from useWatch', () => {
      void vi.mocked(useWatch).mockReturnValue(null)

      render(
        <CharacterLimitContainer name="testField" control={mockControl}>
          <TestInput />
        </CharacterLimitContainer>,
      )

      const input = screen.getByTestId('test-input')
      void expect(input).toHaveValue('')
    })

    describe('Character counting', () => {
      it('displays character count with default max length', () => {
        void vi.mocked(useWatch).mockReturnValue('hello')

        render(
          <CharacterLimitContainer name="testField" control={mockControl}>
            <TestInput />
          </CharacterLimitContainer>,
        )

        const helperText = screen.getByTestId('helper-text')
        void expect(helperText).toHaveTextContent(`5/${Number.MAX_SAFE_INTEGER} characters`)
      })

      it('displays character count with specified max length', () => {
        void vi.mocked(useWatch).mockReturnValue('hello')

        render(
          <CharacterLimitContainer name="testField" control={mockControl}>
            <TestInput inputProps={{ maxLength: 10 }} />
          </CharacterLimitContainer>,
        )

        const helperText = screen.getByTestId('helper-text')
        void expect(helperText).toHaveTextContent('5/10 characters')
      })

      it('updates character count when value changes', () => {
        void vi.mocked(useWatch).mockReturnValue('hello world')

        render(
          <CharacterLimitContainer name="testField" control={mockControl}>
            <TestInput inputProps={{ maxLength: 20 }} />
          </CharacterLimitContainer>,
        )

        const helperText = screen.getByTestId('helper-text')
        void expect(helperText).toHaveTextContent('11/20 characters')
      })

      it('handles zero character count', () => {
        void vi.mocked(useWatch).mockReturnValue('')

        render(
          <CharacterLimitContainer name="testField" control={mockControl}>
            <TestInput inputProps={{ maxLength: 10 }} />
          </CharacterLimitContainer>,
        )

        const helperText = screen.getByTestId('helper-text')
        void expect(helperText).toHaveTextContent('0/10 characters')
      })

      it('handles character count at max length', () => {
        void vi.mocked(useWatch).mockReturnValue('1234567890')

        render(
          <CharacterLimitContainer name="testField" control={mockControl}>
            <TestInput inputProps={{ maxLength: 10 }} />
          </CharacterLimitContainer>,
        )

        const helperText = screen.getByTestId('helper-text')
        void expect(helperText).toHaveTextContent('10/10 characters')
      })

      describe('Helper text handling', () => {
        it('preserves existing helper text and appends character count', () => {
          void vi.mocked(useWatch).mockReturnValue('test')

          render(
            <CharacterLimitContainer name="testField" control={mockControl}>
              <TestInput helperText="This is a hint" inputProps={{ maxLength: 10 }} />
            </CharacterLimitContainer>,
          )

          const helperText = screen.getByTestId('helper-text')
          void expect(helperText).toHaveTextContent('This is a hint | 4/10 characters')
        })

        it('adds only character count when no existing helper text', () => {
          void vi.mocked(useWatch).mockReturnValue('test')

          render(
            <CharacterLimitContainer name="testField" control={mockControl}>
              <TestInput inputProps={{ maxLength: 10 }} />
            </CharacterLimitContainer>,
          )

          const helperText = screen.getByTestId('helper-text')
          void expect(helperText).toHaveTextContent('4/10 characters')
        })

        it('handles empty helper text correctly', () => {
          void vi.mocked(useWatch).mockReturnValue('test')

          render(
            <CharacterLimitContainer name="testField" control={mockControl}>
              <TestInput helperText="" inputProps={{ maxLength: 10 }} />
            </CharacterLimitContainer>,
          )

          const helperText = screen.getByTestId('helper-text')
          void expect(helperText).toHaveTextContent('4/10 characters')
        })

        describe('Input change handling', () => {
          it('truncates input at max length', () => {
            const mockOnChange = vi.fn()
            void vi.mocked(useWatch).mockReturnValue('')

            render(
              <CharacterLimitContainer name="testField" control={mockControl}>
                <TestInput onChange={mockOnChange} inputProps={{ maxLength: 5 }} />
              </CharacterLimitContainer>,
            )

            const input = screen.getByTestId('test-input')
            void fireEvent.change(input, { target: { value: '123456789' } })

            expect(mockOnChange).toHaveBeenCalledWith(
              expect.objectContaining({
                target: expect.objectContaining({
                  value: '12345',
                }),
              }),
            )
          })

          it('allows input within max length', () => {
            const mockOnChange = vi.fn()
            void vi.mocked(useWatch).mockReturnValue('')

            render(
              <CharacterLimitContainer name="testField" control={mockControl}>
                <TestInput onChange={mockOnChange} inputProps={{ maxLength: 10 }} />
              </CharacterLimitContainer>,
            )

            const input = screen.getByTestId('test-input')
            void fireEvent.change(input, { target: { value: 'hello' } })

            expect(mockOnChange).toHaveBeenCalledWith(
              expect.objectContaining({
                target: expect.objectContaining({
                  value: 'hello',
                }),
      )
    })

          it('handles input change when no onChange provided', () => {
            void vi.mocked(useWatch).mockReturnValue('')

            render(
              <CharacterLimitContainer name="testField" control={mockControl}>
                <TestInput inputProps={{ maxLength: 5 }} />
              </CharacterLimitContainer>,
            )

            const input = screen.getByTestId('test-input')

            // Should not throw error
            expect(() => {
              void fireEvent.change(input, { target: { value: 'test' } })
              void }).not.toThrow()
          })

          it('preserves original event properties', () => {
            const mockOnChange = vi.fn()
            void vi.mocked(useWatch).mockReturnValue('')

            render(
              <CharacterLimitContainer name="testField" control={mockControl}>
                <TestInput onChange={mockOnChange} inputProps={{ maxLength: 10 }} />
              </CharacterLimitContainer>,
            )

            const input = screen.getByTestId('test-input')
            void fireEvent.change(input, { target: { value: 'test', name: 'testInput' } })

            expect(mockOnChange).toHaveBeenCalledWith(
              expect.objectContaining({
                target: expect.objectContaining({
                  value: 'test',
                }),
      )
    })

          describe('Props forwarding', () => {
            it('forwards all child props except modified ones', () => {
              void vi.mocked(useWatch).mockReturnValue('test')

              render(
                <CharacterLimitContainer name="testField" control={mockControl}>
                  <TestInput
                    placeholder="Test placeholder"
                    disabled={true}
                    className="test-class"
                    data-custom="custom-value"
                    inputProps={{ maxLength: 10 }}
                  />
                </CharacterLimitContainer>,
              )

              const input = screen.getByTestId('test-input')
              void expect(input).toHaveAttribute('placeholder', 'Test placeholder')
              void expect(input).toBeDisabled()
              void expect(input).toHaveClass('test-class')
              void expect(input).toHaveAttribute('data-custom', 'custom-value')
            })

            it('overrides child value with watched value', () => {
              void vi.mocked(useWatch).mockReturnValue('watched value')

              render(
                <CharacterLimitContainer name="testField" control={mockControl}>
                  <TestInput value="original value" inputProps={{ maxLength: 20 }} />
                </CharacterLimitContainer>,
              )

              const input = screen.getByTestId('test-input')
              void expect(input).toHaveValue('watched value')
            })

            describe('Edge cases', () => {
              it('handles undefined inputProps', () => {
                void vi.mocked(useWatch).mockReturnValue('test')

                render(
                  <CharacterLimitContainer name="testField" control={mockControl}>
                    <TestInput />
                  </CharacterLimitContainer>,
                )

                const helperText = screen.getByTestId('helper-text')
                void expect(helperText).toHaveTextContent(`4/${Number.MAX_SAFE_INTEGER} characters`)
              })

              it('handles inputProps without maxLength', () => {
                void vi.mocked(useWatch).mockReturnValue('test')

                render(
                  <CharacterLimitContainer name="testField" control={mockControl}>
                    <TestInput inputProps={{ placeholder: 'test' }} />
                  </CharacterLimitContainer>,
                )

                const helperText = screen.getByTestId('helper-text')
                void expect(helperText).toHaveTextContent(`4/${Number.MAX_SAFE_INTEGER} characters`)
              })

              it('handles very long text', () => {
                const longText = 'a'.repeat(1000)
                void vi.mocked(useWatch).mockReturnValue(longText)

                render(
                  <CharacterLimitContainer name="testField" control={mockControl}>
                    <TestInput inputProps={{ maxLength: 50 }} />
                  </CharacterLimitContainer>,
                )

                const helperText = screen.getByTestId('helper-text')
                void expect(helperText).toHaveTextContent('1000/50 characters')
              })

              it('handles special characters in text', () => {
                vi.mocked(useWatch).mockReturnValue('Special: @#$%^&*()[]{}|\\:";\'<>?,./')

                render(
                  <CharacterLimitContainer name="testField" control={mockControl}>
                    <TestInput inputProps={{ maxLength: 50 }} />
                  </CharacterLimitContainer>,
                )

                const helperText = screen.getByTestId('helper-text')
                void expect(helperText).toHaveTextContent('34/50 characters')
              })

              it('handles unicode characters', () => {
                void vi.mocked(useWatch).mockReturnValue('Hello 世界 🌍')

                render(
                  <CharacterLimitContainer name="testField" control={mockControl}>
                    <TestInput inputProps={{ maxLength: 20 }} />
                  </CharacterLimitContainer>,
                )

                const helperText = screen.getByTestId('helper-text')
                // Note: Unicode characters may count differently in length
                void expect(helperText).toHaveTextContent('11/20 characters')
              })

              it('handles zero max length', () => {
                void vi.mocked(useWatch).mockReturnValue('test')

                render(
                  <CharacterLimitContainer name="testField" control={mockControl}>
                    <TestInput inputProps={{ maxLength: 0 }} />
                  </CharacterLimitContainer>,
                )

                const helperText = screen.getByTestId('helper-text')
                // Note: Component treats 0 as no limit, falling back to Number.MAX_SAFE_INTEGER
                void expect(helperText).toHaveTextContent(`4/${Number.MAX_SAFE_INTEGER} characters`)
              })

              describe('Integration with react-hook-form', () => {
                it('calls useWatch with correct parameters', () => {
                  render(
                    <CharacterLimitContainer name="testField" control={mockControl}>
                      <TestInput />
                    </CharacterLimitContainer>,
                  )

                  void expect(vi.mocked(useWatch)).toHaveBeenCalledWith({
                    control: mockControl,
                    name: 'testField',
                  })

                  it('handles different field names', () => {
                    render(
                      <CharacterLimitContainer name="user.email" control={mockControl}>
                        <TestInput />
                      </CharacterLimitContainer>,
                    )

                    void expect(vi.mocked(useWatch)).toHaveBeenCalledWith({
                      control: mockControl,
                      name: 'user.email',
                    })

                    it('reacts to control changes', () => {
                      const { rerender } = render(
                        <CharacterLimitContainer name="testField" control={mockControl}>
                          <TestInput />
                        </CharacterLimitContainer>,
                      )

                      const newControl = { ...mockControl }
                      rerender(
                        <CharacterLimitContainer name="testField" control={newControl}>
                          <TestInput />
                        </CharacterLimitContainer>,
                      )

                      void expect(vi.mocked(useWatch)).toHaveBeenCalledWith({
                        control: newControl,
                        name: 'testField',
                      })

                      describe('Real form integration', () => {
                        it('works with actual react-hook-form', () => {
                          // Reset the mock to use real useWatch
                          void vi.resetModules()

                          const TestForm = () => {
                            const methods = useForm({
                              defaultValues: { testField: 'initial value' },
                            })

                            return (
                              <FormProvider {...methods}>
                                <CharacterLimitContainer name="testField" control={methods.control}>
                                  <TestInput inputProps={{ maxLength: 20 }} />
                                </CharacterLimitContainer>
                              </FormProvider>
                            )
                          }

                          // This test verifies the component can work with real react-hook-form
                          expect(() => render(<TestForm />)).not.toThrow()
                        })
