import React, { ReactElement, cloneElement } from 'react'
import { Control, useWatch } from 'react-hook-form'

interface CharacterLimitContainerProps {
  name: string
  control: Control<Record<string, unknown>>
  children: ReactElement<Record<string, unknown>>
}

interface ChildProps {
  inputProps?: { maxLength?: number }
  onChange?: (_e: React.ChangeEvent<HTMLInputElement>) => void
  helperText?: string
  value?: string
  [key: string]: unknown
}

const CharacterLimitContainer: React.FC<CharacterLimitContainerProps> = ({ name, control, children }) => {
  const value = (useWatch({ control, name }) as string) ?? ''
  const childProps = children.props as ChildProps
  const maxLength = childProps.inputProps?.maxLength ?? Number.MAX_SAFE_INTEGER
  const characterCount = value.length

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.slice(0, maxLength)
    if (childProps.onChange) {
      const syntheticEvent = {
        ...e,
        target: { ...e.target, value: newValue },
      }
      childProps.onChange(syntheticEvent)
    }
  }

  const modifiedHelperText = (childProps.helperText ? `${childProps.helperText} | ` : '') + `${characterCount}/${maxLength} characters`

  return cloneElement(children, {
    ...childProps,
    onChange: handleChange,
    helperText: modifiedHelperText,
    value: value,
  } as ChildProps)
}

export default CharacterLimitContainer
