import React from 'react'
import { useTranslation } from 'react-i18next'
import { WBox, WList, WListItem, WListItemText, WButton } from 'wface'
import DigiTextField from '../DigiTextField/DigiTextField'
import './PartyListBox.css'

interface PartyListBoxProps {
  parties: string[]
  onRemoveParty: (_party: string) => void
  newParty: string
  label: string
  setNewParty: (_party: string) => void
  onAddParty: () => void
  disabled: boolean
}

const PartyListBox: React.FC<PartyListBoxProps> = ({ label, parties, onRemoveParty, newParty, setNewParty, onAddParty, disabled }) => {
  const { i18n } = useTranslation()

  return (
    <WBox className={`party-list-box-container ${disabled ? 'Mui-disabled' : ''}`}>
      <DigiTextField
        fullWidth
        variant="outlined"
        label={label}
        value={newParty}
        disabled={disabled}
        onChange={(value: string) => setNewParty(value)}
        className="party-list-box-textfield"
      />
      <WButton size="small" variant="contained" color="primary" onClick={onAddParty} disabled={disabled} className="party-list-box-add-button">
        {i18n.language === 'tr' ? 'Taraf Ekle' : 'Add Party'}
      </WButton>

      {parties.length > 0 && (
        <WList className={`party-list-box-list ${disabled ? 'Mui-disabled' : ''}`}>
          {parties.map((party, index) => (
            <WListItem key={`party-${index}-${party}`} className={`party-list-box-list-item ${disabled ? 'Mui-disabled' : ''}`}>
              <WListItemText primary={party} className="party-list-box-list-item-text" />
              <WButton
                focusRipple={false}
                size="small"
                disabled={disabled}
                variant="contained"
                color="primary"
                onClick={() => onRemoveParty(party)}
                className="party-list-box-remove-button"
              >
                {i18n.language === 'tr' ? 'Kaldır' : 'Remove'}
              </WButton>
            </WListItem>
          ))}
        </WList>
      )}
    </WBox>
  )
}

export default PartyListBox
