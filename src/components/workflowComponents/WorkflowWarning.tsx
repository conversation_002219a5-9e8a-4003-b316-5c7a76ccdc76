import React from 'react'
import { WGrid } from 'wface'
import { AlertTriangle } from 'lucide-react'

interface WorkflowWarningProps {
  title: string
  message: string
  icon?: React.ReactNode
  style?: React.CSSProperties
}

/**
 * Reusable warning component for workflow screens
 */
export const WorkflowWarning: React.FC<WorkflowWarningProps> = ({
  title,
  message,
  icon = <AlertTriangle size={24} color="#e74c3c" />,
  style = {},
}) => {
  return (
    <WGrid item xs={12}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: '16px',
          backgroundColor: '#fff8f8',
          borderRadius: '8px',
          borderLeft: '4px solid #e74c3c',
          ...style,
        }}
      >
        <div style={{ marginRight: '12px' }}>{icon}</div>
        <div style={{ color: '#e74c3c', fontWeight: '500' }}>
          <strong>{title}!</strong> {message}
        </div>
      </div>
    </WGrid>
  )
}
