import React, { useState, useEffect } from 'react'
import {
  W<PERSON><PERSON>on,
  WTextField,
  WGrid,
  WPaper,
  WTabs,
  WTab,
  WDialog,
  WDialogActions,
  WDialogContent,
  WDialogTitle,
  WCircularProgress,
  WRadioGroup,
  WRadio,
  WLinearProgress,
  WDatePicker,
} from 'wface'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { useWebView } from '@/contexts/WebViewContext'
import { Controller, useFormContext } from 'react-hook-form'
import { useDaemonStatus, useLocalStorage, useOrganizationTreeByUserId, useUpdateEffect } from '@/hooks'
import { useTranslation } from 'react-i18next'
import OrganizationTree from '../OrganizationTree/OrganizationTree'
import { UserActionHistoryActionType } from '@/types/UserActionType'
import toast from 'react-hot-toast'
import { IOrganizationSchemaParams, IUploadedFile } from '@/types'
import * as yup from 'yup'
import dayjs from 'dayjs'
import { FileUpload, SelectBox } from '@/components/formElements'
import { mobileBridge } from '@/utils/mobileBridge'

interface TabContentProps {
  children: React.ReactNode
}

const TabContent: React.FC<TabContentProps> = ({ children }) => <WPaper style={{ padding: 20, marginTop: 20 }}>{children}</WPaper>

interface ActionButtonProps {
  label: string
  onClick: () => void
  disabled: boolean
}

const ActionButton: React.FC<ActionButtonProps> = ({ label, onClick, disabled }) => (
  <WButton type="button" variant="contained" onClick={onClick} disabled={disabled} style={{ minWidth: 120, marginLeft: 10, marginBottom: 10 }}>
    {label}
  </WButton>
)

interface CommentFieldProps {
  placeholder: string
  onChange: (value: string) => void
  error?: string
  value: string
  tabKey: string
}

const CommentField: React.FC<CommentFieldProps> = ({ placeholder, value, onChange, error, tabKey }) => (
  <WTextField
    key={tabKey}
    multiline
    rows={4}
    placeholder={placeholder}
    fullWidth
    value={value}
    onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
    variant="outlined"
    error={!!error}
    helperText={error}
    sx={{ marginBottom: 20, marginTop: 10 }}
  />
)

const ActionPanelComponent = React.memo(() => {
  const { t } = useTranslation(['actionPanel'])
  const { isWebView } = useWebView()
  const [userId] = useLocalStorage<number>('UserId')
  const [documents, setDocuments] = useState<IUploadedFile[]>([])
  const { data: orgDataForward, refetch: refetchForward } = useOrganizationTreeByUserId(userId, true, true)
  const { data: orgDataSendToComment, refetch: refetchSendToComment } = useOrganizationTreeByUserId(userId, true)

  const {
    handleWorkflowAction,
    tabs: tabVisibility,
    actionPermissions,
    schemas,
    initialData,
    wfInstanceId,
    activeAction,
    setActiveAction,
    refetchInitialData,
    isSendBackVisible,
    comments,
    setComments,
    isDaemonWorking,
    setIsDaemonWorking,
    isSuspended,
  } = useWorkflow()
  const forwardUserList = (actionPermissions.ForwardLogicalGroupUserList ?? []) as any
  const commentUserList = (actionPermissions.SendCommentLogicalGroupUserList ?? []) as any
  const isForwardSelectBox = forwardUserList.length > 0
  const isCommentSelectBox = commentUserList.length > 0

  const { data: currentDaemonStatus } = useDaemonStatus({ wfInstanceId, enabled: isDaemonWorking })
  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    trigger,
    setError,
    formState: { errors },
  } = useFormContext()

  const [activeTab, setActiveTab] = useState<number>(0)
  const [openConfirmDialog, setOpenConfirmDialog] = useState<boolean>(false)
  const [confirmAction, setConfirmAction] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)

  useEffect(() => {
    if (userId) {
      refetchForward()
      refetchSendToComment()
    }
  }, [userId])

  const handleChange = (_: React.ChangeEvent<{}>, newValue: number) => {
    const newActiveTab = tabs.filter((_tab) => tab.visible)[newValue].key
    setActiveAction(newActiveTab)
    setActiveTab(newValue)
  }

  const handleCommentChange = (tabKey: string, value: string) => {
    setComments((prevComments: any) => ({ ...prevComments, [tabKey]: value }))
  }

  const validateAction = async (action: string) => {
    try {
      const formData = getValues()
      const activeTabKey = tabs.filter((_tab) => tab.visible)[activeTab].key
      const activeComment = comments[activeTabKey] ?? ''
      // Get the current schema based on state
      const currentSchema = schemas.getSchemaForState(action, initialData?.workflowState)

      try {
        // Validate the form data with context
        await currentSchema.validate(
          {
            ...formData,
            Created: formData.Created || dayjs().format('YYYY-MM-DDTHH:mm:ss'),
            LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
            LastUpdatedBy: parseInt(localStorage?.getItem('UserId')?.toString() ?? '0'),
          },
          {
            context: {
              workflowState: initialData?.workflowState,
              enabilitySettings: initialData?.enabilitySettings,
              action,
            },
            abortEarly: false, // This is important to collect all errors
          },
        )
      } catch (_error) {
        if (_error instanceof yup.ValidationError) {
          // Collect all _error messages
          const errorMessages = _error.inner.map((_err) => err.message)

          // Set form errors
          _error.inner.forEach((_err) => {
            {
              if (_err.path) {
                setError(_err.path, {
                  type: 'validation',
                  message: _err.message,
                })
              }
            })

          // Show all errors in a single toast
          if (errorMessages.length > 0) {
            toast.error(
              <div>
                {errorMessages.map((message, index) => (
                  <div key={index}>{message}</div>
                ))}
              </div>,
              {
                duration: 4000, // Longer duration for multiple errors
                style: {
                  maxWidth: '500px', // Wider toast for better readability
                  whiteSpace: 'pre-wrap', // Preserve line breaks
                },
            )
          }
          return false
        }

        // Additional validations for approve/reject
        if (action === 'approve' || action === 'reject') {
          const isCondValid = await trigger(['conditionalApproval'])
          if (!isCondValid) {
            return false
          }

          // Comment validation
          if (action !== 'create' && (!activeComment || activeComment.trim() === '')) {
            toast.error(t('commentError'))
            return false
          }

          // Check specific action requirements
          switch (action) {
            case 'forward':
              if (!formData.ForwardLoginId) {
                toast.error(t('forwardLoginIdRequired'))
                return false
              }
              break
            case 'sendToComment':
              if (!formData.SendToCommentLoginId) {
                toast.error(t('sendToCommentLoginIdRequired'))
                return false
              }
              break
            case 'suspend':
              if (!formData.suspendUntil) {
                toast.error(t('suspendUntilRequired'))
                return false
              }
              if (new Date(formData.suspendUntil) <= new Date()) {
                toast.error(t('invalidSuspendDate'))
                return false
              }
              break
          }

          // Set action types for approve/reject
          if (action === 'approve') {
            setValue('ActionType', UserActionHistoryActionType.ACCEPTED)
            if (watch('conditionalApproval') === '1') {
              setValue('ActionType', UserActionHistoryActionType.CNDACCCEPT)
            } else if (watch('conditionalApproval') === '2') {
              setValue('ActionType', UserActionHistoryActionType.CORRECTION)
            }
          } else if (action === 'reject') {
            setValue('ActionType', UserActionHistoryActionType.REJECTED)
          }

          return true
        } catch (_error) {
          if (process.env.NODE_ENV === 'development') {
            console._error('Validation _error:', _error)
          }
          toast.error(t('validationFailed'))
          return false
        }

        const showConfirmDialog = (action: string) => {
          setConfirmAction(action)
          setOpenConfirmDialog(true)
        }

        const handleActionClick = async (action: string) => {
          const isValid = await validateAction(action)
          if (isValid) {
            if (action === 'fileUpload') {
              // For file upload, we don't show confirmation dialog
              // The upload process is handled by the FileUpload component
              return
            }
            showConfirmDialog(action)
          }

          const handleFileUploadComplete = async (files: IUploadedFile[]) => {
            const activeTabKey = tabs.filter((_tab) => tab.visible)[activeTab].key
            const comment = comments[activeTabKey] ?? ''
            if (!comment || comment.trim() === '') {
              toast.error(t('commentError'))
              return
            }

            setLoading(true)
            try {
              // First update the documents state with the uploaded files
              setDocuments((prevDocs) => [
                ...prevDocs,
                ...files.map((_file) => ({
                  name: file.name,
                  size: file.size,
                  url: file.url, // Now we have the URL from the file upload
                })),
              ])

              // Then call handleWorkflowAction with the files and comment
              await handleWorkflowAction('fileUpload', {
                files: files.map((_file) => file.url),
                comment,
              })

              if (isWebView) {
                // Notify mobile app about file upload success
                void mobileBridge.sendFileUploadComplete(files.length, comment, wfInstanceId)

                // Show success toast
                toast.success(t('fileUploadSuccess'), {
                  duration: 2000,
                  style: {
                    background: '#4caf50',
                    color: 'white',
                  },
                })
              } else {
                // Regular web success toast
                toast.success(t('fileUploadSuccess'))
              }

              setDocuments([])
              setActiveTab(0)

              // Refresh data after a short delay in WebView mode
              if (isWebView) {
                setTimeout(() => {
                  refetchInitialData()
                }, 500)
              } else {
                refetchInitialData()
              }
            } catch (_error) {
              const errorMessage = (_error as Error).message

              if (isWebView) {
                // Notify mobile app about file upload _error
                mobileBridge.sendWorkflowAction({
                  action: 'fileUpload',
                  status: '_error',
                  message: errorMessage,
                  workflowInstanceId: wfInstanceId,
                  timestamp: new Date().toISOString(),
                })
              }

              toast.error(errorMessage)
              // Remove the files from documents state if workflow action failed
              setDocuments((prevDocs) => prevDocs.filter((_doc) => !files.some((f) => f.name === doc.name)))
            } finally {
              setLoading(false)
            }

            const handleFileDelete = async (fileName: string) => {
              try {
                // Remove from documents
                setDocuments((prevDocs) => prevDocs.filter((_doc) => doc.name !== fileName))
              } catch (_error) {
                toast._error((_error as Error).message)
              }

              const handleConfirmAction = () => {
                setOpenConfirmDialog(false)
                setValue('action', confirmAction)
                handleSubmit(onSubmit)()
              }

              const onSubmit = async (data: unknown) => {
                const action = getValues('action')
                setValue('action', undefined)
                setLoading(true)
                try {
                  const result = await handleWorkflowAction(action, data)

                  if (isWebView) {
                    // Notify mobile app instead of allowing any redirects
                    mobileBridge.sendWorkflowAction({
                      action: action,
                      status: 'success',
                      message: t(`${action}Success`) || t('actionSuccess'),
                      workflowInstanceId: wfInstanceId,
                      result: result,
                      timestamp: new Date().toISOString(),
                    })

                    // Show success toast in web UI
                    toast.success(t(`${action}Success`) || t('actionSuccess'), {
                      duration: 2000,
                      style: {
                        background: '#4caf50',
                        color: 'white',
                      },
                    })

                    // Refresh data without redirecting - delay to ensure mobile app receives message first
                    setTimeout(() => {
                      refetchInitialData()
                    }, 500)
                  } else {
                    // Regular web behavior - allow redirects and normal flow
                    refetchInitialData()
                  }
                } catch (_error) {
                  const errorMessage = (_error as Error).message

                  if (isWebView) {
                    // Notify mobile app about _error
                    mobileBridge.sendWorkflowAction({
                      action: action,
                      status: '_error',
                      message: errorMessage,
                      workflowInstanceId: wfInstanceId,
                      timestamp: new Date().toISOString(),
                    })
                  }

                  toast.error(errorMessage)
                } finally {
                  setLoading(false)
                }

                useUpdateEffect(() => {
                  if (currentDaemonStatus !== undefined) {
                    setIsDaemonWorking(currentDaemonStatus)
                    if (currentDaemonStatus == false) {
                      refetchInitialData()
                    }
                  }, [currentDaemonStatus, isDaemonWorking, setIsDaemonWorking])

                const tabs = [
                  {
                    key: 'create',
                    label: t('createRequest'),
                    visible: tabVisibility.NewRequestTabVisible,
                    content: <ActionButton label={t('create')} onClick={() => handleActionClick('create')} disabled={loading || !actionPermissions.CanCreate} />,
                  },
                  {
                    key: 'approve',
                    label: t('approveReject'),
                    visible: tabVisibility.ApproveRejectTabVisible,
                    content: (
                      <>
                        {(actionPermissions.CanConditionalAccept || actionPermissions.CanCorrection) && (
                          <Controller
                            name="conditionalApproval"
                            control={control}
                            render={({ field }) => (
                              <WRadioGroup label="" defaultValue={UserActionHistoryActionType.ACCEPTED} onChange={field.onChange}>
                                {actionPermissions.CanConditionalAccept && (
                                  <WRadio
                                    value={UserActionHistoryActionType.CNDACCCEPT}
                                    name="approveType"
                                    label={t('conditionalApproval')}
                                    onClick={() => field.onChange(UserActionHistoryActionType.CNDACCCEPT)}
                                  />
                                )}
                                {/* {actionPermissions.CanCorrection && ( */}
                                {actionPermissions.CanConditionalAccept && (
                                  <WRadio
                                    value={UserActionHistoryActionType.CORRECTION}
                                    name="approveType"
                                    label={t('correctionRequest')}
                                    onClick={() => field.onChange(UserActionHistoryActionType.CORRECTION)}
                                  />
                                )}
                                <WRadio
                                  defaultChecked
                                  name="approveType"
                                  value={UserActionHistoryActionType.ACCEPTED}
                                  label={t('approve')}
                                  onClick={() => field.onChange(UserActionHistoryActionType.ACCEPTED)}
                                />
                              </WRadioGroup>
                            )}
                          />
                        )}
                        {actionPermissions.CanSendTask && (
                          <Controller
                            name="sendTaskUserList"
                            control={control}
                            render={({ field }) => (
                              <SelectBox
                                options={
                                  actionPermissions?.SendTaskLogicalGroupItems
                                    ? (actionPermissions?.SendTaskLogicalGroupItems as any[])?.map((user: any) => ({
                                      value: user.LoginId,
                                      label: user.Fullname,
                                      labelEn: user.Fullname,
                                    }))
                                    : []
                                }
                                label={t('assignedPersonel')}
                                value={field.value}
                                onChange={field.onChange}
                              />
                            )}
                          />
                        )}
                        <CommentField
                          value={comments['approve'] ?? ''}
                          placeholder={t('reasonForApprovalRejection')}
                          onChange={(value) => handleCommentChange('approve', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="approve"
                        />
                        <WGrid container justifyContent="flex-end">
                          <ActionButton
                            label={
                              !watch('conditionalApproval') || watch('conditionalApproval') == '3'
                                ? actionPermissions.CanSendTask
                                  ? t('assign')
                                  : t('approve')
                                : t('send')
                            }
                            onClick={() => handleActionClick('approve')}
                            disabled={loading || !actionPermissions.CanApproval}
                          />
                          <ActionButton label={t('reject')} onClick={() => handleActionClick('reject')} disabled={loading || !actionPermissions.CanReject} />
                          {isSendBackVisible && actionPermissions.CanSendBack && (
                            <ActionButton
                              label={t('sendBack')}
                              onClick={() => handleActionClick('send-back')}
                              disabled={loading || !actionPermissions.CanSendBack}
                            />
                          )}
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'forward',
                    label: t('forward'),
                    visible: tabVisibility.ForwardTabVisible,
                    content: (
                      <>
                        <CommentField
                          value={comments['forward'] ?? ''}
                          placeholder={t('forwardReason')}
                          onChange={(value) => handleCommentChange('forward', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="forward"
                        />
                        {isForwardSelectBox ? (
                          <Controller
                            name="ForwardLoginId"
                            control={control}
                            render={({ field }) => (
                              <SelectBox
                                label={t('chooseUser')}
                                value={field.value}
                                onChange={(item: any) => {
                                  void field.onChange(item?.value)
                                }}
                                options={forwardUserList.map((user: any) => ({
                                  value: user.LOGIN_ID || user.LoginId,
                                  label: user.NAME_SURNAME || user.Fullname,
                                }))}
                              />
                            )}
                          />
                        ) : (
                          <OrganizationTree
                            key="forward-tree"
                            instanceId="forward-tree"
                            multiple={false}
                            showText={false}
                            userSelectionOnly={true}
                            setSelected={(value) => setValue('ForwardLoginId', value)}
                            initialSelections={orgDataForward as unknown as IOrganizationSchemaParams}
                            reload={!!orgDataForward}
                            userId={userId}
                            progressiveEnable={false}
                          />
                        )}
                        <WGrid container justifyContent="flex-end" style={{ marginTop: 20 }}>
                          <ActionButton label={t('forward')} onClick={() => handleActionClick('forward')} disabled={loading || !actionPermissions.CanForward} />
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'sendToComment',
                    label: t('sendToComment'),
                    visible: tabVisibility.SendToCommentTabVisible,
                    content: (
                      <>
                        <CommentField
                          value={comments['sendToComment'] ?? ''}
                          placeholder={t('comment')}
                          onChange={(value) => handleCommentChange('sendToComment', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="sendToComment"
                        />
                        {isCommentSelectBox ? (
                          <Controller
                            name="SendToCommentLoginId"
                            control={control}
                            render={({ field }) => (
                              <SelectBox
                                label={t('chooseUser')}
                                value={field.value}
                                onChange={(item: any) => {
                                  void field.onChange(item?.value)
                                }}
                                options={commentUserList.map((user: any) => ({
                                  value: user.LOGIN_ID || user.LoginId,
                                  label: user.NAME_SURNAME || user.Fullname,
                                }))}
                              />
                            )}
                          />
                        ) : (
                          <OrganizationTree
                            key="send-to-comment-tree"
                            instanceId="send-to-comment-tree"
                            multiple={false}
                            showText={false}
                            userSelectionOnly={false}
                            setSelected={(value) => setValue('SendToCommentLoginId', value)}
                            initialSelections={orgDataSendToComment as unknown as IOrganizationSchemaParams}
                            reload={!!orgDataSendToComment}
                            userId={userId}
                            progressiveEnable={false}
                          />
                        )}
                        <WGrid container justifyContent="flex-end" mt={2}>
                          <ActionButton
                            label={t('send')}
                            onClick={() => handleActionClick('sendToComment')}
                            disabled={loading || !actionPermissions.CanSendtoCommend}
                          />
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'suspend',
                    label: t('suspendContinue'),
                    visible: tabVisibility.SuspendResumeTabVisible,
                    content: (
                      <>
                        {!isSuspended && (
                          <Controller
                            name="suspendUntil"
                            control={control}
                            render={({ field }) => <WDatePicker {...field} value={field.value ?? null} onChange={field.onChange} label={t('suspendUntil')} />}
                          />
                        )}
                        <CommentField
                          value={comments['suspend'] ?? ''}
                          placeholder={t('suspendReason')}
                          onChange={(value) => handleCommentChange('suspend', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="suspend"
                        />
                        <WGrid container justifyContent="flex-end">
                          <ActionButton
                            label={t('suspend')}
                            onClick={() => handleActionClick('suspend')}
                            disabled={loading || !actionPermissions.CanSuspend || isSuspended}
                          />
                          <ActionButton
                            label={t('continue')}
                            onClick={() => handleActionClick('resume')}
                            disabled={loading || !actionPermissions.CanResume || !isSuspended}
                          />
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'abort',
                    label: t('cancel'),
                    visible: tabVisibility.AbortTabVisible,
                    content: (
                      <>
                        <CommentField
                          value={comments['abort'] ?? ''}
                          placeholder={t('cancellationReason')}
                          onChange={(value) => handleCommentChange('abort', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="abort"
                        />
                        <WGrid container justifyContent="flex-start">
                          <ActionButton label={t('cancel')} onClick={() => handleActionClick('cancel')} disabled={loading || !actionPermissions.CanCancel} />
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'finalize',
                    label: t('finalize'),
                    visible: tabVisibility.FinalizeTabVisible,
                    content: (
                      <>
                        <CommentField
                          value={comments['finalize'] ?? ''}
                          placeholder={t('finalizeReason')}
                          onChange={(value) => handleCommentChange('finalize', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="finalize"
                        />
                        <WGrid container justifyContent="flex-end">
                          <ActionButton label={t('finalize')} onClick={() => handleActionClick('finalize')} disabled={loading || !actionPermissions.CanFinalize} />
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'sendRequestToComment',
                    label: t('addComment'),
                    visible: tabVisibility.AddCommentTabVisible,
                    content: (
                      <>
                        <CommentField
                          value={comments['sendRequestToComment'] ?? ''}
                          placeholder={t('comment')}
                          onChange={(value) => handleCommentChange('sendRequestToComment', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="sendRequestToComment"
                        />
                        <WGrid container justifyContent="flex-end">
                          <ActionButton
                            label={t('addComment')}
                            onClick={() => handleActionClick('sendRequestToComment')}
                            disabled={loading || (!actionPermissions.CanSendRequestToComment && !actionPermissions.CanAddToComment)}
                          />
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'rollback',
                    label: t('revoke'),
                    visible: tabVisibility.RollbackTabVisible,
                    content: (
                      <>
                        <CommentField
                          value={comments['rollback'] ?? ''}
                          placeholder={t('revokeReason')}
                          onChange={(value) => handleCommentChange('rollback', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="rollback"
                        />
                        <WGrid container justifyContent="flex-end">
                          <ActionButton label={t('revoke')} onClick={() => handleActionClick('rollback')} disabled={loading || !actionPermissions.CanRollBack} />
                        </WGrid>
                      </>
                    ),
                  },
                  {
                    key: 'fileUpload',
                    label: t('fileUpload'),
                    visible: tabVisibility.FileUploadTabVisible,
                    content: (
                      <>
                        <CommentField
                          value={comments['fileUpload'] ?? ''}
                          placeholder={t('comment')}
                          onChange={(value) => handleCommentChange('fileUpload', value)}
                          error={errors.comment?.message?.toString()}
                          tabKey="fileUpload"
                        />
                        <FileUpload
                          pathKey={'SharePointActionPanelUploadFolder'}
                          onUpload={handleFileUploadComplete}
                          onDelete={handleFileDelete}
                          disabled={loading || !actionPermissions.CanFileUpload}
                          variant="full"
                          title={t('attachments')}
                          initialFiles={documents}
                        />
                        <Controller name="DetailJson" control={control} render={({ field }) => <input type="hidden" {...field} />} />
                      </>
                    ),
                  },
                ]

                const visibleTabs = tabs.filter((_tab) => tab.visible)

                useEffect(() => {
                  if (visibleTabs.length > 0) {
                    // Find if the current active tab is still visible
                    const currentTabKey = tabs.filter((_tab) => tab.visible)[activeTab]?.key
                    const currentTabStillVisible = visibleTabs.findIndex((tab) => tab.key === currentTabKey)

                    if (currentTabStillVisible !== -1) {
                      // Keep the current tab active if it's still visible
                      setActiveTab(currentTabStillVisible)
                      setActiveAction(currentTabKey)
                    } else {
                      // Only reset to first tab if current tab is no longer visible
                      setActiveAction(visibleTabs[0].key)
                      setActiveTab(0)
                    }
                  }, [visibleTabs.length]) // Only depend on length to avoid unnecessary re-renders

                if (isDaemonWorking) {
                  return (
                    <WPaper style={{ marginTop: 40, marginBottom: 20, width: '100%' }}>
                      <WLinearProgress />
                      <p style={{ textAlign: 'center', marginTop: 10 }}>{t('daemonStillWorking')}</p>
                    </WPaper>
                  )
                }

                return (
    <>
                    <WGrid
                      container
                      marginBottom={5}
                      marginTop={1}
                      style={{ display: visibleTabs.length > 0 ? 'block' : 'none', border: '1px solid #bbb', borderRadius: '5px' }}
                    >
                      <WGrid item xs={12}>
                        <WPaper style={{ backgroundColor: 'white', borderBottom: '1px solid #ccc' }}>
                          <WTabs value={activeTab} onChange={handleChange} variant="scrollable" scrollButtons="auto">
                            {visibleTabs.map((tab, index) => (
                              <WTab
                                key={index}
                                label={tab.label}
                                style={{
                                  marginRight: '5px',
                                  borderRadius: '5px 5px 0 0',
                                }}
                              />
                            ))}
                          </WTabs>
                        </WPaper>
                      </WGrid>
                      <WGrid item xs={12}>
                        <TabContent>{loading ? <WCircularProgress /> : visibleTabs[activeTab]?.content}</TabContent>
                      </WGrid>
                      <WDialog open={openConfirmDialog} onClose={() => setOpenConfirmDialog(false)} aria-labelledby="confirm-dialog-title">
                        <WDialogTitle id="confirm-dialog-title">{t('confirmAction')}</WDialogTitle>
                        <WDialogContent>{t('areYouSure', { action: t(confirmAction) })}</WDialogContent>
                        <WDialogActions>
                          <WButton onClick={() => setOpenConfirmDialog(false)} color="primary">
                            {t('cancel')}
                          </WButton>
                          <WButton onClick={handleConfirmAction} color="primary" autoFocus>
                            {t('confirm')}
                          </WButton>
                        </WDialogActions>
                      </WDialog>
                    </>
                    )
})

                    export default ActionPanelComponent
