import React, { useState, useEffect, useLayoutEffect } from 'react'
import { WGrid } from 'wface'
import SelectBox from '@/components/formElements/SelectBox/SelectBox'
import { useSelectOption, useOrganizationTree, useUpdateEffect } from '@/hooks'
import { IDepsPath, IOption, IOrganizationSchemaParams, ISubTeamOption } from '@/types'
import { clearDepsPath } from '@/utils/helpers/screen'
import { useTranslation } from 'react-i18next'

interface IOrganizationTreeAttributes {
  all?: boolean
  department?: boolean
  divison?: boolean
  unit?: boolean
  team?: boolean
  subteams?: boolean
  user?: boolean
}

interface IOrganizationTreeProps {
  setSelected: (value: any) => void
  multiple?: boolean
  showText?: boolean
  reload?: boolean
  initialSelections?: IOrganizationSchemaParams
  error?: string
  userId?: number
  disable?: IOrganizationTreeAttributes
  visible?: IOrganizationTreeAttributes
  progressiveEnable?: boolean
  userSelectionOnly?: boolean
  instanceId?: string
}

const OrganizationTree = React.memo<IOrganizationTreeProps>(
  ({
    setSelected,
    multiple = true,
    showText = true,
    initialSelections = null,
    reload = false,
    disable = null,
    visible = null,
    userId = undefined,
    error = undefined,
    progressiveEnable = false,
    userSelectionOnly = false,
    instanceId = '',
  }) => {
    const { t } = useTranslation('organizationTree')
    const [isQueryEnabled, setIsQueryEnabled] = useState<boolean>(true)
    const [isUserUpdateEnabled, setIsUserUpdateEnabled] = useState<boolean>(false)

    const [selectedDepartment, setSelectedDepartment, departmentList, setDepartmentList] = useSelectOption<IOption>()
    const [lastSelectedId, setLastSelectedId] = useState<number>(0)
    const [selectedDivision, setSelectedDivision, divisionList, setDivisionList] = useSelectOption<IOption>()
    const [selectedUnit, setSelectedUnit, unitList, setUnitList] = useSelectOption<IOption>()
    const [selectedTeam, setSelectedTeam, teamList, setTeamList] = useSelectOption<IOption>()
    const [selectedUser, setSelectedUsers, userList, setUserList] = useSelectOption<IOption>([])
    const [selectedSubTeams, setSelectedSubTeams, subTeamLists, setSubTeamLists] = useSelectOption<ISubTeamOption[]>([[]])
    const [activeLevel, setActiveLevel] = useState<string>('department')
    const [subTeamLevels, setSubTeamLevels] = useState<IOption[][]>([])

    const { data } = useOrganizationTree(
      {
        selectedDepartment: selectedDepartment?.value ?? undefined,
        selectedDivision: selectedDivision?.value ?? undefined,
        selectedUnit: selectedUnit?.value ?? undefined,
        selectedTeam: selectedTeam?.value ?? undefined,
        selectedSubTeams: selectedSubTeams?.map((_subTeam) => subTeam.value) ?? [],
        lastSelectedId,
        showText,
        userId,
      },
      isQueryEnabled,
    )

    const isDisabled = (level: string, index?: number) => {
      if (userSelectionOnly && level !== 'user') {
        return true
      }

      if (disable?.all || disable?.[level as keyof typeof disable]) {
        return true
      }

      if (!progressiveEnable) {
        return false
      }

      switch (level) {
        case 'department':
          return activeLevel !== 'department'
        case 'division':
          return !divisionList?.length || activeLevel !== 'division'
        case 'unit':
          return !unitList?.length || activeLevel !== 'unit'
        case 'team':
          return !teamList?.length || activeLevel !== 'team'
        case 'subTeam':
          return !subTeamLists?.length || activeLevel !== `subTeam${index ? index + 1 : 1}`
        case 'user':
          return !userList?.length || activeLevel !== 'user'
        default:
          return false
      }

      useEffect(() => {
        if (initialSelections) {
          setDepartmentList(initialSelections.departments ?? [])
          setSelectedDepartment(initialSelections.selectedDepartment ?? null)
          setDivisionList(initialSelections.divisions ?? [])
          setSelectedDivision(initialSelections.selectedDivision ?? null)
          setUnitList(initialSelections.units ?? [])
          setSelectedUnit(initialSelections.selectedUnit ?? null)
          setTeamList(initialSelections.teams ?? [])
          setSelectedTeam(initialSelections.selectedTeam ?? null)
          setUserList(initialSelections.users ?? [])

          if (userSelectionOnly) {
            setActiveLevel('user')
          } else {
            setActiveLevel('department')
          }
        }
      }, [initialSelections, userSelectionOnly])

      useEffect(() => {
        if (isQueryEnabled && orgData) {
          if (orgData?.departments?.length > 0 && !departmentList?.length) {
            setDepartmentList(orgData.departments)
          }
          if (orgData?.divisions?.length > 0 && !divisionList?.length) {
            setDivisionList(orgData.divisions)
          }
          if (orgData?.units?.length > 0 && !unitList?.length) {
            setUnitList(orgData.units)
          }
          if (orgData?.teams?.length > 0 && !teamList?.length) {
            setTeamList(orgData.teams)
          }
          if (orgData?.users?.length > 0 && !userList?.length) {
            setUserList(orgData.users)
          }
        }, [orgData, isQueryEnabled])

      useEffect(() => {
        if (reload) {
          refetch()
        }
      }, [reload, instanceId])

      const findPreviousLevelSelection = (level: string) => {
        switch (level) {
          case 'division':
            return selectedDepartment?.value ?? 0
          case 'unit':
            return selectedDivision?.value ?? 0
          case 'team':
            return selectedUnit?.value ?? 0
          case 'subTeam':
            return selectedTeam?.value ?? 0
          default:
            return 0
        }

        const handleSelectionChange = async (level: string, value: IOption, subTeamIndex: number = 0) => {
          setLastSelectedId(value.value > 0 ? value.value : findPreviousLevelSelection(level))
          switch (level) {
            case 'department':
              setSelectedDepartment(value)
              setSubTeamLists([])
              setSelectedSubTeams([])
              setSubTeamLevels([])
              setTeamList([])
              setSelectedTeam(null)
              setUnitList([])
              setSelectedUnit(null)
              setDivisionList([])
              setSelectedDivision(null)
              if (progressiveEnable) setActiveLevel('division')
              await refetch()
              !isUserUpdateEnabled && setIsUserUpdateEnabled(true)
              break
            case 'division':
              setSelectedDivision(value)
              setSubTeamLists([])
              setSelectedSubTeams([])
              setSubTeamLevels([])
              setTeamList([])
              setSelectedTeam(null)
              setUnitList([])
              setSelectedUnit(null)
              if (progressiveEnable) setActiveLevel('unit')
              await refetch()
              !isUserUpdateEnabled && setIsUserUpdateEnabled(true)
              break
            case 'unit':
              setSelectedUnit(value)
              setSubTeamLists([])
              setSelectedSubTeams([])
              setSubTeamLevels([])
              setTeamList([])
              setSelectedTeam(null)
              if (progressiveEnable) setActiveLevel('team')
              await refetch()
              !isUserUpdateEnabled && setIsUserUpdateEnabled(true)
              break
            case 'team':
              setSelectedTeam(value)
              setSubTeamLists([])
              setSelectedSubTeams([])
              setSubTeamLevels([])
              if (progressiveEnable) setActiveLevel('subTeam1')
              await refetch()
              !isUserUpdateEnabled && setIsUserUpdateEnabled(true)
              break
            case 'subTeam':
              const newSelectedSubTeams = [...(selectedSubTeams ?? [])]
              newSelectedSubTeams[subTeamIndex] = value
              setSelectedSubTeams(newSelectedSubTeams.slice(0, subTeamIndex + 1))
              setSubTeamLevels((prevLevels) => prevLevels.slice(0, subTeamIndex + 1))
              if (progressiveEnable) setActiveLevel(`subTeam${subTeamIndex + 2}`)
              await refetch()
              !isUserUpdateEnabled && setIsUserUpdateEnabled(true)
              break
            default:
              break
          }

          const getDepsPathValue = (path: IDepsPath, key: keyof IDepsPath): string | null => {
            return clearDepsPath(path[key] as string)
          }

          const depsPathKey: keyof IDepsPath = t('depsPathKey') === 'depsTr' ? 'depsTr' : 'depsEn'

          useUpdateEffect(() => {
            if (isQueryEnabled && orgData) {
              if (orgData?.subTeams?.length > 0) {
                setSubTeamLevels((prevLevels: any) => [...prevLevels, orgData.subTeams])
              } else if (orgData?.teams?.length > 0) {
                setTeamList(orgData.teams)
              } else if (orgData?.units?.length > 0) {
                setUnitList(orgData.units)
              } else if (orgData?.divisions?.length > 0) {
                setDivisionList(orgData.divisions)
              } else if (orgData?.departments?.length > 0) {
                setDepartmentList(orgData.departments)
              }
            }, [orgData])

          useUpdateEffect(() => {
            if (isUserUpdateEnabled) {
              setSelectedUsers(null)
              setUserList(orgData?.users ?? [])
              setActiveLevel('user')
            }
          }, [isUserUpdateEnabled, departmentList, divisionList, unitList, teamList, subTeamLists])

          useEffect(() => {
            if (initialSelections?.selectedDepartment ?? userSelectionOnly) {
              setIsQueryEnabled(true)
            }
          }, [initialSelections?.selectedDepartment, userSelectionOnly])

          const ensureArray = (options: IOption[] | null | undefined): IOption[] => {
            if (Array.isArray(options)) {
              return options.map((option: any) => ({
                ...option,
                subTeams: Array.isArray(option.subTeams) ? option.subTeams : [],
              }))
            }
            return []
          }

          useLayoutEffect(() => {
            if (userList && userList.length > 0 && userId != null) {
              setSelectedUsers(userList?.find((user) => user.value == userId) ?? null)
            }
          }, [userId, userList])

          return (
            <WGrid fontSize={14} container spacing={3.5}>
              {showText && orgData?.depsPath && (
                <div style={{ padding: '5px 15px 10px 25px' }}>
                  <strong>{orgData.depsPath.nameSurname}</strong>
                  <div>{getDepsPathValue(orgData.depsPath, depsPathKey)}</div>
                </div>
              )}
              {!showText && (
                <>
                  {(visible?.department != false || !visible) && (
                    <WGrid item xs={12}>
                      <SelectBox
                        label={t('department')}
                        value={selectedDepartment}
                        onChange={(value: any) => handleSelectionChange('department', value)}
                        options={ensureArray(departmentList)}
                        id="selectedDepartment"
                        name="selectedDepartment"
                        defaultText={t('selectDepartmentDefaultText')}
                        disabled={userSelectionOnly || (disable && (disable.all || disable.department)) || isDisabled('department') || false}
                      />
                    </WGrid>
                  )}
                  {(visible?.divison != false || !visible) && (
                    <WGrid item xs={12}>
                      <SelectBox
                        label={t('division')}
                        value={selectedDivision}
                        onChange={(value: any) => handleSelectionChange('division', value)}
                        options={ensureArray(divisionList)}
                        id="selectedDivision"
                        name="selectedDivision"
                        defaultText={t('selectDivisionDefaultText')}
                        disabled={userSelectionOnly || (disable && (disable.all || disable.divison)) || isDisabled('division') || false}
                      />
                    </WGrid>
                  )}
                  {(visible?.unit != false || !visible) && (
                    <WGrid item xs={12}>
                      <SelectBox
                        label={t('unit')}
                        value={selectedUnit}
                        onChange={(value: any) => handleSelectionChange('unit', value)}
                        options={ensureArray(unitList)}
                        defaultText={t('selectUnitDefaultText')}
                        disabled={userSelectionOnly || (disable && (disable.all || disable.unit)) || isDisabled('unit') || false}
                      />
                    </WGrid>
                  )}
                  {(visible?.team != false || !visible) && (
                    <WGrid item xs={12}>
                      <SelectBox
                        label={t('team')}
                        value={selectedTeam}
                        onChange={(value: any) => handleSelectionChange('team', value)}
                        options={ensureArray(teamList)}
                        defaultText={t('selectTeamDefaultText')}
                        disabled={userSelectionOnly || (disable && (disable.all || disable.team)) || isDisabled('team') || false}
                      />
                    </WGrid>
                  )}
                  {(visible?.subteams != false || !visible) &&
                    !!selectedTeam &&
                    subTeamLevels.length > 0 &&
                    subTeamLevels[0].length > 0 &&
                    subTeamLevels.map((subTeamLevel, index) => (
                      <WGrid item xs={12} key={index}>
                        <SelectBox
                          value={selectedSubTeams && selectedSubTeams[index] ? selectedSubTeams[index] : null}
                          label={`${t('subteam')} ${index + 1}`}
                          onChange={(value: any) => handleSelectionChange('subTeam', value, index)}
                          options={ensureArray(subTeamLevel)}
                          defaultText={t('selectSubTeamDefaultText')}
                          disabled={userSelectionOnly || (disable && (disable.all || disable.subteams)) || isDisabled('subTeam', index) || false}
                        />
                      </WGrid>
                    ))}
                  {(visible?.user != false || !visible) && (
                    <WGrid item xs={12}>
                      <SelectBox
                        label={t('user')}
                        value={selectedUser}
                        multiple={multiple}
                        searchable={true}
                        defaultText={t('selectUserDefaultText')}
                        onChange={(selectedOptions: IOption | IOption[] | null) => {
                          setSelectedUsers(selectedOptions as any)
                          if (multiple) {
                            const values = selectedOptions ? (selectedOptions as IOption[]).map((_opt) => opt.value) : []
                            setSelected(values)
                          } else {
                            const value = selectedOptions ? (selectedOptions as IOption).value : null
                            setSelected(value)
                          }
                        }}
                        options={ensureArray(userList)}
                        error={error}
                        disabled={
                          (disable && (disable.all || disable.user)) || (!userSelectionOnly && progressiveEnable && activeLevel !== 'user') || false
                        }
                      />
                    </WGrid>
                  )}
                </>
              )}
            </WGrid>
          )
        },
)

export default OrganizationTree
