import React from 'react'
import { useTranslation } from 'react-i18next'
import { WBox, WTypography, WButton } from 'wface'

const UnauthorizedComponent: React.FC = () => {
  const { t } = useTranslation('unauthorized')
  return (
    <WBox
      style={{
        display: 'flex',
        minHeight: '100vh',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5',
      }}
    >
      <div style={{ maxWidth: '600px', width: '100%', margin: '0 auto', padding: '0 16px' }}>
        <div
          style={{
            padding: '32px',
            backgroundColor: 'white',
            borderRadius: '4px',
            boxShadow: '0 3px 5px 2px rgba(0, 0, 0, .1)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              backgroundColor: '#dc004e',
              borderRadius: '50%',
              padding: '16px',
              marginBottom: '16px',
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40" fill="white">
              <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z" />
            </svg>
          </div>
          <WTypography variant="h4" style={{ marginBottom: '16px' }}>
            {t('title')}
          </WTypography>
          <WTypography variant="body1" style={{ textAlign: 'center', marginBottom: '16px' }}>
            {t('description')}
          </WTypography>
          <WButton variant="contained" color="primary" onClick={() => window.history.back()} style={{ marginTop: '16px' }}>
            {t('button')}
          </WButton>
        </div>
      </div>
    </WBox>
  )
}

export default UnauthorizedComponent
