import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { WTextField, WTypography, WBox, WGrid, WCircularProgress } from 'wface'
import Modal from '@/components/Modal/Modal'
import api from '@/api'
import toast from 'react-hot-toast'
import { IHistory } from '@/types'

interface HistoryNotificationModalProps {
  isOpen: boolean
  onClose: () => void
  workflowData: IHistory
}

export default function HistoryNotificationModal({ isOpen, onClose, workflowData }: HistoryNotificationModalProps) {
  const { t } = useTranslation('history')
  const [notificationNote, setNotificationNote] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!notificationNote.trim()) return

    setIsSubmitting(true)
    try {
      const response = await api.post('/histories/notification', {
        NotificationNote: notificationNote,
        WfInstanceId: workflowData.wfinstanceid,
      })
      if (response.data) {
        toast.success(t('notificationSuccess'))
        setNotificationNote('')
        onClose()
      }
    } catch (_error) {
      toast.error(t('notificationError'))
      if (process.env.NODE_ENV === 'development') {
        console.error('Notification submission error:', _error)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Modal open={isOpen} title={t('notificationTitle')} onClose={onClose} onConfirm={handleSubmit}>
      <WBox padding={2}>
        <WGrid container spacing={2}>
          <WGrid item xs={12} sm={6}>
            <WTypography variant="body2">
              <strong>{t('flowname')}:</strong>
            </WTypography>
            <WTypography variant="body1">
              {workflowData.flowname} - {workflowData.statename}
            </WTypography>
          </WGrid>
          <WGrid item xs={12} sm={6}>
            <WTypography variant="body2">
              <strong>{t('workflowNo')}:</strong>
            </WTypography>
            <WTypography variant="body1">{workflowData.wfinstanceid}</WTypography>
          </WGrid>
          <WGrid item xs={12}>
            <WTypography variant="body2">
              <strong>{t('assignedUser')}:</strong>
            </WTypography>
            <WTypography variant="body1">{workflowData.assignednamesurname}</WTypography>
          </WGrid>
          <WGrid item xs={12}>
            <WTextField
              label={t('notificationNote')}
              variant="outlined"
              value={notificationNote}
              onChange={(e) => setNotificationNote(e.target.value)}
              multiline
              rows={4}
              fullWidth
            />
          </WGrid>
        </WGrid>
        {isSubmitting && (
          <WBox display="flex" justifyContent="center" marginTop={2}>
            <WCircularProgress size={24} />
          </WBox>
        )}
      </WBox>
    </Modal>
  )
}
