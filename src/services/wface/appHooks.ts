import { getUser, getUserId } from '..'
import useAppHelper from './appHelper'

function setUserId(userId: string | number) {
  const { setValue } = useAppHelper() as { setValue: (key: string, value: string | number | boolean | object) => void }
  setValue('UserId', userId)
}

export default function AppHooks() {
  const onAppDidMount = () => {
    getUserId().then((userId) => {
      getUser(userId).then((result) => {
        setUserId(result.id)
      })
    })
  }
  return { onAppDidMount }
} // End of AppHooks
