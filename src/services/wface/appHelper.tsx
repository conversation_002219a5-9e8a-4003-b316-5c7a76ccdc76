import { useNavigationHistory } from '@/hooks/useNavigationHistory'
import { useNavigate } from 'react-router-dom'
import { useAppContext, useUserContext } from 'wface'
import { safeNavigate } from '@/utils/helpers/navigation/navigationHelpers'

export default function AppHelper() {
  const navigate = useNavigate()
  const { data } = useUserContext()
  const { cache, setValue } = useAppContext()
  
  const loading = (value: boolean) => setValue('loading', value)
  const showMessage = (value: string) => setValue('message', value)
  
  // Define setQueryParams function
  const setQueryParams = (params: Record<string, string>) => {
    setValue('queryParams', params)
  }
  
  // Define openScreenById function
  const openScreenById = (screenId: string, parameters?: any) => {
    openScreen(screenId, parameters)
  }
  
  const openScreen = (screen: string, parameters?: any) => {
    const urlPath = '/main/' + screen
    let searchParams = ''

    // Handle URLSearchParams or plain object
    if (parameters instanceof URLSearchParams) {
      searchParams = parameters.toString()
    } else if (parameters && typeof parameters === 'object') {
      searchParams = new URLSearchParams(parameters).toString()
    } else if (parameters) {
      searchParams = parameters.toString()
    }

    // Add ? prefix if there are params
    const searchString = searchParams ? `?${searchParams}` : ''

    // Set query params in app context before navigation
    if (searchParams) {
      const paramsObject: Record<string, string> = {}
      const urlSearchParams = new URLSearchParams(searchParams)
      urlSearchParams.forEach((value, key) => {
        paramsObject[key] = value
      })
      setQueryParams(paramsObject)
    }

    // Navigate directly with query params instead of using openScreenById
    // since wface's openScreen doesn't support query params
    safeNavigate(navigate, urlPath + searchString)

    addToHistory(urlPath, searchString)
  }

  const openScreenUrl = (screen: string, urlParams?: Record<string, string>) => {
    openScreen(screen, urlParams)
  }
  
  const getInitialScreenValue = (screen: string): any => {
    const value = localStorage.getItem(screen)
    return value ? JSON.parse(value) : undefined
  }
  
  const setInitialScreenValue = (screen: string, value: unknown): void => {
    localStorage.setItem(screen, JSON.stringify(value))
  }
  
  const clearInitialScreenValue = (screen: string): void => {
    localStorage.removeItem(screen)
  }
  
  // Initialize navigation history after openScreen is defined
  const { addToHistory } = useNavigationHistory(openScreen)
  
  return {
    user: data,
    cache,
    addToHistory,
    setValue,
    loading,
    showMessage,
    setQueryParams,
    openScreenById,
    openScreen,
    openScreenUrl,
    getInitialScreenValue,
    setInitialScreenValue,
    clearInitialScreenValue,
  }
  const useAppHelper = AppHelper
  export default useAppHelper
