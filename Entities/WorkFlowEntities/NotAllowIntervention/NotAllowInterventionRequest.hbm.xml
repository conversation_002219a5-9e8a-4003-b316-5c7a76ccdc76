<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="NotAllowInterventionRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SITE_NOT_ALLOW_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
      <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
      <property name="RegionId" column="REGION_ID" />
      <property name="CityId" column="CITY_ID" />
      <property name="ServicesId" column="SERVICES_ID" />
      <property name="OpenDate" column="OPEN_DATE" />
      <property name="SiteName" column="SITE_NAME" />
      <property name="SiteContactName" column="SITE_CONTACT_NAME" />
      <property name="SiteContactPhone" column="SITE_CONTACT_PHONE" />
      <property name="OperationalCompanyName" column="OPERATIONAL_CO" />
      <property name="OperationalCompanyPersonelName" column="OPERATIONAL_CO_PERS_NAME" />
      <property name="OperationalCompanyPersonelPhone" column="OPERATIONAL_CO_PERS_PHONE" />
      <property name="Description" column="DESCRIPTION" />
      <property name="Created" column="CREATED" />
      <property name="LastUpdated" column="LAST_UPDATED" />
      <property name="CreatedBy" column="CREATED_BY" />
      <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
      <property name="VersionID" column="VERSION_ID" />
    </class>
    </hibernate-mapping>