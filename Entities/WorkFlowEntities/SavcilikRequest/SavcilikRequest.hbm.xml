<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SavcilikRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SAVCILIK_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="DESCRIPTION" column="DESCRIPTION" />
    <property name="ACC_VAR" column="ACC_VAR" />
    <property name="SES" column="SES" />
    <property name="FAKS" column="FAKS" />
    <property name="UYE_BILGISI" column="UYE_BILGISI" />
    <property name="IPTAL_BILGISI" column="IPTAL_BILGISI" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>