﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class OldPurchaseRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long FormId { get; set; }
        public virtual string UserName { get; set; }
        public virtual string Subject { get; set; }
        public virtual DateTime PurchaseCreated { get; set; }
        public virtual string Status { get; set; }
        public virtual string BudgetCodes { get; set; }
        public virtual decimal Amount { get; set; }
        public virtual string Currency { get; set; }
        public virtual decimal ConsumedAmount { get; set; }
        public virtual decimal AmountOpt { get; set; }
        public virtual decimal CurrentAmount { get; set; }
        public virtual string Type { get; set; }
        public virtual long PurchaseCreatedBy { get; set; }
        public virtual string PurchaseInnerOrOuter { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}