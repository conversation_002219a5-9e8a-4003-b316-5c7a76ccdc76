﻿using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class MalzemeCikisEntityDetay : EntityBase, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string FirmaId { get; set; }
        public virtual string TeslimAlanSirketId { get; set; }
        public virtual string TeslimAlanSirketAd { get; set; }

        public virtual string Sube { get; set; }
        public virtual string Tip { get; set; }
        public virtual string Marka { get; set; }
        public virtual string Model { get; set; }
        public virtual string AssetNo { get; set; }
        public virtual string SeriNo { get; set; }
        public virtual string Cikis<PERSON>edeni { get; set; }
        public virtual string Adet { get; set; }
        public virtual DateTime Tarih { get; set; }

        public virtual string VarlikYonetimiId { get; set; }
        public virtual string VarlikYonetimiKaydiVarMi { get; set; }

        public virtual string GarantiKapsamindaMi { get; set; }
    }
}
