using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class YurtDisiUcretsizPaketFormu : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string <PERSON>yeNo { get; set; }
        public virtual string Bolge { get; set; }
        public virtual string UyelikTipi { get; set; }
        public virtual string Bayi<PERSON>odu { get; set; }
        public virtual string PaketTipi { get; set; }
        public virtual DateTime YayinBaslangicTarihi { get; set; }
        public virtual DateTime YayinBitisTarihi { get; set; }
        public virtual string TalepNedeni { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}

