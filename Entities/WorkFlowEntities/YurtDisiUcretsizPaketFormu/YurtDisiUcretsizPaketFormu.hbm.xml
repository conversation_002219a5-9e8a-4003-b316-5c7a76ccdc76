<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="YurtDisiUcretsizPaketFormu, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_YURTDISI_UCRETSIZ_PAKET" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>   
    <property name="UyeNo" column="UYE_NO" type="string" />
    <property name="Bolge" column="BOLGE" type="string" />  
    <property name="UyelikTipi" column="UYELIK_TIPI" type="string" />
    <property name="BayiKodu" column="BAYI_KODU" type="string" />
    <property name="PaketTipi" column="PAKET_TIPI" type="string" />
    <property name="YayinBaslangicTarihi" column="YAYIN_BAS_TARIHI" type="DateTime" />
    <property name="YayinBitisTarihi" column="YAYIN_BIT_TARIHI" type="DateTime" />
    <property name="TalepNedeni" column="TALEP_NEDENI" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionID" column="VERSION_ID" type="long" />
  </class>
</hibernate-mapping>

