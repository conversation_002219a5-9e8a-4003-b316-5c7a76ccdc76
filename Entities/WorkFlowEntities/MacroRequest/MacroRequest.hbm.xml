<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="MacroRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_MACRO_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="MACRO_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="PersonelID" column="PERSONEL_ID" />
    <property name="PersonelUserName" column="PERSONEL_USER_NAME" />
    <property name="Description" column="DESCRIPTION" />
    <property name="MacroID" column="MACRO_ID" />
    <property name="Purpose" column="PURPOSE" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="IsApprovalRequest" column="IS_APPROVAL_REQUEST" />
  </class>
</hibernate-mapping>