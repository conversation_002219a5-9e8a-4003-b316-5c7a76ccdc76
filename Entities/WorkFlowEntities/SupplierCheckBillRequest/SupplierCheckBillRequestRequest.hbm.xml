﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SupplierCheckBillRequestRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SUPPLIER_BILL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="SUPPLIER_BILL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="ServiceTypeId" column="SERVICE_TYPE_ID" />
    <property name="RegionId" column="REGION_ID" />
    <property name="CityId" column="CITY_ID" />
    <property name="YTSServiceId" column="YTS_SERVICE_ID" />
    <property name="RequestStatus" column="REQUEST_STATUS" />
    <property name="RequestReason" column="REQUEST_REASON" />
    <property name="RequestDetail" column="REQUEST_DETAIL" />
    <property name="AttachmentFile1" column="ATTACHMENT_FILE_1" />
    <property name="AttachmentFile2" column="ATTACHMENT_FILE_2" />
    <property name="HasTermInterestRate" column="HAS_TERM_INTEREST_RATE" />
    <property name="IsEncashed" column="IS_ENCASHED" />
    <property name="HasPostponedCheck" column="HAS_POSTPONED_CHECK" />
    <property name="IsNewCheckReceived" column="IS_NEW_CHECK_RECEIVED" />
    <property name="HasPaymentRate" column="HAS_PAYMENT_RATE" />
    <property name="HasProductRate" column="HAS_PRODUCT_RATE" />
    <property name="CheckBillNo" column="CHECKBILL_NO" />
    <property name="CheckBillDate" column="CHECKBILL_DATE" />
    <property name="CheckBillAmount" column="CHECKBILL_AMOUNT" />
    <property name="SupplierMontlyDeserved" column="SUPPLIER_MONTLY_DESERVED" />
    <property name="SupplierClaim" column="SUPPLIER_CLAIM" />
    <property name="GuaranteeLetterAmount" column="GUARANTEE_LETTER_AMOUNT" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>