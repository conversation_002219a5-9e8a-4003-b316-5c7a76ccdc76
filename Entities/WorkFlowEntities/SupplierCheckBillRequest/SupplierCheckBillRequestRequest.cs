﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SupplierCheckBillRequestRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long ServiceTypeId { get; set; }
        public virtual long RegionId { get; set; }
        public virtual long CityId { get; set; }
        public virtual long YTSServiceId { get; set; }
        public virtual string RequestStatus { get; set; }
        public virtual string RequestReason { get; set; }
        public virtual string RequestDetail { get; set; }
        public virtual string AttachmentFile1 { get; set; }
        public virtual string AttachmentFile2 { get; set; }
        public virtual long HasTermInterestRate { get; set; }
        public virtual long IsEncashed { get; set; }
        public virtual long HasPostponedCheck { get; set; }
        public virtual long IsNewCheckReceived { get; set; }
        public virtual long HasPaymentRate { get; set; }
        public virtual long HasProductRate { get; set; }
        public virtual string CheckBillNo { get; set; }
        public virtual DateTime CheckBillDate { get; set; }
        public virtual decimal CheckBillAmount { get; set; }
        public virtual decimal SupplierMontlyDeserved { get; set; }
        public virtual decimal SupplierClaim { get; set; }
        public virtual decimal GuaranteeLetterAmount { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}