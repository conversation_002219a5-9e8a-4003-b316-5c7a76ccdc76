﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="PurchaseOrderDetailFileRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PURCHASE_ORDER_FL_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="FileName" column="FILE_NAME" />
    <property name="Status" column="STATUS" />
  </class>
</hibernate-mapping>