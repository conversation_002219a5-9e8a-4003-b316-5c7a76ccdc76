<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
    <class name="PurchaseDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PURCHASE_DETAILREQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
    <generator class="trigger-identity"></generator>
    </id>
      <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
      <property name="OrderNo" column="ORDER_NO" />
      <property name="CategoryId" column="CATEGORY_ID" />
      <property name="Category" column="CATEGORY" />
      <property name="ProductId" column="PRODUCT_ID" />
      <property name="Product" column="PRODUCT" />
      <property name="ProductDefination" column="PRODUCT_DEFINATION" />
      <property name="Unit" column="UNIT" />
      <property name="UnitOfQuantity" column="UNIT_OF_QUANTITY" />
      <property name="EstimateTotalAmount" column="ESTIMATE_AMOUNT" />
      <property name="CurrencyCode" column="CURRENCY_CODE" />
      <property name="Created" column="CREATED" />
      <property name="LastUpdated" column="LAST_UPDATED" />
      <property name="CreatedBy" column="CREATED_BY" />
      <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
      <property name="VersionID" column="VERSION_ID" />
    </class>
    </hibernate-mapping>