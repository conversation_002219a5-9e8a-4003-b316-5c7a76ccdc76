﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class PurchaseButceDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string HesapKodu { get; set; }
        public virtual string HesapAdi { get; set; }
        public virtual string HesapAdiIng { get; set; }
        public virtual decimal ButceTutari { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}