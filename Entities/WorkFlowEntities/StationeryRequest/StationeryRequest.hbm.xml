﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="StationeryRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_STATIONERY_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="STATIONERY_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>

    <property name="ReqType" column="REQTYPE" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="Owner" column="OWNER" />
    <property name="Lokasyon" column="LOKASYON" />
    <property name="BulunduguKat" column="BULUNDUGU_KAT" />
  </class>
</hibernate-mapping>