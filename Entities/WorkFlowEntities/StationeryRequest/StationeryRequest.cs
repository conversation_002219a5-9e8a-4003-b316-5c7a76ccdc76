﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class StationeryRequest : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }

        public virtual long ReqType { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual long Owner { get; set; }
        public virtual long Lokasyon { get; set; }
        public virtual long BulunduguKat { get; set; }
        #endregion
    }
}