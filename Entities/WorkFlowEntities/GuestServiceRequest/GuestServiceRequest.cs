﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class GuestServiceRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long LoginId { get; set; }
        public virtual long AcanLoginId { get; set; }
        public virtual string ServisAdi { get; set; }
        public virtual long ServisId { get; set; }
        public virtual DateTime Tarih { get; set; }
        public virtual long TalepSira { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string Talep_Tip { get; set; }
        public virtual DateTime Bitis_Tarih { get; set; }
    }
}