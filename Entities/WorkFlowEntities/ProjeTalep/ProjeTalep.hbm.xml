﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ProjeTalep,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PROJE_TALEP" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="PROJE_TALEP_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="PROJE_ADI" column="PROJE_ADI" />
    <property name="ACIKLAMA" column="ACIKLAMA" />
    <property name="TESLIM_TARIHI" column="TESLIM_TARIHI" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>