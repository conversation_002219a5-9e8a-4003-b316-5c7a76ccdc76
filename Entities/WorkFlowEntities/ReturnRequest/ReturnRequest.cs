﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ReturnRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string CompanyName { get; set; }
        public virtual DateTime RequestDate { get; set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual long PurchaseRequestNo { get; set; }
        public virtual string PurchaseOwnerNameSurname { get; set; }

        public virtual long IsSupplementaryBudget { get; set; }
        public virtual string SupplementaryBudgets { get; set; }
        public virtual string PurchaseBudgets { get; set; }

        public virtual long NotInRD { get; set; }

        public virtual string ReturnBillDescription { get; set; }
        public virtual string BillSubject { get; set; }
        public virtual string CampaignCode { get; set; }
        public virtual string Purchaser { get; set; }

        public virtual decimal PaymentAmount { get; set; }
        public virtual string PaymentCurrency { get; set; }

        public virtual string InLettering { get; set; }

        public virtual decimal ExchangeRateBuy { get; set; }
        public virtual decimal ExchangeRateSell { get; set; }

        public virtual long IsPurchase { get; set; }
        public virtual long IsContract { get; set; }
        public virtual long IsDeliveryForm { get; set; }
        public virtual long IsDispatch { get; set; }
        public virtual DateTime DispatchDate { get; set; }
        public virtual string DispatchId { get; set; }
        public virtual long IsBill { get; set; }
        public virtual DateTime BillDate { get; set; }
        public virtual string BillId { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}