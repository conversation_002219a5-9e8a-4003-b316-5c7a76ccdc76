﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobQuitFormDetail : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string QUIT_POLDY { get; set; }
        public virtual string QUIT_SOCIAL_INSURANCE { get; set; }
        public virtual string QUIT_INTRANET { get; set; }
        public virtual string QUIT_SODEXO { get; set; }
        public virtual string QUIT_HEALTH_INSURANCE { get; set; }
        public virtual string QUIT_LIFE_INSURENCE { get; set; }
        public virtual string QUIT_PRIVATE_PENSION { get; set; }
        public virtual string RETRIEVAL_PERSONNEL_CARD { get; set; }
        public virtual string RETRIEVAL_COMPANY_VEHICLE { get; set; }
        public virtual string RETRIEVAL_TRANSPORTMATIC { get; set; }
        public virtual string RETRIEVAL_GSM_LINE { get; set; }
        public virtual string RETRIEVAL_MOBILE_PHONE { get; set; }
        public virtual string RETRIEVAL_NOT_EXISTING { get; set; }
        public virtual string RETRIEVAL_PERSONEL_COMPUTER { get; set; }
        public virtual string CLOSURE_TELEPHONE_LINE { get; set; }
        public virtual string CLOSURE_MAIL_ACCOUNT { get; set; }
        public virtual string CLOSURE_USER_ACCOUNT { get; set; }
        public virtual string CLOSURE_OUTLOOK_USER { get; set; }
        public virtual string EXISTS_OTHER_DEBITTED_HARDWARE { get; set; }
        public virtual string OTHER_DEBITTED_HARDWARES { get; set; }
        public virtual string EXIST_BROKEN_HARDWARES { get; set; }
        public virtual string BROKEN_HARDWARES { get; set; }
        public virtual string SECURITY_INFORMED { get; set; }
        public virtual string RETRIEVAL_DESKTOP_EQUIPMENTS { get; set; }
        public virtual string EXISTS_DESKTOP_EQUIPMENTS { get; set; }
        public virtual string EXISTS_JOB_ADVANCE { get; set; }
        public virtual string AMOUNT_JOB_ADVANCE { get; set; }
        public virtual string EXISTS_SALARY_ADVANCE { get; set; }
        public virtual string AMOUNT_SALARY_ADVANCE { get; set; }
        public virtual string EXISTS_LOAN_PAYBACK { get; set; }
        public virtual string AMOUNT_LOAN_PAYBACK { get; set; }
        public virtual string EXISTS_COST_PAYMENT { get; set; }
        public virtual string AMOUNT_COST_PAYMENT { get; set; }
        public virtual string BLOCKED_EMPLOYEE_DISCOUNT { get; set; }
        public virtual string TEST_DEVICES_TRACKED { get; set; }
        public virtual decimal? INSAN_KAYNAKLARI_DOLDURAN { get; set; }
        public virtual DateTime? INSAN_KAYNAKLARI_DOLUM_TARIH { get; set; }
        public virtual decimal? IDARI_ISLER_DOLDURAN { get; set; }
        public virtual DateTime? IDARI_ISLER_DOLUM_TARIH { get; set; }
        public virtual decimal? YARDIM_MASASI_DOLDURAN { get; set; }
        public virtual DateTime? YARDIM_MASASI_DOLUM_TARIH { get; set; }
        public virtual decimal? GUVENLIK_DOLDURAN { get; set; }
        public virtual DateTime? GUVENLIK_DOLUM_TARIHI { get; set; }
        public virtual decimal? KIRTASIYE_DOLDURAN { get; set; }
        public virtual DateTime? KIRTASIYE_DOLUM_TARIH { get; set; }
        public virtual decimal? MUHASEBE_DOLDURAN { get; set; }
        public virtual DateTime? MUHASEBE_DOLUM_TARIH { get; set; }
        public virtual decimal? PAZARLAMA_DOLDURAN { get; set; }
        public virtual DateTime? PAZARLAMA_DOLUM_TARIH { get; set; }
        public virtual decimal? SET_TOP_BOX_DOLDURAN { get; set; }
        public virtual DateTime? SET_TOP_BOX_DOLUM_TARIH { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        #endregion Entity Properties
    }
}
