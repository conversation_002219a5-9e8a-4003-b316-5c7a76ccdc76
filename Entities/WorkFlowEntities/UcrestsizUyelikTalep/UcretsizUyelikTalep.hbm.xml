<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="UcretsizUyelikTalep, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_UYELIK_TALEP" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="TalepTip" column="TALEP_TIP" type="string" />
    <property name="UyeNo" column="UYE_NO" type="string" />
    <property name="UyeAdSoyad" column="UYE_AD_SOYAD" type="string" />
    <property name="UyeCepTel" column="UYE_CEP_TEL" type="string" />
    <property name="YayinTipi" column="YAYIN_TIPI" type="string" />
    <property name="YayinBaslangicTarih" column="YAYIN_BASLANGIC_TARIH" type="DateTime" />
    <property name="YayinBitisTarih" column="YAYIN_BITIS_TARIH" type="DateTime" />
    <property name="YayinSuresi" column="YAYIN_SURESI" type="string" />
    <property name="TalepNedeni" column="TALEP_NEDENI" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionID" column="VERSION_ID" type="long" />
  </class>
</hibernate-mapping>

