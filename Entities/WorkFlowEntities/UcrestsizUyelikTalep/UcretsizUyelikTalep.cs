using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class UcretsizUyelikTalep : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string TalepTip { get; set; }
        public virtual string UyeNo { get; set; }
        public virtual string UyeAdSoyad { get; set; }
        public virtual string UyeCepTel { get; set; }
        public virtual string YayinTipi { get; set; }
        public virtual DateTime YayinBaslangicTarih { get; set; }
        public virtual DateTime YayinBitisTarih { get; set; }
        public virtual string YayinSuresi { get; set; }
        public virtual string Talep<PERSON><PERSON>ni { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}

