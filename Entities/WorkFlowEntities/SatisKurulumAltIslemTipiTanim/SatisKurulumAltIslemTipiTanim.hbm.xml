<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SatisKurulumAltIslemTipiTanim, Digiturk.Workflow.Digiflow.Entities" table="DF_ALT_ISLEM_TIP_TANIM" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ALT_ISLEM_TIP_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="AnaIslemTipId" column="ANA_ISLEM_TIP_ID" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="EkrandaGoster" column="EKRANDA_GOSTER" />    

    <property name="CreatedBy" column="CREATED_BY" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="LastUpdated" column="LAST_UPDATED" /> 
    <property name="VersionID" column="VERSION_ID" />

  </class>
</hibernate-mapping>