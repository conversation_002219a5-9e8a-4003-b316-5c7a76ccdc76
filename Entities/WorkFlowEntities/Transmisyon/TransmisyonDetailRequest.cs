using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TransmisyonDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long ParticipantLoginId { get; set; }
        public virtual string Marka { get; set; }
        public virtual string Model { get; set; }
        public virtual string SeriNo { get; set; }
        public virtual string Lokasyon { get; set; }
        public virtual long TahminiMaliyet { get; set; }
        public virtual string ParaBirimi { get; set; }
        public virtual string ArizaAciklama { get; set; }
        public virtual long Adet { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}