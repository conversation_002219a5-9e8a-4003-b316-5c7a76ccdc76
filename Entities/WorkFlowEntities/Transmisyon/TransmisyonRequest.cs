﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TransmisyonRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual DateTime RequestTime { get; set; }
        public virtual long Garantili { get; set; }
        public virtual DateTime RaporTarihi { get; set; }
        public virtual DateTime ArizaTarihi { get; set; }
        public virtual string IlgiliRaporTRId { get; set; }
        public virtual string DosyaEkleri { get; set; }
        public virtual long WFSecenek { get; set; }
        public virtual string FirmaAdi { get; set; }
        public virtual long Telefon { get; set; }
        public virtual string FirmaYetkili { get; set; }
        public virtual long TahminiDonusSuresi { get; set; }
        public virtual string GecikmeNedeni { get; set; }
        public virtual string Adres { get; set; }
        public virtual string IlgiliSatinAlma { get; set; }
        public virtual long Gonder { get; set; }
        public virtual DateTime GonderimTarihi { get; set; }
        public virtual string TestBulgulari { get; set; }
        public virtual string TestBulgulariDocURL { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long VersionID { get; set; }
    }
}