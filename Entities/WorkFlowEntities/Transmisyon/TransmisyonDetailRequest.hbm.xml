<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="TransmisyonDetailRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TRANS_DETAILREQUEST" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="TRANSMISYON_DETAIL_REQUEST_ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
        <property name="ParticipantLoginId" column="PARTICIPANT_LOGIN_ID" />
        <property name="Marka" column="MARKA" />
        <property name="Model" column="MODEL" />
        <property name="SeriNo" column="SERINO" />
        <property name="Lokasyon" column="LOKASYON" />
        <property name="TahminiMaliyet" column="TAHMINI_MALIYET" />
        <property name="ParaBirimi" column="PARA_BIRIMI" />
        <property name="ArizaAciklama" column="ARIZA_ACIKLAMA" />
        <property name="Adet" column="ADET" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="VersionID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>