﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TransmisyonRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TRANSMISYON_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="TRANSMISYON_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="RequestTime" column="REQUEST_TIME" />
    <property name="Garantili" column="GARANTILI" />
    <property name="RaporTarihi" column="RAPOR_TARIHI" />
    <property name="ArizaT<PERSON>hi" column="ARIZA_TARIHI" />
    <property name="IlgiliRaporTRId" column="ILGILI_RAPOR_TRID" />
    <property name="DosyaEkleri" column="DOSYA_EKLERI" />
    <property name="WFSecenek" column="WF_SECENEK" />
    <property name="FirmaAdi" column="FIRMA_ADI" />
    <property name="Telefon" column="TELEFON" />
    <property name="FirmaYetkili" column="FIRMA_YETKILI" />
    <property name="TahminiDonusSuresi" column="TAHMINI_DONUS" />
    <property name="GecikmeNedeni" column="GECIKME_NEDENI" />
    <property name="Adres" column="ADRES" />
    <property name="IlgiliSatinAlma" column="ILGILI_SATINALMA" />
    <property name="Gonder" column="GONDER" />
    <property name="GonderimTarihi" column="GONDERIM_TARIHI" />
    <property name="TestBulgulari" column="TEST_BULGULARI" />
    <property name="TestBulgulariDocURL" column="TEST_BULGULARI_DOCURL" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>