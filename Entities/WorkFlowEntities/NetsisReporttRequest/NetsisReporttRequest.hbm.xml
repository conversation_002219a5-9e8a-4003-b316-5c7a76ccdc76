﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="NetsisReporttRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_NETSIS_REPORT_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="NETSIS_R_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="DepKontrol" column="DEP_KONTROL" />
    <property name="Hazkontrol" column="HAZ_KONTROL" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
	<property name="RaporNo" column="RAPOR_NO" />
	<property name="InsertFileName" column="INSERT_FILE_NAME"></property>
  </class>
</hibernate-mapping>