﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="NetsisReporttDetailRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_NETSIS_R_DETAIL_REQUEST" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="NETSIS_DETAIL_R_REQUEST_ID">
			<generator class="trigger-identity"></generator>
		</id>
		<property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
		<property name="CariKod" column="CARI_KOD" />
		<property name="CariIsim" column="CARI_ISIM" />
		<property name="GrupKod" column="GRUP_KOD" />
		<property name="GrupIsim" column="GRUP_ISIM" />
		<property name="Onceki" column="ONCEKI" />
		<property name="P1" column="P1" />
		<property name="P2" column="P2" />
		<property name="P3" column="P3" />
		<property name="P4" column="P4" />
		<property name="P5" column="P5" />
		<property name="P6" column="P6" />
		<property name="P7" column="P7" />
		<property name="P8" column="P8" />
		<property name="Toplam" column="TOPLAM" />
		<property name="VadesiGelenBorc" column="VADE_BORC" />
		<property name="DepStopaj" column="DEP_STOPAJ" />
		<property name="DepTutar" column="DEP_TUTAR" />
		<property name="DepAciklama" column="DEP_ACIKLAMA" />
		<property name="DepUser" column="DEP_KULLANICI" />
		<property name="HazTutar" column="HAZ_TUTAR" />
		<property name="HazAciklama" column="HAZ_ACIKLAMA" />
		<property name="HazUser" column="HAZ_KULLANICI" />
		<property name="CariIban" column="CARI_IBAN" />
		<property name="FirmaHesapKod" column="FIRMA_HESAP_KOD" />
		<property name="DovizTip" column="DOVIZTIPI" />
		<property name="DokumanLink" column="DOK_LINK" />
		<property name="IslemTip" column="ISLEM_TIP" />
		<property name="DpOnayDurum" column="DP_ONAY_DURUM" />
		<property name="Created" column="CREATED" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
		<property name="VersionID" column="VERSION_ID" />
		<property name="Sirket" column="SIRKET" />
		<property name="Yurtdisimi" column="YURTDISIMI" />
		<property name="DepKontrol" column="DEP_KONTROL" />
		<property name="HazCari" column="HAZ_CARI" />
		<property name="HazOdemeTarih" column="HAZ_ODEME_TARIH" />
		<property name="IncKeyno" column="INC_KEY_NO" />
		<property name="TlVadeBorc" column="TLVADE_BORC" />
		<property name="TlToplam" column="TLTOPLAM" />
	</class>
</hibernate-mapping>