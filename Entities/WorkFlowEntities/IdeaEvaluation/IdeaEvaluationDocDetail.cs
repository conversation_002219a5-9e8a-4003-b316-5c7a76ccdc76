﻿using System;
using Digiturk.Workflow.Entities;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class IdeaEvaluationDocDetail : EntityBase, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string FileName { get; set; }
        public virtual string AttachLink { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
    }
}
