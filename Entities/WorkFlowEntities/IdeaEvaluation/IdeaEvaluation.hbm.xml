﻿<?xml version="1.0" encoding="utf-8"?>		
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="IdeaEvaluation,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_IDEA_EVALUATION" schema="DT_WORKFLOW">

		<id name="RequestId" type="long" column="IDEA_EVALUATION_REQUEST_ID">
			<generator class="trigger-identity"/>
		</id>

		<property name="IdeaName" column="IDEA_NAME"/>
		<property name="Category" column="CATEGORY"/>
		<property name="ProblemOpportunity" column="PROBLEM_OPPORTUNITY"/>
		<property name="DetailedDescription" column="DETAILED_DESCRIPTION"/>
		<property name="OtherContributors" column="OTHER_CONTRIBUTORS"/>
		<property name="CEOApprovalRequired" column="CEO_APPROVAL_REQUIRED"/>
		<property name="Created" column="CREATED"/>
		<property name="LastUpdated" column="LAST_UPDATED"/>
		<property name="CreatedBy" column="CREATED_BY"/>
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY"/>
		<property name="VersionID" column="VERSION_ID"/>

	</class>
</hibernate-mapping>
