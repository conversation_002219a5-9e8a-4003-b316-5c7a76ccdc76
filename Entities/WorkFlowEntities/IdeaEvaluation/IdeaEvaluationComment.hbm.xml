﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="IdeaEvaluationComment,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_IDEA_EVALUATION_COMMENTS" schema="DT_WORKFLOW">
		<id name="CommentId" type="long" column="COMMENT_ID">
			<generator class="trigger-identity"/>
		</id>
		<property name="RequestId" column="REQUEST_ID"/>
		<property name="FLoginId" column="F_LOGIN_ID"/>
		<property name="EvaluationStatus" column="EVALUATION_STATUS"/>
		<property name="CommentText" column="COMMENT_TEXT"/>
		<property name="Created" column="CREATED"/>
		<property name="LastUpdated" column="LAST_UPDATED"/>
		<property name="CreatedBy" column="CREATED_BY"/>
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY"/>
		<property name="VersionID" column="VERSION_ID"/>
	</class>
</hibernate-mapping>
