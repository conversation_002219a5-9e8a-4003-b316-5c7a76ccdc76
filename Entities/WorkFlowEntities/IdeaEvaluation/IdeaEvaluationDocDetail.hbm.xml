﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="IdeaEvaluationDocDetail,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_IDEA_EVALUATION_DETAIL_DOC" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="DETAIL_REQUEST_ID">
			<generator class="trigger-identity"/>
		</id>
		<property name="RelatedRequestID" column="RELATED_REQUEST_ID"/>
		<property name="FileName" column="FILE_NAME"/>
		<property name="AttachLink" column="ATTACHMENT_LINK"/>
		<property name="Created" column="CREATED"/>
		<property name="LastUpdated" column="LAST_UPDATED"/>
		<property name="CreatedBy" column="CREATED_BY"/>
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY"/>
		<property name="VersionID" column="VERSION_ID"/>
	</class>
</hibernate-mapping>
