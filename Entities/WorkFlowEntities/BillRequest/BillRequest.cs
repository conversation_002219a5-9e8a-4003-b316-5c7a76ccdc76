﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BillRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual string BillInfo { get; set; }
        public virtual string BillRequester { get; set; }
        public virtual string RequesterAddress { get; set; }
        public virtual string TaxOffice { get; set; }
        public virtual string TaxNumber { get; set; }
        public virtual decimal BillAmount { get; set; }
        public virtual string BillAmountCurrency { get; set; }
        public virtual decimal ExchangeRateBuy { get; set; }
        public virtual decimal ExchangeRateSell { get; set; }
        public virtual string CollectingType { get; set; }
        public virtual string PaymentDay { get; set; }
        public virtual string BillInfoNameSurname { get; set; }
        public virtual string BillInfoPhone { get; set; }
        public virtual char IsContract { get; set; }
        public virtual char IsOther { get; set; }
        public virtual string CompanyName { get; set; }
        public virtual DateTime RequestDate { get; set; }
        public virtual string InLettering { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        public virtual DateTime ContractDate { get; set; }

        public virtual DateTime PaymentDate { get; set; }
        public virtual DateTime ServiceSDate { get; set; }
        public virtual DateTime ServiceEDate { get; set; }
        public virtual string BillType { get; set; }
        public virtual string BillInfoEmail { get; set; }
        public virtual string AccountCode { get; set; }
        
    }
}