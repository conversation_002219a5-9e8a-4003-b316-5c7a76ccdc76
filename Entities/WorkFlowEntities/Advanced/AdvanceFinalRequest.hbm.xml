<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="AdvanceFinalRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ADVANCE_FINALREQUEST" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="ADVANCE_FINAL_REQUEST_ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
        <property name="WfInstanceId" column="WF_INSTANCEID" />
        <property name="PaymentDate" column="PAYMENT_DATE" />
        <property name="PaymentPrice" column="PAYMENT_PRICE" />
        <property name="PaymentType" column="PAYMENT_TYPE" />
        <property name="ApproveDate" column="APPROVE_DATE" />
        <property name="AdvancePriceCurrently" column="ADVANCE_PRICE_CURRENTLY" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="VersionID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>