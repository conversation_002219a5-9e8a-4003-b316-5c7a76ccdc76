<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SatisIptalChurnRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_STIPTCHR_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="PERIOD" column="PERIOD" />
    <property name="BASLANGIC_TARIH" column="BASLANGIC_TARIH" />
    <property name="BITIS_TARIH" column="BITIS_TARIH" />
    <property name="SATIS_ADET" column="SATIS_ADET" />
    <property name="IPTAL_ADET" column="IPTAL_ADET" />
    <property name="CHURN_ADET" column="CHURN_ADET" />
    <property name="ONAY2" column="ONAY2" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>