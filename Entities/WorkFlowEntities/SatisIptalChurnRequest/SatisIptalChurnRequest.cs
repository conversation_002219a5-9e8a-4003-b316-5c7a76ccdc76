using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SatisIptalChurnRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string PERIOD { get; set; }
        public virtual DateTime BASLANGIC_TARIH { get; set; }
        public virtual DateTime BITIS_TARIH { get; set; }
        public virtual long SATIS_ADET { get; set; }
        public virtual long IPTAL_ADET { get; set; }
        public virtual long CHURN_ADET { get; set; }
        public virtual long ONAY2 { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}