﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="AracTakipRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ARAC_TAKIP_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ARAC_TAKIP_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="Bolge" column="BOLGE" />
    <property name="C<PERSON>s_Tarihi" column="CIKIS_TARIHI" />
    <property name="Cikis_Saati" column="CIKIS_SAATI" />
    <property name="Donus_Tarihi" column="DONUS_TARIHI" />
    <property name="Donus_Saati" column="DONUS_SAATI" />
    <property name="Talep_Nedeni" column="TALEP_NEDENI" />
    <property name="Verilen_Arac" column="VERILEN_ARAC" />
    <property name="Verilen_Arac_Plaka" column="VERILEN_ARAC_PLAKA" />
    <property name="Request_User_Id" column="REQUEST_USER_ID" />
    
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
 
  </class>
</hibernate-mapping>
