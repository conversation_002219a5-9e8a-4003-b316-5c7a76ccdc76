﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SatisKurulumDegerlendirmeDoc,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SATIS_DEGERLENDIRME_DOC" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />

    <property name="DokumanLink" column="DOKUMAN_LINK" />
    <property name="DokumanAd" column="DOKUMAN_AD" />
	  <property name="DokumanAciklama" column="DOKUMAN_ACIKLAMA" />

  	<property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>

