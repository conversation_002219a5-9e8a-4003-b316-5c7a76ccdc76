using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class CampaignOfferlist : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual long KmpTypeId { get; set; }
        public virtual string PacketKmpCode { get; set; }
        public virtual string PacketId { get; set; }//WIZ_PRODUCT_CODES-->PRODUCT_CODE CHAR(3)
        public virtual string FranchiseId { get; set; }//WIZ_FRAN_SYS -->FRANCHISE_CODE CHAR(3 BYTE)
        public virtual string BrandId { get; set; } //vv_stb_tipi -->KOD VARCHAR2 (2 BYTE)
        public virtual string SubscriberTypeId { get; set; }//WIZ_CUSTOMER_TYPE_CODES -->CUSTOMER_TYPE (CHAR 3 BYTE)
        public virtual string FrequencyId { get; set; }//WIZ_BILLING_FREQUENCY_CODES -->BILLING_FREQ_NUMBER (NUMBER)
        public virtual string ListPrice { get; set; }
        public virtual string CommitmentPrice { get; set; }
        public virtual string PriceTypeId { get; set; } //VV_PROMOSYON_TIPI -->TIP_KODU (NUMBER(5))
        public virtual string Time { get; set; }
        public virtual long DiscountPercent { get; set; }
        public virtual DateTime? ConstantDate { get; set; }
        public virtual string Explanation { get; set; }
        public virtual string TahhutBasKmpKodu { get; set; }

        #endregion Entity Properties
    }
}