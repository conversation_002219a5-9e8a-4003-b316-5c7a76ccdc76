﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="CampaignOfferlist, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CAMPAIGN_OFFERLIST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CAMPAIGN_OFFERLIST_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />

    <property name="KmpTypeId" column="KMP_TYPE_ID" />
    <property name="PacketKmpCode" column="PACKET_KMP_KODE" />
    <property name="PacketId" column="PACKET_ID" />
    <property name="FranchiseId" column="FRANCHISE_ID" />
    <property name="BrandId" column="BRAND_ID" />
    <property name="SubscriberTypeId" column="SUBSCRIBER_TYPE_ID" />
    <property name="FrequencyId" column="FREQUENCY_ID" />
    <property name="ListPrice" column="LIST_PRICE" />
    <property name="CommitmentPrice" column="COMMITMENT_PRICE" />
    <property name="PriceTypeId" column="PRICE_TYPE_ID" />
    <property name="Time" column="TIME" />
    <property name="DiscountPercent" column="DISCOUNT_PERCENT" />
    <property name="ConstantDate" column="CONSTANT_DATE" />
    <property name="Explanation" column="EXPLANATION" />
    <property name="TahhutBasKmpKodu" column="TAAHHUT_BAS_KMP_KODU" />
  </class>
</hibernate-mapping>