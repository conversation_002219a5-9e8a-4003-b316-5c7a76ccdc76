using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class OTTUcretsizYayinFormuDetay : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string UyeNo { get; set; }
        public virtual string UyeCepTel { get; set; }
        public virtual string UyeEposta { get; set; }
        public virtual string UyelikTipi { get; set; }
        public virtual string PaketTipi { get; set; }
        public virtual string Talep<PERSON><PERSON>ni { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string UyeAdSoyad { get; set; }
        public virtual string YoneticiSec { get; set; }

        #endregion Entity Properties
    }
}

