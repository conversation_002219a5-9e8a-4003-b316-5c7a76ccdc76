<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="OTTUcretsizYayinFormuDetay, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_OTT_UCRETSIZ_YAYIN_DETAY" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" type="string" />
    <property name="UyeNo" column="UYE_NO" />   
    <property name="UyeCepTel" column="UYE_CEP_TEL" />
    <property name="UyeEposta" column="UYE_EPOSTA" />
    <property name="UyelikTipi" column="UYELIK_TIPI" />
    <property name="UyeAdSoyad" column="UYE_ADSOYAD" />
    <property name="YoneticiSec" column="YONETICI_SEC" />
    <property name="PaketTipi" column="PAKET_TIPI" />
    <property name="TalepNedeni" column="TALEP_NEDENI" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>
