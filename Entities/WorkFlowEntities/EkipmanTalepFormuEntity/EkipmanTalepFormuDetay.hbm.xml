﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="EkipmanTalepFormuDetay , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EKIPMAN_TALEP_FORMU_DETAY" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="EKIPMAN_TALEP_FORM_ID" />
    <property name="Ekipman_Adet" column="EKIPMAN_ADET" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="Yts_Id" column="YTS_ID" />
    <property name="Malzeme_Id" column="MALZEME_ID" />       

  </class>
</hibernate-mapping>

