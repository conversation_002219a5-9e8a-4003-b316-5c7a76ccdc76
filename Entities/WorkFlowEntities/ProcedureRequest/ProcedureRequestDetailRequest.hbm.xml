﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ProcedureRequestDetail,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PROCEDURE_REQUEST_DETAIL" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="PROCEDURE_REQUEST_DETAIL_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="DOKUMAN_ACIKLAMA" column="DOKUMAN_ACIKLAMA" />
    <property name="DOKUMAN_PATH" column="DOKUMAN_PATH" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>