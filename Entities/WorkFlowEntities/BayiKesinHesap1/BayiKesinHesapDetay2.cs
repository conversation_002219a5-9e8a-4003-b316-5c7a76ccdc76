﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiKesinHesapDetay2 : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long RelatedDetailRequestID { get; set; }        
        public virtual decimal Borc { get; set; }
        public virtual decimal Alacak { get; set; }
        public virtual decimal Tutar { get; set; }
        public virtual string Durum { get; set; }
        public virtual string SatisYetkilisiYorum { get; set; }
        public virtual string FinansYetkilisiYorum { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }


    }
}
