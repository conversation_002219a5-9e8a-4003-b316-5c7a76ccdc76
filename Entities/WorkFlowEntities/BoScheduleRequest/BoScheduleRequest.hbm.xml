<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BoScheduleRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BO_SCHEDULE" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
 <property name="RAPOR_AMACI" column="RAPOR_AMACI" />
 <property name="RAPOR_ADI_KLASORU" column="RAPOR_ADI_KLASORU" />
 <property name="RAPOR_SORGU_SECIM" column="RAPOR_SORGU_SECIM" />
 <property name="ORNEK_ISIM" column="ORNEK_ISIM" />
 <property name="FILTRE_ALAN" column="FILTRE_ALAN" />
 <property name="FILTRE_DEGER" column="FILTRE_DEGER" />
 <property name="RAPOR_BICIM" column="RAPOR_BICIM" />
 <property name="RAPOR_EVENT" column="RAPOR_EVENT" />
 <property name="RAPOR_YOLLANMA_HEDEF" column="RAPOR_YOLLANMA_HEDEF" />
 <property name="RAPOR_MAIL_FROM" column="RAPOR_MAIL_FROM" />
 <property name="RAPOR_MAIL_TO" column="RAPOR_MAIL_TO" />
 <property name="RAPOR_MAIL_CC" column="RAPOR_MAIL_CC" />
 <property name="RAPOR_MAIL_BCC" column="RAPOR_MAIL_BCC" />
 <property name="RAPOR_MAIL_SUBJECT" column="RAPOR_MAIL_SUBJECT" />
 <property name="RAPOR_MESSAGE" column="RAPOR_MESSAGE" />
 <property name="RAPOR_MAIL_ISMINI_KULLAN" column="RAPOR_MAIL_ISMINI_KULLAN" />
 <property name="RAPOR_MAIL_YENIDEN_ISIMLENDIR" column="RAPOR_MAIL_YENIDEN_ISIMLENDIR" />
 <property name="RAPOR_BI_KIME" column="RAPOR_BI_KIME" />
 <property name="RAPOR_BI_ISMINI_KULLAN" column="RAPOR_BI_ISMINI_KULLAN" />
 <property name="RAPOR_BI_YENIDEN_ISIMLENDIR" column="RAPOR_BI_YENIDEN_ISIMLENDIR" />
 <property name="RAPOR_BI_FARKLI_GONDER" column="RAPOR_BI_FARKLI_GONDER" />
 <property name="RAPOR_SIK_GUN" column="RAPOR_SIK_GUN" />
 <property name="RAPOR_SIK_GUN_DEGER" column="RAPOR_SIK_GUN_DEGER" />
 <property name="RAPOR_SIK_AY" column="RAPOR_SIK_AY" />
 <property name="RAPOR_SIK_AY_DEGER" column="RAPOR_SIK_AY_DEGER" />
 <property name="RAPOR_SIK_AY_N" column="RAPOR_SIK_AY_N" />
 <property name="RAPOR_SIK_AY_N_DEGER" column="RAPOR_SIK_AY_N_DEGER" />
 <property name="RAPOR_SIK_AY_SON" column="RAPOR_SIK_AY_SON" />
 <property name="RAPOR_SIK_SIMDI" column="RAPOR_SIK_SIMDI" />
 <property name="RAPOR_SIK_AY_N_GUN" column="RAPOR_SIK_AY_N_GUN" />
 <property name="RAPOR_SIK_AY_N_HAFTA" column="RAPOR_SIK_AY_N_HAFTA" />
 <property name="RAPOR_SIK_AY_N_HAFTA_GUN" column="RAPOR_SIK_AY_N_HAFTA_GUN" />
 <property name="RAPOR_SIK_SAAT" column="RAPOR_SIK_SAAT" />
 <property name="RAPOR_SIK_SAAT_N" column="RAPOR_SIK_SAAT_N" />
 <property name="RAPOR_SIK_SAAT_DAKIKA_N" column="RAPOR_SIK_SAAT_DAKIKA_N" />
 <property name="RAPOR_SIK_ILK_PAZARTESI" column="RAPOR_SIK_ILK_PAZARTESI" />
 <property name="RAPOR_SIK_HAFTALIK" column="RAPOR_SIK_HAFTALIK" />
 <property name="RAPOR_SIK_HAFTALIK_N" column="RAPOR_SIK_HAFTALIK_N" />
   
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    
  </class>
</hibernate-mapping>