﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class CIPRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual string RequestType { get; set; }
        public virtual DateTime RequestDate { get; set; }
        public virtual string SubscrbNo { get; set; }
        public virtual string SubscrbName { get; set; }
        public virtual string Tckn { get; set; }
        public virtual string CellPhone { get; set; }
        public virtual string Job { get; set; }
        public virtual string Position { get; set; }
        public virtual string Description { get; set; }
        public virtual string ContactPerson { get; set; }
        public virtual string CipFeature { get; set; }
        public virtual string RequestReason { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}