﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="OldPurchaseUseRequestDetail,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_OLD_PURCHASE_USE_DET_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_DETAIL_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="PurchaseId" column="PURCHASE_ID" />
    <property name="Description" column="DESCRIPTION" />
    <property name="Link" column="LINK" />
    <property name="Ka<PERSON>Bakiye" column="KALAN_BAKIYE" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>