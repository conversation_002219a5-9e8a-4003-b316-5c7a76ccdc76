﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BillReturnApproveRequestRequest : EntityBase
    {
        #region Entity Properties

        public virtual long RequestID { get; set; }
        public virtual long LoginId { get; set; }
        public virtual DateTime RequestDate { get; set; }
        public virtual string CompaignCode { get; set; }
        public virtual string BudgetCode { get; set; }
        public virtual string FormId { get; set; }
        public virtual string BillSubject { get; set; }
        public virtual string PersonOrCompanyName { get; set; }
        public virtual long BillTypeId { get; set; }
        public virtual string BillDescription { get; set; }
        public virtual decimal BillAmount { get; set; }
        public virtual string BillCurrency { get; set; }
        public virtual string BillAmountText { get; set; }
        public virtual string ExchangeRateBuy { get; set; }
        public virtual string ExchangeRateSell { get; set; }
        public virtual long IsPurchaseRequest { get; set; }
        public virtual long IsContract { get; set; }
        public virtual long IsDeliveryForm { get; set; }
        public virtual long IsDispatch { get; set; }
        public virtual DateTime DispatchDate { get; set; }
        public virtual string DispatchNo { get; set; }
        public virtual long IsBill { get; set; }
        public virtual DateTime BillDate { get; set; }
        public virtual string BillNo { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}