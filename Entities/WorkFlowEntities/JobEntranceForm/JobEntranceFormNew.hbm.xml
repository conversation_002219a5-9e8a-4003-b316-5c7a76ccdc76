﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="JobEntranceFormNew,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_ENTRANCE_NEW" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="JOB_ENTRANCE_NEW_REQUEST_ID">
			<generator class="trigger-identity"></generator>
		</id>

		<property name="AdiSoyadi" column="ADI_SOYADI" />
		<property name="IseBaslamaTarihi" column="ISE_BASLAMA_TARIHI" />
		<property name="Division" column="DIVISION" />
		<property name="Department" column="DEPARTMENT" />
		<property name="Unit" column="UNIT" />
		<property name="Team" column="TEAM" />
		<property name="ManagerId" column="MANAGER_ID" />
		<property name="Location" column="LOCATION" />
		<property name="PersonalTypeId" column="PERSONAL_TYPE_ID" />
		<property name="Floor" column="FLOOR" />
		<property name="UserName" column="USERNAME" />
		<property name="BagliOlduguFirmaHRId" column="FIRMA_HR_ID_VAL" />
		<property name="GsmTarifeId" column="GSM_TARIFE_ID" />
		<property name="MailGroup" column="MAIL_GROUP" />
		<property name="BilgisayarVerilecekmi" column="BILGISAYARVERILECEKMI" />
		<property name="BilgisayarTipi" column="BILGISAYAR_TIPI" />
		<property name="MasaTelefonuVerilecekmi" column="MASATELEFONUVERILECEKMI" />
		<property name="YakaKartiVerilecekmi" column="YAKAKARTIVERILECEKMI" />
		<property name="SirketAraciVerilecekmi" column="SIRKETARACIVERILECEKMI" />
		<property name="TasitmatikVerilecekmi" column="TASITMATIKVERILECEKMI" />
		<property name="OtoparkVerilecekmi" column="OTOPARKVERILECEKMI" />
		<property name="CepTelefonuVerilecekmi" column="CEPTELEFONUVERILECEKMI" />
		<property name="GSMHatVerilecekmi" column="GSMHATVERILECEKMI" />
		<property name="GerekliEvraklarVarmi" column="GEREKLIEVRAKLARVARMI" />
		<property name="PoldyTanimlandi" column="POLDYTANIMLANDI" />
		<property name="SGKIseGirisYapildi" column="SGKISEGIRISYAPILDI" />
		<property name="BankaKartiBasvuru" column="BANKAKARTIBASVURU" />
		<property name="IntranetYapildi" column="INTRANETYAPILDI" />
		<property name="SaglikSigortasiKayit" column="SAGLIKSIGORTASIKAYIT" />
		<property name="SodexoKayit" column="SODEXOKAYIT" />
		<property name="MasaTelefonuAtandi" column="MASATELEFONUATANDI" />
		<property name="EMailAccountAtandi" column="EMAILACCOUNTATANDI" />
		<property name="BilgisayarAtandi" column="BILGISAYARATANDI" />
		<property name="ZimmetImzalandi" column="ZIMMETIMZALANDI" />
		<property name="YakaKartiTanimlandi" column="YAKAKARTITANIMLANDI" />
		<property name="SirketAracTahsisi" column="SIRKETARACTAHSISI" />
		<property name="OtoparkYerAyarlama" column="OTOPARKYERAYARLAMA" />
		<property name="GSMHatAtandi" column="GSMHATATANDI" />
		<property name="CepTelefonuTahsisi" column="CEPTELEFONUTAHSISI" />
		<property name="TasitmatikTahsisi" column="TASITMATIKTAHSISI" />
		<property name="UserNameCreate" column="USERNAMECREATE" />
		<property name="InformationEducation" column="INFORMATIONEDUCATION" />
		<property name="TechOutlook" column="TECHOUTLOOK" />
		<property name="MasaDuzeniElektrik" column="MASADUZENIELEKTRIK" />
		<property name="YakaKartiCikartildi" column="YAKAKARTICIKARTILDI" />
		<property name="GuvenlikBilgilendirildi" column="GUVENLIKBILGILENDIRILDI" />
		<property name="IseBaslamaKitiGonderildi" column="ISEBASLAMAKITIGONDERILDI" />

		<property name="IkIsOrtagi" column="IKISORTAGI" />
		<property name="DogumTarihi" column="DOGUM_TARIHI" />
		<property name="TelefonNo" column="TELEFON_NO" />
		<property name="MailAdresi" column="MAIL_ADRESI" />
		<property name="KiminYerine" column="KIMIN_YERINE" />
		<property name="Pozisyonu" column="POZISYONU" />
		<property name="SozlemeTipi" column="SOZLESME_TIPI" />
		<property name="EmekliDurumu" column="EMEKLI_DURUMU" />

		<property name="DigiBuddyAdSoyad" column="DIGI_BUDDY_ADSOYAD" />
		<property name="DigiBuddyTelno" column="DIGI_BUDDY_TELNO" />
		<property name="DigiBuddyMail" column="DIGI_BUDDY_MAIL" />
		<property name="OzelNotlar" column="OZEL_NOTLAR" />
		<property name="IkAciklama" column="IK_ACIKLAMA" />
		<property name="YoneticiToplanti" column="YONETICI_TOPLANTI" />
		<property name="YoneticiYetkiEkipman" column="YONETICI_YETKI_EKIPMAN" />
		<property name="PtfId" column="PTF_ID" />

		<property name="IK_SON_GT1" column="IK_SON_GT1" />
		<property name="IK_SON_YK1" column="IK_SON_YK1" />
		<property name="IK_SON_GT2" column="IK_SON_GT2" />
		<property name="IK_SON_YK2" column="IK_SON_YK2" />

		<property name="IKIlkOnayIslem" column="IK_ILK_ONAY_ISLEM" />
		<property name="IKIlkOnayTarih" column="IK_ILK_ONAY_TARIH" />
		<property name="IKBordroOnayIslem" column="IK_BORDRO_ONAY_ISLEM" />
		<property name="IKBordroOnayTarih" column="IK_BORDRO_ONAY_TARIH" />
		<property name="IdariIslerIslem" column="IDARI_ISLER_ISLEM" />
		<property name="IdariIslerTarih" column="IDARI_ISLER_TARIH" />
		<property name="GuvenlikIslem" column="GUVENLIK_ISLEM" />
		<property name="GuvenlikTarih" column="GUVENLIK_TARIH" />
		<property name="YardimMasasiIslem" column="YARDIM_MASASI_ISLEM" />
		<property name="YardimMasasiTarih" column="YARDIM_MASASI_TARIH" />
		<property name="KirtasiyeIslem" column="KIRTASIYE_ISLEM" />
		<property name="KirtasiyeTarih" column="KIRTASIYE_TARIH" />
		<property name="YoneticiOnayIslem" column="YONETICI_ONAY_ISLEM" />
		<property name="YoneticiOnayTarih" column="YONETICI_ONAY_TARIH" />
		<property name="IKSonOnayIslem" column="IK_SON_ONAY_ISLEM" />
		<property name="IKSonOnayTarih" column="IK_SON_ONAY_TARIH" />

		<property name="Created" column="CREATED" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
		<property name="VersionID" column="VERSION_ID" />
	</class>
</hibernate-mapping>