using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobEntranceFormKartvizit : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }

        public virtual string Departman { get; set; }
        public virtual string GSMNO { get; set; }
        public virtual string Dahili { get; set; }
        public virtual long Adet { get; set; }
        public virtual string Dil { get; set; }
        public virtual string Faks { get; set; }

        public virtual long Status { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long Version_ID { get; set; }

        #endregion Entity Properties
    }
}