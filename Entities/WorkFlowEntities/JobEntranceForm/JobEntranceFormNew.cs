﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobEntranceFormNew : EntityBase, IEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual string AdiSoyadi { get; set; }
        public virtual DateTime IseBaslamaTarihi { get; set; }
        public virtual long? Division { get; set; }
        public virtual long? Department { get; set; }
        public virtual long? Unit { get; set; }
        public virtual long? Team { get; set; }
        public virtual long ManagerId { get; set; }
        public virtual string Location { get; set; }
        public virtual string UserName { get; set; }
        public virtual long PersonalTypeId { get; set; }
        public virtual string Floor { get; set; }
        public virtual string BagliOlduguFirmaHRId { get; set; }
        public virtual long GsmTarifeId { get; set; }
        public virtual string MailGroup { get; set; }
        public virtual long BilgisayarVerilecekmi { get; set; }
        public virtual long BilgisayarTipi { get; set; }
        public virtual long MasaTelefonuVerilecekmi { get; set; }
        public virtual long YakaKartiVerilecekmi { get; set; }
        public virtual long SirketAraciVerilecekmi { get; set; }
        public virtual long TasitmatikVerilecekmi { get; set; }
        public virtual long OtoparkVerilecekmi { get; set; }
        public virtual long CepTelefonuVerilecekmi { get; set; }
        public virtual long GSMHatVerilecekmi { get; set; }
        public virtual long GerekliEvraklarVarmi { get; set; }
        public virtual long PoldyTanimlandi { get; set; }
        public virtual long SGKIseGirisYapildi { get; set; }
        public virtual long BankaKartiBasvuru { get; set; }
        public virtual long IntranetYapildi { get; set; }
        public virtual long SaglikSigortasiKayit { get; set; }
        public virtual long SodexoKayit { get; set; }
        public virtual long MasaTelefonuAtandi { get; set; }
        public virtual long EMailAccountAtandi { get; set; }
        public virtual long BilgisayarAtandi { get; set; }
        public virtual long ZimmetImzalandi { get; set; }
        public virtual long YakaKartiTanimlandi { get; set; }
        public virtual long SirketAracTahsisi { get; set; }
        public virtual long TasitmatikTahsisi { get; set; }
        public virtual long OtoparkYerAyarlama { get; set; }
        public virtual long GSMHatAtandi { get; set; }
        public virtual long UserNameCreate { get; set; }
        public virtual long InformationEducation { get; set; }
        public virtual long TechOutlook { get; set; }
        public virtual long CepTelefonuTahsisi { get; set; }
        public virtual long MasaDuzeniElektrik { get; set; }
        public virtual long YakaKartiCikartildi { get; set; }
        public virtual long GuvenlikBilgilendirildi { get; set; }
        public virtual long IseBaslamaKitiGonderildi { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }


        public virtual long IkIsOrtagi { get; set; }
        public virtual DateTime DogumTarihi { get; set; }
        public virtual string TelefonNo { get; set; }
        public virtual string MailAdresi { get; set; }
        public virtual string KiminYerine { get; set; }
        public virtual string Pozisyonu { get; set; }
        public virtual string SozlemeTipi { get; set; }
        public virtual string EmekliDurumu { get; set; }

        public virtual string DigiBuddyAdSoyad { get; set; }
        public virtual string DigiBuddyTelno { get; set; }
        public virtual string DigiBuddyMail { get; set; }
        public virtual string OzelNotlar { get; set; }
        public virtual string IkAciklama { get; set; }
        public virtual string YoneticiToplanti { get; set; }
        public virtual string YoneticiYetkiEkipman { get; set; }
        public virtual long PtfId { get; set; }

        public virtual string IK_SON_GT1 { get; set; }
        public virtual string IK_SON_YK1 { get; set; }
        public virtual string IK_SON_GT2 { get; set; }
        public virtual string IK_SON_YK2 { get; set; }

        public virtual long? IKIlkOnayIslem { get; set; }
        public virtual DateTime? IKIlkOnayTarih { get; set; }
        public virtual long? IKBordroOnayIslem { get; set; }
        public virtual DateTime? IKBordroOnayTarih { get; set; }
        public virtual long? IdariIslerIslem { get; set; }
        public virtual DateTime? IdariIslerTarih { get; set; }
        public virtual long? GuvenlikIslem { get; set; }
        public virtual DateTime? GuvenlikTarih { get; set; }
        public virtual long? YardimMasasiIslem { get; set; }
        public virtual DateTime? YardimMasasiTarih { get; set; }
        public virtual long? KirtasiyeIslem { get; set; }
        public virtual DateTime? KirtasiyeTarih { get; set; }
        public virtual long? YoneticiOnayIslem { get; set; }
        public virtual DateTime? YoneticiOnayTarih { get; set; }
        public virtual long? IKSonOnayIslem { get; set; }
        public virtual DateTime? IKSonOnayTarih { get; set; }
    }
}
