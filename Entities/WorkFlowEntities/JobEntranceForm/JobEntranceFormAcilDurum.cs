using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobEntranceFormAcilDurum : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }

        public virtual string PoldySira { get; set; }
        public virtual string Adi<PERSON>oyadi { get; set; }
        public virtual string IsTel { get; set; }
        public virtual string EvTel { get; set; }
        public virtual string GSM { get; set; }
        public virtual string YakinlikDerecesi { get; set; }
        public virtual string YakinlikDerecesiId { get; set; }
        public virtual string Adres { get; set; }
        public virtual string EPosta { get; set; }

        public virtual long Status { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long Version_ID { get; set; }

        #endregion Entity Properties
    }
}