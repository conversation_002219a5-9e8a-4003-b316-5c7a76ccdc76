using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobEntranceFormCocuk : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }

        public virtual int PoldyId { get; set; }
        public virtual string AdiSoyadi { get; set; }
        public virtual string TCKN { get; set; }
        public virtual DateTime DogumTarihi { get; set; }
        public virtual string Cinsiyet { get; set; }
        public virtual string TahsilDurumuID { get; set; }
        public virtual string TahsilDurumu { get; set; }

        public virtual long Status { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long Version_ID { get; set; }

        #endregion Entity Properties
    }
}