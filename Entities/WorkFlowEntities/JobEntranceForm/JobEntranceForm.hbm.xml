﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="JobEntranceForm,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_ENTRANCE" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="JOB_ENTRANCE_ID">
      <generator class="trigger-identity"></generator>
    </id>

    <property name="AdiSoyadi" column="FULLNAME" />
    <property name="SicilNo" column="SICILNO" />
    <property name="Hr_Users_ID" column="HRUSERSID" />
    <property name="IseBaslamaTarihi" column="STARTDATE" />
    <property name="UnvanId" column="UNVAN_ID" />
    <property name="BagliOlduguFirmaPoldyId" column="BAGLIOLDUGUFIRMAPOLDY_ID" />
    <property name="BagliOlduguFirmaHRId" column="BAGLIOLDUGUFIRMAHR_ID" />
    <property name="OnayYetkisiVarmi" column="ONAYYETKISIVARMI" />
    <property name="BagliOlacagiKisi" column="BAGLIOLACAGIKISI" />
    <property name="Vadiyalimi" column="VADIYALIMI" />
    <property name="ArgePersonelimi" column="ARGEPERSONELIMI" />
    <property name="TitleId" column="TITLE_ID" />
    <property name="IlId" column="IL_ID" />
    <property name="LokasyonId" column="LOKASYON_ID" />
    <property name="BulunduguKatId" column="BULUNDUGUKAT_ID" />

    <property name="BilgisayarVerilecekmi" column="WILLHAVECOMPUTER" />
    <property name="BilgisayarTipiId" column="COMPUTERTYPEID" />
    <property name="MasaTelefonuVerilecekmi" column="WILLHAVEDESKPHONE" />
    <property name="YakaKartiVerilecekmi" column="WILLHAVEIDCARD" />
    <property name="SirketAraciVerilecekmi" column="WILLHAVECOMPANYCAR" />
    <property name="TasitmatikVerilecekmi" column="WILLHAVEBENZINE" />
    <property name="OtoparkVerilecekmi" column="WILLHAVECARPARK" />
    <property name="KartvizitVerilecekmi" column="WILLHAVECARD" />
    <property name="CicekVerilecekmi" column="WILLHAVEFLOWER" />
    <property name="IseBaslamaKitiVerilecekmi" column="WILLHAVESTARTINGKIT" />
    <property name="CepTelefonuVerilecekmi" column="WILLHAVEMOBILEPHONE" />
    <property name="CepTelefonuSureklimi" column="MOBILEPHONEISPERMANENT" />
    <property name="CepTelefonuSuresi" column="MOBILEPHONEDURATION" />
    <property name="CepTelefonuTalepNedeni" column="MOBILEPHONEREQUESTREASON" />
    <property name="GSMHatVerilecekmi" column="WILLHAVEGSMCARD" />
    <property name="GSMHatSureklimi" column="GSMCARDISPERMANENT" />
    <property name="GSMHatSuresi" column="GSMCARDDURATION" />
    <property name="GSMHatTalepNedeni" column="GSMCARDREQUESTREASON" />
    <property name="GSMHatTarifeId" column="GSMCARDTARIFE_ID" />
    <property name="ModemVerilecekmi" column="WILLHAVEMODEM" />
    <property name="ModemSureklimi" column="MODEMISPERMANENT" />
    <property name="ModemSuresi" column="MODEMDURATION" />
    <property name="ModemTalepNedeni" column="MODEMREQUESTREASON" />
    <property name="ModemKota" column="MODEMKOTA" />

    <property name="KullaniciAdiAcildi" column="OK_USERNAME" />
    <property name="KullaniciAdi" column="USERNAME" />
    <property name="MasaTelefonuAtandi" column="OK_DESKPHONE" />
    <property name="MasaTelefonu" column="DESKPHONE" />
    <property name="EMailAccountAtandi" column="OK_MAILACCOUNT" />
    <property name="EMailAccount" column="MAILACCOUNT" />
    <property name="EMailGroupAtandi" column="OK_MAILGROUPNAME" />
    <property name="ST_HD1Tamamlandi" column="OK_ST_HD1" />

    <property name="BilgisayarAtandi" column="OK_COMPUTER" />
    <property name="ZimmetImzalandi" column="OK_ZIMMET" />
    <property name="ST_HD2Tamamlandi" column="OK_ST_HD2" />

    <property name="SGKIseGirisYapildi" column="OK_SGK" />
    <property name="BankaKartiBasvuru" column="OK_BANKAKARTI" />
    <property name="SaglikSigortasiKayit" column="OK_SAGLIKSIGORTASI" />
    <property name="SodexoKayit" column="OK_SODEXO" />
    <property name="TCKN" column="TCKN" />
    <property name="DogumTarihi" column="DOGUMTARIHI" />
    <property name="VKN" column="VKN" />
    <property name="OzelCepNo" column="OZELCEPNO" />
    <property name="SGKN" column="SGKN" />
    <property name="MedeniDurum" column="MEDENIDURUM" />
    <property name="MezunOlduguOkul" column="MEZUNOLDUGUOKUL" />
    <property name="Cinsiyet" column="CINSIYET" />
    <property name="MezunOlduguYil" column="MEZUNOLDUGUYIL" />
    <property name="KanGrubuId" column="KANGRUBUID" />
    <property name="KanGrubuAdi" column="KANGRUBUADI" />
    <property name="KullaniciGrubu" column="KULLANICIGRUBU" />
    <property name="BedenOlcusu" column="BEDENOLCUSU" />
    <property name="ButceGrubu" column="BUTCEGRUBU" />
    <property name="Adres" column="ADRES" />

    <property name="EsAdi" column="ESFULLNAME" />
    <property name="EsTCKN" column="ESTCKN" />
    <property name="EsDogum" column="ESDOGUMTARIHI" />
    <property name="EsCalisiyor" column="ESCALISIYOR" />

    <property name="YakaKartiBasvuru" column="OK_IDCARDREQUEST" />
    <property name="Resim" column="RESIM" />
    <property name="ST_IKTamamlandi" column="OK_ST_IK" />

    <property name="GSMHatAtandi" column="OK_GSMCARD" />
    <property name="GSMNo" column="GSMNO" />
    <property name="ModemAtandi" column="OK_MODEM" />
    <property name="ST_GSMTamamlandi" column="OK_ST_GSM" />

    <property name="KartvizitBasvuru" column="OK_CARD" />
    <property name="ST_KARTVIZITTamamlandi" column="OK_ST_KARTVIZIT" />

    <property name="CicekGonderilecek" column="OK_FLOWER" />
    <property name="ST_CICEKTamamlandi" column="OK_ST_CICEK" />

    <property name="CepTelefonuTahsisi" column="OK_MOBILEPHONE" />
    <property name="ST_CEPTELEFONUTamamlandi" column="OK_ST_CEPTELEFONU" />

    <property name="SirketAracTahsisi" column="OK_COMPANYCAR" />
    <property name="TasitmatikTahsisi" column="OK_BENZINE" />
    <property name="OtoparkYerAyarlama" column="OK_CARPARK" />
    <property name="ST_ARACTamamlandi" column="OK_ST_ARAC" />

    <property name="YakaKartiCikartildi" column="OK_IDCARD" />
    <property name="GuvenlikBilgilendirildi" column="OK_SECURITYINFO" />
    <property name="ST_YAKAKARTITamamlandi" column="OK_ST_YAKAKARTI" />

    <property name="IseBaslamaKitiGonderildi" column="OK_BASLAMAKITI" />
    <property name="ST_ISEBASLAMAKITITamamlandi" column="OK_ST_ISEBASLAMAKITI" />

    <property name="Created" column="CREATED" />
    <property name="Last_Updated" column="LAST_UPDATED" />
    <property name="Created_By" column="CREATED_BY" />
    <property name="Last_Updated_By" column="LAST_UPDATED_BY" />
    <property name="Version_ID" column="VERSION_ID" />
  </class>
</hibernate-mapping>