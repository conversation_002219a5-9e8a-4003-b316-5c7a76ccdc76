<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="JobEntranceFormKartvizit, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_ENTRANCE_KARTVIZIT" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_ID" />

        <property name="Departman" column="DEPARTMENT" />
        <property name="GSMNO" column="GSM" />
        <property name="Dahili" column="INTERNALNO" />
        <property name="Adet" column="QUANTITY" />
        <property name="Dil" column="LANGUAGE" />
        <property name="Faks" column="FAX" />

        <property name="Status" column="STATUS" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="Version_ID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>