﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class LeaveVardiyaDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Detail Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string leaveType { get; set; }
        public virtual decimal Quoate { get; set; }
        public virtual DateTime LeaveDate { get; set; }
        public virtual bool IsHalfDay { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Detail Entity Properties
    }
}