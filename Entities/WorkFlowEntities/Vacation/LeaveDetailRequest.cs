using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class LeaveDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string leaveType { get; set; }
        public virtual string leaveCode { get; set; }
        public virtual decimal Quoate { get; set; }
        public virtual decimal LeaveHourCount { get; set; }
        public virtual DateTime StartTime { get; set; }
        public virtual bool IsStartHalfDay { get; set; }
        public virtual DateTime EndTime { get; set; }
        public virtual bool IsFinishHalfDay { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}