using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class LeaveRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual decimal LeaveDayCount { get; set; }
        public virtual decimal LeaveHourCount { get; set; }

        public virtual DateTime StartTime { get; set; }
        public virtual long IsStartHalfDay { get; set; }
        public virtual DateTime EndTime { get; set; }
        public virtual long IsFinishHalfDay { get; set; }
        public virtual string Decription { get; set; }
        public virtual long IsNotDelegation { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}