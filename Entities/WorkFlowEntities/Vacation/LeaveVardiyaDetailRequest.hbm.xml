﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="LeaveVardiyaDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_LEAVE_VARDIYA_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="LEAVE_VARDIYA_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="leaveType" column="LEAVE_TYPE" />
    <property name="Quoate" column="QUOATE" />
    <property name="LeaveDate" column="LEAVE_DATE" />
    <property name="IsHalfDay" column="IS_HALF_DAY" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>