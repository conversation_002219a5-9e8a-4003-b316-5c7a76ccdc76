<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="LeaveDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_LEAVE_DETAILREQUEST" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="LEAVE_DETAIL_REQUEST_ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
        <property name="leaveType" column="LEAVE_TYPE" />
        <property name="leaveCode" column="LEAVE_CODE" />
        <property name="Quoate" column="QUOATE" />
        <property name="LeaveHourCount" column="LEAVE_HOUR_COUNT" />
        <property name="StartTime" column="START_TIME" />
        <property name="IsStartHalfDay" column="IS_START_HALF_DAY" />
        <property name="EndTime" column="END_TIME" />
        <property name="IsFinishHalfDay" column="IS_FINISH_HALF_DAY" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="VersionID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>