﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class PurchaseForPaymentRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RequestTypeId { get; set; }
        public virtual long RequestOwnerId { get; set; }
        public virtual long PurchaseId { get; set; }
        public virtual long IsPurchaseOld { get; set; }
        public virtual long PersonnelId { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}