using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BusinessObjectRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long IsNewBOSelection { get; set; }
        public virtual long RequestLoginId { get; set; }
        public virtual long CopyToLoginId { get; set; }
        public virtual long IsUniversSelection { get; set; }
        public virtual string UniversName { get; set; }
        public virtual long IsDataFeederSelection { get; set; }
        public virtual string DataFeederDescription { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual string TargetUserName { get; set; }
        public virtual string CopyUserName { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}

//Digiturk.Workflow.Digiflow.Entities.BusinessObjectRequest ,Digiturk.Workflow.Digiflow.Entities