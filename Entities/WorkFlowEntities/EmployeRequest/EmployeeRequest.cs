﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EmployeeRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual long EmployeeType { get; set; }
        public virtual long Divison { get; set; }
        public virtual long Department { get; set; }
        public virtual long Unit { get; set; }
        public virtual long Team { get; set; }
        public virtual long SubTeam_1_Id { get; set; }
        public virtual long SubTeam_2_Id { get; set; }
        public virtual long SubTeam_3_Id { get; set; }
        public virtual long SubTeam_4_Id { get; set; }
        public virtual long SubTeam_5_Id { get; set; }

        public virtual string Title { get; set; }
        public virtual string SeriNo { get; set; }
        public virtual string LeavePersonelName { get; set; }
        public virtual DateTime StartDate { get; set; }
        public virtual DateTime EndDate { get; set; }
        public virtual long IsReplacement { get; set; }
        public virtual long Budgeted { get; set; }
        public virtual string DeptBudgetAccount { get; set; }
        public virtual string OutsourceDescription { get; set; }
        public virtual string OutsourceCompany { get; set; }
        public virtual string SerialNumber { get; set; }
        public virtual string MainResponsibilities { get; set; }
        public virtual string Salary { get; set; }
        public virtual string WorkDescription { get; set; }
        public virtual string Extras { get; set; }
        public virtual string Requirements { get; set; }
        public virtual string ReasonForHiring { get; set; }
        public virtual long DepartmentManager { get; set; }
        public virtual long ApprovingDepartmentManager { get; set; }
        public virtual long DivisionManager { get; set; }
        public virtual long ApprovingDivisionManager { get; set; }
        public virtual long UnitManager { get; set; }
        public virtual long ApprovingUnitManager { get; set; }
        public virtual long InnerServicesManager { get; set; }
        public virtual long ApprovingInnerServicesManager { get; set; }
        public virtual long GeneralManager { get; set; }
        public virtual long ApprovingGeneralManager { get; set; }
        public virtual DateTime DepartmentManagerApproval { get; set; }
        public virtual DateTime DivisionManagerApproval { get; set; }
        public virtual DateTime UnitManagerApproval { get; set; }
        public virtual DateTime InnerServicesManagerApproval { get; set; }
        public virtual DateTime GeneralManagerApproval { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastModified { get; set; }
        public virtual long LastModifiedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual long RequestType { get; set; }

        public virtual long BuddyLoginId { get; set; }
        public virtual string Lokasyon { get; set; }
    }
}