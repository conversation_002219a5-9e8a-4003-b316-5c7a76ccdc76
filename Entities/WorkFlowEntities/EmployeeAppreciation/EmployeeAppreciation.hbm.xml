﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
                   assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">

	<class name="EmployeeAppreciation, Digiturk.Workflow.Digiflow.Entities"
		   table="WF_DF_EMPLOYEE_APPRECIATION" schema="DT_WORKFLOW">

		<id name="RequestId" type="long" column="APPRECIATION_REQUEST_ID">
			<generator class="trigger-identity"></generator>
		</id>
		
		<property name="CandidateId"				 column="CANDIDATE_ID" />
		<property name="CandidateName"				 column="CANDIDATE_NAME" />
		<property name="CandidateDepartment"		 column="CANDIDATE_DEPARTMENT" />
		<property name="CandidatePosition"			 column="CANDIDATE_POSITION" />
		<property name="AwardCategories"			 column="AWARD_CATEGORIES" />
		<property name="ExampleCase"				 column="EXAMPLE_CASE" />
		<property name="BehaviorValueAlignmentScore" column="BEHAVIOR_VALUE_ALIGNMENT_SCORE" />
		<property name="BehaviorConcretenessScore"   column="BEHAVIOR_CONCRETENESS_SCORE"    />
		<property name="BehaviorImpactScore"         column="BEHAVIOR_IMPACT_SCORE"         />
		<property name="RepeatabilityScore"          column="REPEATABILITY_SCORE"           />
		<property name="Created"					 column="CREATED" />
		<property name="LastUpdated"				 column="LAST_UPDATED" />
		<property name="CreatedBy"					 column="CREATED_BY" />
		<property name="LastUpdatedBy"				 column="LAST_UPDATED_BY" />
		<property name="VersionID"					 column="VERSION_ID" />

	</class>
</hibernate-mapping>
