﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="EmployeesInfoRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EMPLOYEES_INFO_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EMPLOYEES_INFO_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="UserId" column="USER_ID" />
    <property name="Firma" column="FIRMA" />
    <property name="Sirket" column="SIRKET" />
    <property name="SicilNo" column="SICIL_NO" />
    <property name="KullaniciAdi" column="KULLANICI_ADI" />
    <property name="AdiSoyadi" column="ADI_SOYADI" />
    <property name="DepartmanId" column="DEPARTMAN_ID" />
    <property name="ButceGrubu" column="BUTCE_GRUBU" />
    <property name="Email" column="EMAIL" />
    <property name="IsManager" column="ISMANAGER" />
    <property name="ButceOnayYetki" column="BUTCEONAY_YETKI" />
    <property name="MasaTel" column="MASA_TEL" />
    <property name="IseGirisTarihi" column="ISEGIRIS_TARIHI" />
    <property name="DogumTarihi" column="DOGUM_TARIHI" />
    <property name="TcNo" column="TC_NO" />
    <property name="VergiNo" column="VERGI_NO" />
    <property name="SgkNo" column="SGK_NO" />
    <!--<property name="MezunOkul" column="MEZUN_OKUL" />
    <property name="MezunYil" column="MEZUN_YIL"  />-->
    <property name="Adres" column="ADRES" />
    <property name="CepTel" column="CEP_TEL" />
    <property name="MedeniDurum" column="MEDENI_DURUM" />
    <property name="Cinsiyet" column="CINSIYET" />
    <property name="EsAdSoyad" column="ES_ADSOYAD" />
    <property name="EsTcNo" column="ES_TCNO" />
    <property name="EsDogumTarihi" column="ES_DOGUMTARIHI" />
    <property name="EsCalisiyormu" column="ES_CALISIYORMU" />
    <property name="KanGrup" column="KAN_GRUP" />
    <property name="KullaniciGrubu" column="KULLANICI_GRUBU" />
    <property name="BedenOlcusu" column="BEDEN_OLCUSU" />
    <property name="Vardiyalimi" column="VARDIYALIMI" />
    <property name="ArgePersonelimi" column="ARGE_PERSONELIMI" />
    <property name="Title" column="TITLE" />
    <property name="GsmProfil" column="GSM_PROFIL" />
    <property name="Unvan" column="UNVAN" />
    <property name="Il" column="IL" />
    <property name="Lokasyon" column="LOKASYON" />
    <property name="BulunduguKat" column="BULUNDUGU_KAT" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionId" column="VERSION_ID" />
    <property name="SODEXHO_KART_NO" column="SODEXHO_KART_NO" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="Name" column="NAME" />
    <property name="Surname" column="SURNAME" />
	<property name="Pozisyon" column="POZISYON" />
	  <property name="SozlesmeTuru" column="SOZLESME_TURU" />
	  <property name="CalismaTipi" column="CALISMA_TIPI" />
	  <property name="CalisanTahsil" column="TAHSIL" />
  </class>
</hibernate-mapping>