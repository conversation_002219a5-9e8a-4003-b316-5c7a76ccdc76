﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="EmployeesDetailAcil, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EMPLOYEES_DETAIL_ACIL" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EMPLOYEES_DETAIL_EMERGENCY_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="AcilAdSoyad" column="ACIL_ADSOYAD" />
    <property name="AcilYakinlik" column="ACIL_YAKINLIK" />
    <property name="AcilTelis" column="ACIL_TELIS" />
    <property name="AcilTelev" column="ACIL_TELEV" />
    <property name="AcilGsm" column="ACIL_GSM" />
    <property name="AcilAdres" column="ACIL_ADRES" />
    <property name="AcilEmail" column="ACIL_EMAIL" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionId" column="VERSION_ID" />
  </class>
</hibernate-mapping>