﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EmployeesDetailChild : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string CocukAdSoyad { get; set; }
        public virtual string CocukTcNo { get; set; }
        public virtual DateTime CocukDogumTarihi { get; set; }
        public virtual string CocukCinsiyet { get; set; }
        public virtual string CocukTahsil { get; set; }
        public virtual long CocukId { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }
    }
}