<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="MonitoringRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_MONITORING_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="MONITORING_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="FlowDefId" column="FLOW_DEF_ID" />
    <property name="FlowInstanceID" column="FLOW_INSTANCE_ID" />
    <property name="FlowTypeID" column="FLOW_TYPE_ID" />
    <property name="FlowTypeName" column="NAME" />
    <property name="PersonelID" column="PERSONEL_ID" />
    <property name="FlowDefIdList" column="FLOW_DEF_ID_LIST" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="IsActive" column="IS_ACTIVE" />
	<property name="OwnerLoginID" column="OWNER_LOGIN_ID" />
  </class>
</hibernate-mapping>