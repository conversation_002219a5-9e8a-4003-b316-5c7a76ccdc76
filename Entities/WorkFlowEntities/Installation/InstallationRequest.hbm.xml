<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="Digiturk.Workflow.Digiflow.Entities.InstallationRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_INSTALLATIONREQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="INSTALLATION_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="DemandType" column="DEMAND_TYPE" />
    <property name="DemandDate" column="DEMAND_DATE" />
    <property name="ProcessType" column="PROCESS_TYPE" />
    <property name="NameSurname" column="NAMESURNAME" />
    <property name="Phone" column="PHONE" />
    <property name="Address" column="ADDRESS" />
    <property name="References" column="REFERENCES" />
    <property name="Notes" column="NOTES" />
    <property name="PaymentType" column="PAYMENT_TYPE" />
    <property name="ForwardYTS" column="FORWARD_YTS" />
    <property name="ForwardDate" column="FORWARD_DATE" />
    <property name="ApprovalResult" column="APPROVAL_RESULT" />
    <property name="ApprovalComment" column="APPROVAL_COMMENT" />
    <property name="ProcessComment" column="PROCESS_COMMENT" />
    <property name="TotalAmount" column="TOTAL_AMOUNT" />
    <property name="TakenAmount" column="TAKEN_AMOUNT" />
    <property name="RequestEndDate" column="REQUEST_END_DATE" />
    <property name="RequestEndHour" column="REQUEST_END_HOUR" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="ClosingDate" column="CLOSING_DATE" />
  </class>
</hibernate-mapping>