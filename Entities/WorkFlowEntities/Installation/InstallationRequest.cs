using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class InstallationRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long DemandType { get; set; }
        public virtual DateTime DemandDate { get; set; }
        public virtual long ProcessType { get; set; }
        public virtual string NameSurname { get; set; }
        public virtual string Phone { get; set; }
        public virtual string Address { get; set; }
        public virtual string References { get; set; }
        public virtual string Notes { get; set; }
        public virtual string PaymentType { get; set; }
        public virtual string ForwardYTS { get; set; }
        public virtual DateTime ForwardDate { get; set; }
        public virtual string ApprovalResult { get; set; }
        public virtual string ApprovalComment { get; set; }
        public virtual string ProcessComment { get; set; }
        public virtual decimal TotalAmount { get; set; }
        public virtual decimal TakenAmount { get; set; }
        public virtual DateTime RequestEndDate { get; set; }
        public virtual string RequestEndHour { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual DateTime ClosingDate { get; set; }


        #endregion Entity Properties
    }
}