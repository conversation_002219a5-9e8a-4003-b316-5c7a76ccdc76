using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SupplierOperationRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual long SalesDelegateLoginId { get; set; }
        public virtual long DepartmentId { get; set; }
        public virtual long RegionId { get; set; }
        public virtual long ZoneManagerLoginId { get; set; }
        public virtual long CityId { get; set; }
        public virtual string CityName { get; set; }
        public virtual string ServiceId { get; set; }
        public virtual string DemandSubject { get; set; }
        public virtual string Description { get; set; }
        public virtual string FileName { get; set; }
        public virtual long OperationTypeId { get; set; }
        public virtual string RequestType { get; set; }
        public virtual string ServiceName { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual DateTime ClosingDate { get; set; }

        #endregion Entity Properties
    }
}