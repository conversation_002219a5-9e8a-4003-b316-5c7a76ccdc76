<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SupplierOperationRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SUPPLIER_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
	<property name="SalesDelegateLoginId" column="SALESDEL_LOGIN_ID" />
    <property name="DepartmentId" column="DEPARTMENT_ID" />
    <property name="RegionId" column="REGION_ID" />
    <property name="ZoneManagerLoginId" column="ZONE_MANAGER_ID" />
    <property name="CityId" column="CITY_ID" />
	<property name="CityName" column="CITY_NAME" />
    <property name="ServiceId" column="SERVICE_ID" />
    <property name="DemandSubject" column="DEMAND_SUBJECT" />
    <property name="Description" column="DESCRIPTION" />
    <property name="FileName" column="FILE_NAME" />
    <property name="OperationTypeId" column="OPERATION_TYPE_ID" />
	<property name="RequestType" column="REQUEST_TYPE" />
	<property name="ServiceName" column="SERVICE_NAME" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
	<property name="ClosingDate" column="CLOSING_DATE" />

  </class>
</hibernate-mapping>