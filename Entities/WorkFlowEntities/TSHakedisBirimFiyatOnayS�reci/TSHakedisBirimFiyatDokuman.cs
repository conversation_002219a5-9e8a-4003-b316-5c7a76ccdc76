﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TSHakedisBirimFiyatDokuman : EntityBase, IEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual long WorkflowInstanceId { get; set; }
        public virtual long StateInstanceId { get; set; }
        public virtual long UploadLoginId { get; set; }
        public virtual string FileUploadKey { get; set; }
        public virtual string UploadFileComment { get; set; }
        public virtual string UploadFileName { get; set; }
        public virtual string UploadFile { get; set; }
        public virtual DateTime UpladDate { get; set; }
        public virtual string Description { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        //CREATE TABLE DT_WORKFLOW.WF_DF_TS_FILE_UPLOAD
        //(
        //  REQUEST_ID NUMBER                   NOT NULL,
        //  FILE_UPLOAD_KEY      NVARCHAR2(1000),
        //  WF_INSTANCE_ID NUMBER                   NOT NULL,
        //  STATE_INSTANCE_ID    NUMBER NOT NULL,
        //  UPLOAD_LOGIN_ID NUMBER                   NOT NULL,
        //  UPLOAD_FILE_COMMENT  NVARCHAR2(1000),
        //  UPLOAD_FILE NVARCHAR2(1000),
        //  UPLOAD_DATE DATE,
        //  CREATED              DATE,
        //  CREATED_BY NUMBER,
        //  LAST_UPDATED         DATE,
        //  LAST_UPDATED_BY NUMBER,
        //  VERSION_ID           NUMBER
        //);
        //<property name = "FileUploadKey" column="FILE_UPLOAD_KEY" />
        //<property name = "WorkflowInstanceId" column="WF_INSTANCE_ID" />
        //<property name = "StateInstanceId" column="STATE_INSTANCE_ID" />
        //<property name = "UploadLoginId" column="UPLOAD_LOGIN_ID" />
        //<property name = "UploadFileComment" column="UPLOAD_FILE_COMMENT" />
        //<property name = "UploadFile" column="UPLOAD_FILE" />
        //<property name = "UpladDate" column="UPLOAD_DATE" />
        //<property name = "Created" column="CREATED" />
        //<property name = "LastUpdated" column="LAST_UPDATED" />
        //<property name = "CreatedBy" column="CREATED_BY" />
        //<property name = "LastUpdatedBy" column="LAST_UPDATED_BY" />
        //<property name = "VersionID" column="VERSION_ID" />

    }
}
