﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TSHakedisBirimFiyatOnaySüreciDetail,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TSHKDSBRMFYT_DET_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="FiyatTuru" column="FIYAT_TURU" />
    <property name="BayiKodu" column="BAYI_KODU" />
    <property name="MemoKodu" column="MEMO_KODU" />
    <property name="MalzemeStokKodu" column="MALZEME_STOK_KODU" />
    <property name="MalzemeAdi" column="MALZEME_ADI" />
    <property name="MevcutBirimFiyat" column="MEVCUT_BIRIM_FIYAT" />
    <property name="YeniBirimFiyat" column="YENI_BIRIM_FIYAT" />
    <property name="FiyatGecerlilikTarihi" column="FIYAT_GECERLILIK_TARIHI" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="SonucKodu" column="SONUC_KODU" />
    <property name="SonucAciklamasi" column="SONUC_ACIKLAMASI" />
    <property name="SonucKayitId" column="SONUCKAYITID" />
  </class>
</hibernate-mapping>