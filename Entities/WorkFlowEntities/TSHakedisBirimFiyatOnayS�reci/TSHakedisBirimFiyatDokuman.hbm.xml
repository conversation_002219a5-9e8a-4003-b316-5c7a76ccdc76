﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TSHakedisBirim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TS_FILE_UPLOAD" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="FileUploadKey" column="FILE_UPLOAD_KEY" />
    <property name="WorkflowInstanceId" column="WF_INSTANCE_ID" />
    <property name="StateInstanceId" column="STATE_INSTANCE_ID" />
    <property name="UploadLoginId" column="UPLOAD_LOGIN_ID" />
    <property name="UploadFileComment" column="UPLOAD_FILE_COMMENT" />
    <property name="UploadFile" column="UPLOAD_FILE" />
    <property name="UploadFileName" column="UPLOAD_FILE_NAME" />
    <property name="UpladDate" column="UPLOAD_DATE" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>