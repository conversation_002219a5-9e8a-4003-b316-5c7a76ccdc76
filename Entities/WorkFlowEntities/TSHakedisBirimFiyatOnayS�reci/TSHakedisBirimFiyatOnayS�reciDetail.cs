﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{

    //dr["FIYAT_TURU"] = drpFiyatTuru.SelectedValue;
    //dr["BAYI_KODU"] = txtBayiKodu.Text;
    //dr["MEMO_KODU"] = txtMemoKodu.Text;
    //dr["MALZEME_STOK_KODU"] = drpMalzemeStokKodu.SelectedValue;
    //dr["MALZEME_ADI"] = txtMalzemeAdi.Text;
    //dr["MEVCUT_BIRIM_FIYAT"] = !string.IsNullOrEmpty(txtMevcutFiyat.Text) ? decimal.Parse(txtMevcutFiyat.Text) : 0;
    //dr["YENI_BIRIM_FIYAT"] = !string.IsNullOrEmpty(txtYeniMalzemeBirimFiyati.Text) ? decimal.Parse(txtYeniMalzemeBirimFiyati.Text) : 0;
    //dr["FIYAT_GECERLILIK_TARIHI"] = !string.IsNullOrEmpty(txtFiyatGecerlilikTarihi.Text) ? DateTime.Parse(txtFiyatGecerlilikTarihi.Text) : DateTime.MinValue;


    public class TSHakedisBirimFiyatOnaySüreciDetail : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string FiyatTuru { get; set; }
        public virtual string BayiKodu { get; set; }
        public virtual string MemoKodu { get; set; }
        public virtual string MalzemeStokKodu { get; set; }
        public virtual string MalzemeAdi { get; set; }
        public virtual decimal MevcutBirimFiyat { get; set; }
        public virtual decimal YeniBirimFiyat { get; set; }
        public virtual DateTime FiyatGecerlilikTarihi { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string SonucKodu { get; set; }
        public virtual string SonucAciklamasi { get; set; }
        public virtual long SonucKayitId { get; set; }
    }
}
