﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TallyDayDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual DateTime RequestTime { get; set; }
        public virtual long DayWorkHour { get; set; }
        public virtual long NightWorkHour { get; set; }
        public virtual long EducationWorkHour { get; set; }
        public virtual long OvertTimeHour { get; set; }
        public virtual long OtherStatusId { get; set; }
        public virtual long OtherWorkHour { get; set; }
    }
}