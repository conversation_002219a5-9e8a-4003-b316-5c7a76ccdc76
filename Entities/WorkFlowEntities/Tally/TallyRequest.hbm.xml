<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TallyRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TALLYREQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="TALLY_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" type="long" />
    <property name="TotalPrice" column="TOTAL_PRICE" type="decimal" />
    <property name="WorkPrice" column="WORK_PRICE" type="decimal" />
    <property name="PrivatePrice" column="PRIVATE_PRICE" type="decimal" />
    <property name="IsCorrect" column="IS_CORRECT" type="long" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionID" column="VERSION_ID" type="long" />
  </class>
</hibernate-mapping>