<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TallyDayDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DP_TALLY_DAY_DETAIL_INFO" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>

    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" type="long" />
    <property name="RequestTime" column="REQUEST_TIME" type="DateTime" />
    <property name="DayWorkHour" column="DAY_WORK_HOUR" type="long" />
    <property name="NightWorkHour" column="NIGHT_WORK_HOUR" type="long" />
    <property name="EducationWorkHour" column="EDUCATION_WORK_HOUR" type="long" />
    <property name="OvertTimeHour" column="OVER_TIME_HOUR" type="long" />
    <property name="OtherStatusId" column="OTHER_STATUS_ID" type="long" />
    <property name="OtherWorkHour" column="OTHER_WORK_HOUR" type="long" />
  </class>
</hibernate-mapping>

<!--
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual DateTime RequestTime { get; set; }
        public virtual long DayWorkHour { get; set; }
        public virtual long NightWorkHour { get; set; }
        public virtual long EducationWorkHour { get; set; }
        public virtual long OvertTimeHour { get; set; }
        public virtual long OtherStatusId { get; set; }
        public virtual long OtherWorkHour { get; set; }
-->