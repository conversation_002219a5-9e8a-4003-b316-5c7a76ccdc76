using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TallyRequest : EntityBase, IEntity
    {
        [ID]
        public virtual long RequestId { get; private set; }

        public virtual long OwnerLoginId { get; set; }
        public virtual decimal TotalPrice { get; set; }
        public virtual decimal WorkPrice { get; set; }
        public virtual decimal PrivatePrice { get; set; }
        public virtual long IsCorrect { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
    }
}