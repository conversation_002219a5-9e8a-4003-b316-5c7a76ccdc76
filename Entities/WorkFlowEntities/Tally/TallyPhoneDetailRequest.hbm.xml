<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TallyPhoneDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_TALLY_TEL_INFO" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="WF_TALLY_TEL_INFO_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="TelNo" column="TEL_NO" type="string" />
    <property name="PortNo" column="PORT_NO" type="string" />
    <property name="CallType" column="CALL_TYPE" type="string" />
    <property name="CompanyName" column="COMPANYNAME" type="string" />
    <property name="InsertDate" column="INSERT_DATE" type="DateTime" />
    <property name="InsertedBy" column="INSERTEDBY" type="long" />
  </class>
</hibernate-mapping>