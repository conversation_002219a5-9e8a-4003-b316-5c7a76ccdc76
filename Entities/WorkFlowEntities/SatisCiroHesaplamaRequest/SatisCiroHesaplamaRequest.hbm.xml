﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SatisCiroHesaplamaRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SATIS_CIRA_HESAPLAMA_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="HesaplamaSatisBirim" column="HESAPLAMA_SATIS_BIRIM" />
    <property name="HesaplamaSatisBirimAdi" column="HESAPLAMA_SATIS_BIRIM_ADI" />
    <property name="Aciklama" column="ACIKLAMA" />
	<property name="Dosya" column="DOSYA" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>