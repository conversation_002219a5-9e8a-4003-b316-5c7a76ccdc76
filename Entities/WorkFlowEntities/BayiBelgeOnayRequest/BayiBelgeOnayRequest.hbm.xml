﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BayiBelgeOnayRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BAYI_BELGE_ONAY_RQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="IrisTalepID" column="IRIS_TALEP_ID" />
    <property name="PersonelId" column="PERSONEL_ID" />
	<property name="PersonelAdsoyad" column="PERSONEL_AD_SOYAD" />
	<property name="PersonelGorevi" column="PERSONEL_GOREVI" />
	<property name="CalistigiBayi" column="CALISTIGI_BAYI" />
	<property name="BayiKodu" column="BAYI_KODU" />
	<property name="TeknikServisYonetici" column="TEKNIK_SERVIS_YONETICI" />
	<property name="SatisTemsilcisi" column="SATIS_TEMSILCISI" />  
    <property name="TeknikServisMuduru" column="TEKNIK_SERVIS_MUDURU" />   
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
	
	 
  </class>
</hibernate-mapping>