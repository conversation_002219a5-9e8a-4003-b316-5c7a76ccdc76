﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiBelgeOnayRequest : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual long IrisTalepID { get; set; }
        public virtual string PersonelId { get; set; }
        public virtual string PersonelAdsoyad { get; set; }
        public virtual string <PERSON>elGorevi { get; set; }
        public virtual string CalistigiBayi { get; set; }
        public virtual string Bayi<PERSON>odu { get; set; }
        public virtual string TeknikServisYonetici { get; set; }
        public virtual string SatisTemsilcisi { get; set; }
        public virtual string TeknikServisMuduru { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        
       

        #endregion
    }
}