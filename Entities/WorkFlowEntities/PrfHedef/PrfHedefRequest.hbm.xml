﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="PrfHedefRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PRF_HEDEF_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="RequestId">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="ARA_DONEM_ID" column="ARA_DONEM_ID" />
    <property name="KULLANICI_ID" column="KULLANICI_ID" />
    <property name="ACAN_KULLANICI_ID" column="ACAN_KULLANICI_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>