﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ExpenseFormRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long AcanLoginId { get; set; }
        public virtual long LoginId { get; set; }
        public virtual string NameSurname { get; set; }
        public virtual long DepartmentId { get; set; }
        public virtual string CurrentAccountNo { get; set; }
        public virtual decimal ExpenseAmount { get; set; }
        public virtual long FirmExpenseId { get; set; }
        public virtual string FirmExpense { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual string CreatedBy { get; set; }
        public virtual string LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}