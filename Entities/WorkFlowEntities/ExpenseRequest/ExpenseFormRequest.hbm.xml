﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ExpenseFormRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EXPENSE_FORM_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EXPENSE_FORM_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="LoginId" column="LOGIN_ID" />
    <property name="NameSurname" column="NAME_SURNAME" />
    <property name="AcanLoginId" column="ACAN_LOGIN_ID" />
    <property name="DepartmentId" column="DEPARTMENT_ID" />
    <property name="CurrentAccountNo" column="CURRENT_ACCOUNT_NO" />
    <property name="ExpenseAmount" column="EXPENSE_AMOUNT" />
    <property name="FirmExpenseId" column="FIRM_EXPENSE_ID" />
    <property name="FirmExpense" column="FIRM_EXPENSE" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>