﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ExpenseDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EXPENSE_DETAILREQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EXPENSE_DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="ExpenseTypeId" column="EXPENSE_TYPE_ID" />
    <property name="ExpenseAmount" column="EXPENSE_AMOUNT" />
    <property name="ExpenseCurrency" column="EXPENSE_CURRENCY" />
    <property name="ExpenseDate" column="EXPENSE_DATE" />
    <property name="ExpenseDescription" column="EXPENSE_DESCRIPTION" />
    <property name="DistanceDescription" column="DISTANCE_DESCRIPTION" />
    <property name="BillID" column="BILL_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>