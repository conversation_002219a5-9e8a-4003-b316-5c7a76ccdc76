﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SodexhoDetailRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SODEXHO_DETAIL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="SODEXHO_DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="KISI_FLOGIN" column="KISI_FLOGIN" />
    <property name="SICIL_NUMARASI" column="SICIL_NUMARASI" />
    <property name="KART_NO" column="KART_NO" />
    <property name="SEHIR_ICI_OGUN" column="SEHIR_ICI_OGUN" />
    <property name="OGUN_GUN" column="OGUN_GUN" />
    <property name="SEHIR_DISI_GUN" column="SEHIR_DISI_GUN" />
    <property name="ACIKLAMA" column="ACIKLAMA" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="KISI_SIRKET" column="KISI_SIRKET" />
    <property name="OgunTarih" column="OGUN_TARIH" />
	<property name="SehisIcDis" column="SEHIR_IC_DIS" />
  </class>
</hibernate-mapping>