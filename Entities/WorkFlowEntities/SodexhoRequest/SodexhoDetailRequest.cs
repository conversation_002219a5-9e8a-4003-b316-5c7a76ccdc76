﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SodexhoDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long KISI_FLOGIN { get; set; }

        public virtual string SICIL_NUMARASI { get; set; }

        public virtual string KART_NO { get; set; }
        public virtual long SEHIR_ICI_OGUN { get; set; }

        public virtual long OGUN_GUN { get; set; }
        public virtual long SEHIR_DISI_GUN { get; set; }
        public virtual string ACIKLAMA { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string KISI_SIRKET { get; set; }
        public virtual DateTime OgunTarih { get; set; }
        public virtual string SehisIcDis { get; set; } 

        #endregion Entity Properties
    }
}