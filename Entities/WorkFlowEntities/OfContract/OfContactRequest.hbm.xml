<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="OfContactRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_OFCONTACTREQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="OFCONTACT_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="City" column="CITY" />
    <property name="NameSurname" column="NAMESURNAME" />
    <property name="DepDesc" column="DEP" />
    <property name="Gsm" column="GSM" />
	<property name="Gsm2" column="GSM2" />
    <property name="InternalNo" column="INTERNALNO" />
    <property name="Email" column="EMAIL" />
    <property name="Count" column="COUNT" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="Language" column="LANGUAGE" />
	<property name="Fax" column="FAX" />
	<property name="FaxExist" column="FAX_EXIST" />
	<property name="PhoneExist" column="PHONE_EXIST" />
  </class>
</hibernate-mapping>