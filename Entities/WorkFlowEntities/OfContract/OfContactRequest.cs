using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class OfContactRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string City { get; set; }
        public virtual string NameSurname { get; set; }
        public virtual string DepDesc { get; set; }
        public virtual string Gsm { get; set; }
        public virtual string Gsm2 { get; set; }
        public virtual string InternalNo { get; set; }
        public virtual string Email { get; set; }
        public virtual long Count { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string Language { get; set; }
        public virtual string Fax { get; set; }
        public virtual string FaxExist { get; set; }
        public virtual string PhoneExist { get; set; }

        #endregion Entity Properties
    }
}