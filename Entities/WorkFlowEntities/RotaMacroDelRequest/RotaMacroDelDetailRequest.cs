﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class RotaMacroDelDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long PersonelID { get; set; }
        public virtual string PersonelUserName { get; set; }
        public virtual long RotaGroupNo { get; set; }
        public virtual long RotaReportNo { get; set; }
        public virtual string MacroReportName { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string RotaGroup { get; set; }
        public virtual string RotaReport { get; set; }
        public virtual string RaporTip { get; set; }
        public virtual string NameSurname { get; set; }

        public virtual long WorkflowId { get; set; }


        #endregion Entity Properties
    }
}