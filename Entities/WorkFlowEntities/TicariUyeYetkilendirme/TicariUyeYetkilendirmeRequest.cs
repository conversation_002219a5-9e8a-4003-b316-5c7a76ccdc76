using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TicariUyeYetkilendirmeRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long IRIS_ID { get; set; }
        public virtual long UYE_NO { get; set; }
        public virtual string UYE_ADI { get; set; }
        public virtual string BAYI_AD { get; set; }
        public virtual string BAYI_KOD { get; set; }
        public virtual string BAYI_BOLGE_KOD { get; set; }
        public virtual string BAYI_YONETICI { get; set; }
        public virtual string ACIKLAMA { get; set; }
        public virtual string ESKI_GRUP { get; set; }
        public virtual string ESKI_PESIN { get; set; }
        public virtual string ESKI_TAKSITLI { get; set; }

        public virtual string YENI_GRUP { get; set; }
        public virtual string YENI_PESIN { get; set; }
        public virtual string YENI_TAKSITLI { get; set; }

        public virtual string ADRES { get; set; }
        public virtual string IL { get; set; }
        public virtual string ILCE { get; set; }
        public virtual string DEMOGRAFIK { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        public virtual long POTANSIYEL_NO { get; set; }
        public virtual string POTANSIYEL_ADI { get; set; }
        public virtual string KAYNAK { get; set; }
        public virtual string BAYI_IL { get; set; }
        #endregion Entity Properties
    }
}