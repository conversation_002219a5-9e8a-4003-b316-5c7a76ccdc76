﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SatinAlmaSurecleriOnayRequest_Detay,Digiturk.Workflow.Digiflow.Entities"
         table="WF_DF_SATIN_ALMA_SURECLERI_ONAY_REQUEST_DETAY" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="Talep_Dokumani" column="DOKUMAN_LINK" />
    <property name="DokumanDosyaAdi" column="DOKUMAN_DOSYA_ADI" />
    <property name="Created" column="KAYIT_TARIHI" />
    <property name="LastUpdated" column="DEGISTIRME_TARIHI" />
    <property name="CreatedBy" column="KAYDEDEN" />
    <property name="LastUpdatedBy" column="DEGISTIREN" />
  </class>
</hibernate-mapping>