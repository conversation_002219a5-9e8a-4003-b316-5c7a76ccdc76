﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SatinAlmaSurecleriOnayRequest,Digiturk.Workflow.Digiflow.Entities" 
         table="WF_DF_SATIN_ALMA_SURECLERI_ONAY_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="Talep_Tipi" column="TALEP_TIPI" />
    <property name="Pr_Number" column="PR_NUMBER" />
    <property name="Sirket_Adi" column="SIRKET_ADI" />
    <property name="Pr_Tutari_Usd" column="PR_TUTARI_USD" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="Created" column="KAYIT_TARIHI" />
    <property name="LastUpdated" column="DEGISTIRME_TARIHI" />
    <property name="CreatedBy" column="KAYDEDEN" />
    <property name="LastUpdatedBy" column="DEGISTIREN" />    
  </class>
</hibernate-mapping>


