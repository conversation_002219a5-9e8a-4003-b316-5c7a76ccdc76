﻿using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SatinAlmaSurecleriOnayRequest_Detay : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string Tale<PERSON>_<PERSON><PERSON><PERSON> { get; set; }
        public virtual string DokumanDosyaAdi { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        
    }
}
