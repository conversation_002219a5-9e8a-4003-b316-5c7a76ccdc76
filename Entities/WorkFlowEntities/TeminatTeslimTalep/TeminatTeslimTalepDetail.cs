using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TeminatTeslimTalepDetail : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string FIRMA_ADI { get; set; }
        public virtual string TUTAR { get; set; }
        public virtual DateTime ALINDIGI_TARIH { get; set; }
        public virtual string BANKA { get; set; }

        public virtual string MEKTUP_NO { get; set; }
        public virtual string TIP { get; set; }
        public virtual string BAYI_KODU { get; set; }
        public virtual string PARA_BIRIMI { get; set; }
        public virtual string BITIS_TIP { get; set; }
        public virtual System.Nullable<DateTime> BITIS_TARIHI { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}