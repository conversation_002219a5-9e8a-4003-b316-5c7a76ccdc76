<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="TeminatTeslimTalepDetail,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TEMINAT_TESLIM_DETAIL" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="TEMINAT_TESLIM_DETAIL_ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
        <property name="FIRMA_ADI" column="FIRMA_ADI" />
        <property name="TUTAR" column="TUTAR" />
        <property name="ALINDIGI_TARIH" column="ALINDIGI_TARIH" />
        <property name="BANKA" column="BANKA" />
        <property name="MEKTUP_NO" column="MEKTUP_NO" />

        <property name="TIP" column="TIP" />
        <property name="BAYI_KODU" column="BAYI_KODU" />
        <property name="PARA_BIRIMI" column="PARA_BIRIMI" />
        <property name="BITIS_TIP" column="BITIS_TIP" />
        <property name="BITIS_TARIHI" column="BITIS_TARIHI" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="VersionID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>