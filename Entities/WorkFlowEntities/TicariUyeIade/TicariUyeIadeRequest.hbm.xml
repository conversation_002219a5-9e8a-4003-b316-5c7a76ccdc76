<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TicariUyeIadeRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TICARI_UCRET_IADE" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="IRIS_ID" column="IRIS_ID" />
    <property name="UYE_NO" column="UYE_NO" />
    <property name="UYE_ADI" column="UYE_ADI" />
    <property name="BAYI_AD" column="BAYI_AD" />
    <property name="BAYI_KOD" column="BAYI_KOD" />
    <property name="BAYI_BOLGE_KOD" column="BAYI_BOLGE_KOD" />
    <property name="BAYI_YONETICI" column="BAYI_YONETICI" />
    <property name="ACIKLAMA" column="ACIKLAMA" />
    <property name="ESKI_GRUP" column="ESKI_GRUP" />
    <property name="ESKI_PESIN" column="ESKI_PESIN" />
    <property name="ESKI_TAKSITLI" column="ESKI_TAKSITLI" />
    <property name="GPS_LOKASYON" column="GPS_LOKASYON" />
    <property name="ADRES" column="ADRES" />
    <property name="IL" column="IL" />
    <property name="ILCE" column="ILCE" />
    <property name="DEMOGRAFIK" column="DEMOGRAFIK" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />

    <property name="POTANSIYEL_NO" column="POTANSIYEL_NO" />
    <property name="POTANSIYEL_ADI" column="POTANSIYEL_ADI" />
    <property name="KAYNAK" column="KAYNAK" />
    
  </class>
</hibernate-mapping>