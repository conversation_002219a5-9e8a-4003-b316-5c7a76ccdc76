﻿<?xml version="1.0" encoding="utf-8" ?>
<!--<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">-->
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2">
  <class name="Digiturk.Workflow.Digiflow.Entities.GSMRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_GSM_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestID" type="long" column="GSM_REQUEST_ID">
        <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedLoginID" column="RELATED_LOGIN_ID" type="long" />
    <property name="Divison" column="DIVISION" type="long" />
    <property name="Department" column="DEPARTMENT" type="long" />
    <property name="Unit" column="UNIT" type="long" />
    <property name="Team" column="TEAM" type="long" />
    <property name="DepartmentManager" column="DEPARTMENT_MANAGER" />
    <property name="DivisionManager" column="DIVISION_MANAGER" />
    <property name="UnitManager" column="UNIT_MANAGER" />
    <property name="InnerServicesManager" column="INNER_SERVICES_MANAGER" />
    <property name="GeneralManager" column="GENERAL_MANAGER" />
    <property name="GsmRequest" column="GSM_REQUEST" type="long" />
    <property name="CepTelRequest" column="CEPTEL_REQUEST" type="long" />
    <property name="MobilIntRequest" column="MOBILINT_REQUEST" type="long" />
    <property name="GsmRequestDuration" column="GSM_REQUEST_DURATION" type="long" />
    <property name="CepTelRequestDuration" column="CEPTEL_REQUEST_DURATION" type="long" />
    <property name="MobilIntRequestDuration" column="MOBILINT_REQUEST_DURATION" type="long" />
    <property name="GsmRequestReason" column="GSM_REQUEST_REASON" type="string" />
    <property name="CepTelRequestReason" column="CEPTEL_REQUEST_REASON" type="string" />
    <property name="MobilIntRequestReason" column="MOBILINT_REQUEST_REASON" type="string" />
    <property name="OGGsmConfirm" column="OG_GSM_CONFIRM" type="long" />
    <property name="OGCepTelConfirm" column="OG_CEPTEL_CONFIRM" type="long" />
    <property name="OGMobilIntConfirm" column="OG_MOBILINT_CONFIRM" type="long" />
    <property name="OGGsmProfil" column="OG_GSM_PROFIL" type="long" />
    <property name="OGMobilIntKota" column="OG_MOBILINT_KOTA" type="long" />
    <property name="GsmConfirm" column="GSM_CONFIRM" type="long" />
    <property name="CepTelConfirm" column="CEPTEL_CONFIRM" type="long" />
    <property name="MobilIntConfirm" column="MOBILINT_CONFIRM" type="long" />
    <property name="GsmKota" column="GSM_KOTA" type="long" />
    <property name="MobilIntKota" column="MOBILINT_KOTA" type="long" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionID" column="VERSION_ID" type="long" />
    <property name="OGGsmProfilMakam" column="OG_GSMPROFIL_HIZMET" type="long" />
    <property name="OGGsmProfilHizmet" column="OG_GSMPROFIL_MAKAM" type="long" />
  </class>
</hibernate-mapping>