﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class GSMRequest : EntityBase
    {
        [ID]
        public virtual long RequestID { get; private set; }

        public virtual long RelatedLoginID { get; set; }
        public virtual long GsmRequest { get; set; }
        public virtual long CepTelRequest { get; set; }
        public virtual long MobilIntRequest { get; set; }
        public virtual long GsmRequestDuration { get; set; }
        public virtual long CepTelRequestDuration { get; set; }
        public virtual long MobilIntRequestDuration { get; set; }
        public virtual string GsmRequestReason { get; set; }
        public virtual string CepTelRequestReason { get; set; }
        public virtual string MobilIntRequestReason { get; set; }

        public virtual long OGGsmConfirm { get; set; }
        public virtual long OGCepTelConfirm { get; set; }
        public virtual long OGMobilIntConfirm { get; set; }
        public virtual long OGGsmProfil { get; set; }
        public virtual long OGMobilIntKota { get; set; }

        public virtual long OGGsmProfilHizmet { get; set; }
        public virtual long OGGsmProfilMakam { get; set; }

        public virtual long GsmConfirm { get; set; }
        public virtual long CepTelConfirm { get; set; }
        public virtual long MobilIntConfirm { get; set; }
        public virtual long GsmKota { get; set; }
        public virtual long MobilIntKota { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        public virtual long EmployeeType { get; set; }
        public virtual long Divison { get; set; }
        public virtual long Department { get; set; }
        public virtual long Unit { get; set; }
        public virtual long Team { get; set; }
        public virtual long DepartmentManager { get; set; }
        public virtual long DivisionManager { get; set; }
        public virtual long UnitManager { get; set; }
        public virtual long InnerServicesManager { get; set; }
        public virtual long GeneralManager { get; set; }
    }
}