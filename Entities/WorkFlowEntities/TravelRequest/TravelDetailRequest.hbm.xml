﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TravelDetailRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_Travel_DETAIL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="TRAVEL_DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="ACIKLAMA" column="ACIKLAMA" />
    <property name="NEREDEN" column="NEREDEN" />
    <property name="NEREYE" column="NEREYE" />
    <property name="GIDIS_TARIHI" column="GIDIS_TARIHI" />
    <property name="GIDIS_SAATI" column="GIDIS_SAATI" />
    <property name="DONUS_TARIHI" column="DONUS_TARIHI" />
    <property name="DONUS_SAATI" column="DONUS_SAATI" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>