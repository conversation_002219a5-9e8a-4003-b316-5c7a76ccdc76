﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TravelRequest : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual long PERSONEL_ID { get; set; }
        public virtual long YURT_ICI_DISI { get; set; }
        public virtual string ACIKLAMA { get; set; }
        public virtual long KONAKLAMA { get; set; }
        public virtual string KONAKLAMA_ACIKLAMA { get; set; }
        public virtual long ULASIM { get; set; }
        public virtual string ULASIM_ACIKLAMA { get; set; }
        public virtual long ARAC_KIRALAMA { get; set; }
        public virtual string ARAC_KIRALAMA_ACIKLAMA { get; set; }
        public virtual long GSM_PAKETI { get; set; }
        public virtual long GSM_DATA_PAKETI { get; set; }
        public virtual long AVANS_TALEBI { get; set; }
        public virtual string AVANS_TUTARI { get; set; }
        public virtual string AVANS_PARA_BIRIMI { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        public virtual string AVANS_BANKA { get; set; }
        public virtual string AVANS_HESAP_NO { get; set; }
        public virtual string FAZLA_AVANS_TUTAR { get; set; }

        public virtual long UcusSinifi { get; set; }

        #endregion
    }
}