﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TravelRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TRAVEL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="TRAVEL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="PERSONEL_ID" column="PERSONEL_ID" />
    <property name="YURT_ICI_DISI" column="YURT_ICI_DISI" />
    <property name="ACIKLAMA" column="ACIKLAMA" />
    <property name="KONAKLAMA" column="KONAKLAMA" />
    <property name="KONAKLAMA_ACIKLAMA" column="KONAKLAMA_ACIKLAMA" />
    <property name="ULASIM" column="ULASIM" />
    <property name="ULASIM_ACIKLAMA" column="ULASIM_ACIKLAMA" />
    <property name="ARAC_KIRALAMA" column="ARAC_KIRALAMA" />
    <property name="ARAC_KIRALAMA_ACIKLAMA" column="ARAC_KIRALAMA_ACIKLAMA" />
    <property name="GSM_PAKETI" column="GSM_PAKETI" />
    <property name="GSM_DATA_PAKETI" column="GSM_DATA_PAKETI" />
    <property name="AVANS_TALEBI" column="AVANS_TALEBI" />
    <property name="AVANS_TUTARI" column="AVANS_TUTARI" />
    <property name="AVANS_PARA_BIRIMI" column="AVANS_PARA_BIRIMI" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />

    <property name="AVANS_BANKA" column="AVANS_BANKA" />
    <property name="AVANS_HESAP_NO" column="AVANS_HESAP_NO" />
    <property name="FAZLA_AVANS_TUTAR" column="FAZLA_AVANS_TUTAR" />
	<property name="UcusSinifi" column="UCUS_SINIFI" />
	  
  </class>
</hibernate-mapping> 