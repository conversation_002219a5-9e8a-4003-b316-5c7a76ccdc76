﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TravelDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string ACIKLAMA { get; set; }
        public virtual string NEREDEN { get; set; }
        public virtual string NEREYE { get; set; }
        public virtual DateTime GIDIS_TARIHI { get; set; }
        public virtual string GIDIS_SAATI { get; set; }
        public virtual DateTime DONUS_TARIHI { get; set; }
        public virtual string DONUS_SAATI { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}