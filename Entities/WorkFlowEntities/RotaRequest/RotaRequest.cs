using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class RotaRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string GroupNoDescription { get; set; }
        public virtual long PersonelID { get; set; }
        public virtual string PersonelUserName { get; set; }
        public virtual long GroupNo { get; set; }
        public virtual long ReportNo { get; set; }
        public virtual string ReportNoList { get; set; }
        public virtual long SourcePersonelId { get; set; }
        public virtual long TargetPersonelId { get; set; }
        public virtual string Description { get; set; }
        public virtual string ProcessType { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual string TargetUserName { get; set; }
        public virtual string CopyUserName { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}