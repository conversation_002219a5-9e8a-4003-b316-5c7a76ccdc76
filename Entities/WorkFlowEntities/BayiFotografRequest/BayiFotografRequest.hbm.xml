﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BayiFotografRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BAYI_PERSONEL_FOTO_RQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="IrisID" column="IRIS_ID" />
    <property name="PersonelId" column="PERSONEL_ID" />
    <property name="FotoAdres" column="FOTO_ADRES" />
    <property name="CalistigiBayi" column="CALISTIGI_BAYI" />
    <property name="PersonelAdsoyad" column="PERSONEL_AD_SOYAD" />
    <property name="Gorevi" column="GOREVI" />
    <property name="CalismaSekli" column="CALISMA_SEKLI" />
    <property name="IseBaslamaTarihi" column="ISE_BASLAMA_TARIHI" />
    <property name="OncekiCalistigiBayi" column="ONCEKI_CALISTIGI_BAYI" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
	<property name="BayiId" column="BAYI_ID" />
	  <property name="TeknikServisYonetici" column="SERVIS_YONETICI" />
	  <property name="SatisTemsilcisi" column="SATIS_TEMSILCISI" />
  </class>
</hibernate-mapping>