﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiFotografRequest : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual long IrisID { get; set; }
        public virtual string PersonelId { get; set; }
        public virtual string FotoAdres { get; set; }
        public virtual string CalistigiBayi { get; set; }
        public virtual string PersonelAdsoyad { get; set; }
        public virtual string Gorevi { get; set; }
        public virtual string CalismaSekli { get; set; }
        public virtual DateTime? IseBaslamaTarihi { get; set; }
        public virtual string OncekiCalistigiBayi { get; set; }          
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string BayiId { get; set; }
        public virtual string TeknikServisYonetici { get; set; }
        public virtual string SatisTemsilcisi { get; set; }

        #endregion
    }
}