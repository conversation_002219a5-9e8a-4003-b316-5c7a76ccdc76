<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="PaymentRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PAYMENT_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="PAYMENT_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="CompanyName" column="COMPANY_NAME" />
    <property name="RequestDate" column="REQUEST_DATE" />
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="RequestType" column="REQUEST_TYPE" />
    <property name="BillComingDate" column="BILL_COMING_DATE" />
    <property name="PurchaseRequestNo" column="PURCHASE_REQUEST_NO" />
    <property name="PurchaseAmount" column="PURCHASE_AMOUNT" />
    <property name="PurchaseOwnerNameSurname" column="PURCHASE_OWNER_NAME_SURNAME" />
    <property name="PurchaseType" column="PURCHASE_TYPE" />
    <property name="PurchaseDate" column="PURCHASE_DATE" />
    <property name="IsSupplementaryBudget" column="IS_SUPPLEMENTARY_BUDGET" />
    <property name="SupplementaryBudgets" column="SUPPLEMENTARY_BUDGETS" />
    <property name="PurchaseBudgets" column="PURCHASE_BUDGETS" />
    <property name="AddPurchaseRequestNo" column="ADDITIONAL_PURCHASE_NO" />
    <property name="AddPurchaseAmount" column="ADD_PURCHASE_AMOUNT" />
    <property name="AddPurchaseOwnerNameSurname" column="ADDPURCHASE_OWNER_NAME_SURNAME" />
    <property name="AddPurchaseType" column="ADD_PURCHASE_TYPE" />
    <property name="IsAddSupplementaryBudget" column="IS_ADD_SUPPLEMENTARY_BUDGET" />
    <property name="AddSupplementaryBudgets" column="ADD_SUPPLEMENTARY_BUDGETS" />
    <property name="AddPurchaseBudgets" column="ADD_PURCHASE_BUDGETS" />
    <property name="NotInRD" column="NOT_IN_RD" />
    <property name="RDProjectCode" column="RD_PROJECT_CODE" />
    <property name="RDSubProjectCode" column="RD_SUB_PROJECT_CODE" />
    <property name="ServiceType" column="SERVICE_TYPE" />
    <property name="PurchaseDescription" column="PURCHASE_DESCRIPTION" />
    <property name="Notes" column="NOTES" />
    <property name="Purchaser" column="PURCHASER" />
    <property name="CurrentAccountNo" column="CURRENT_ACCOUNT_NO" />
    <property name="PaymentType" column="PAYMENT_TYPE" />
    <property name="PaymentPeriod" column="PAYMENT_PERIOD" />
    <property name="PaymentDate" column="PAYMENT_DATE" />
    <property name="PaymentBank" column="PAYMENT_BANK" />
    <property name="IBAN" column="IBAN" />
    <property name="PaymentAmount" column="PAYMENT_AMOUNT" />
    <property name="PaymentAmountTLWithNoVAT" column="PAYMENT_AMOUNT_TL_WITH_NO_VAT" />
    <property name="PaymentCurrency" column="PAYMENT_CURRENCY" />
    <property name="InLettering" column="IN_LETTERING" />
    <property name="VATRatio" column="VAT_RATIO" />
    <property name="IsNoVAT" column="IS_NO_VAT" />
    <property name="ExchangeRateBuy" column="EXCHANGE_RATE_BUY" />
    <property name="ExchangeRateSell" column="EXCHANGE_RATE_SELL" />
    <property name="IsContract" column="IS_CONTRACT" />
    <property name="ContractDetail" column="CONTRACT_DETAIL" />
    <property name="IsDEliveryForm" column="IS_DELIVERY_FORM" />
    <property name="IsDispatch" column="IS_DISPATCH" />
    <property name="DispatchDate" column="DISPACH_DATE" />
    <property name="DispatchId" column="DISPATCH_ID" />
    <property name="IsBill" column="IS_BILL" />
    <property name="BillDate" column="BILL_DATE" />
    <property name="BilLId" column="BILL_ID" />
    <property name="InsertFileName" column="INSERT_FILE_NAME" />

    <property name="IsExistContract" column="IS_EXIST_CONTRACT" />
    <property name="IsSuitableContract" column="IS_SUITABLE_CONTRACT" />
    <property name="ContractDescription" column="CONTRACT_DESCRIPTION" />
    <property name="IsSuitableOrder" column="IS_SUITABLE_ORDER" />
    <property name="OrderDescription" column="ORDER_DESCRIPTION" />

    <property name="PrNo" column="PR_NO" />
    <property name="PoNo" column="PO_NO" />
    <property name="PrNoSahibi" column="PR_NO_SAHIBI" />
    <property name="PoNoSahibi" column="PO_NO_SAHIBI" />
    <property name="PrNoDept" column="PR_NO_DEPT" />
    <property name="PoNoDept" column="PO_NO_DEPT" /> 

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>