﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="PaymentDetails, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PAYMENT_DETAILS" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="PurchaseType" column="PURCHASE_TYPE" />
    <property name="PurchaseInstanceId" column="PURCHASE_INSTANCE_ID" />
    <property name="RelatedRequestID" column="PAYMENT_ENTITY_REF_ID" />
    <property name="PurchaseAmount" column="PURCHASE_AMOUNT" />
    <property name="PurchaseAmountWithOption" column="PURCHASE_AMOUNT_WITH_OPTION" />
    <property name="PaymentAmount" column="PAYMENT_AMOUNT" />
    <property name="CurrentAmount" column="CURRENT_AMOUNT" />
    <property name="Status" column="STATUS" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
  </class>
</hibernate-mapping>