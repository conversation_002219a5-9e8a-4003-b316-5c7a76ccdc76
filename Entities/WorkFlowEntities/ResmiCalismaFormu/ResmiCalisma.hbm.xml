<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ResmiCalisma, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_RESMI_CALISMA_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="TALEP_EDEN" type="long" />
    <property name="Sicil" column="SICIL" type="decimal" />    
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
	<property name="LastUpdated" column="LAST_UPDATED" />
	<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
	<property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>