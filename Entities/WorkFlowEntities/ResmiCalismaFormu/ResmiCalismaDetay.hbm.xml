<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="ResmiCalismaDetay, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_RESMI_CALISMA_DET_REQ" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="REQUEST_ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
        <property name="CalismaTarih" column="CALISMA_TARIH" />
        <property name="BaslangicSaat" column="BASLANGIC_SAAT" />
        <property name="BitisSaat" column="BITIS_SAAT" />
        <property name="CalismaSure" column="CALISMA_SURE" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="VersionID" column="VERSION_ID" />
	    <property name="GorevYeri" column="GOREV_YERI" />
		 <property name="Aciklama" column="ACIKLAMA" />
      </class>
    </hibernate-mapping>

