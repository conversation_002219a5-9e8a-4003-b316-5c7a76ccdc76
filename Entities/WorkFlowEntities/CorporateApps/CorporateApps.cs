﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class CorporateApps : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual string KULLANIM_AMACI { get; set; }
        public virtual string EKLI_DOSYA { get; set; }
        public virtual string DETAYLI_ACIKLAMA { get; set; }
        public virtual DateTime TESLIM_TALEP_TARIHI { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}