﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BayiKesinHesapTalepDetay, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BAYI_KESIN_HESAP_DT_RQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_DETAIL_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />

    <property name="BayiAd" column="BAYI_AD" />
    <property name="BayiKod" column="BAYI_KOD" />
    <property name="DepoKod" column="DEPO_KOD" />
    <property name="BayiAdres" column="BAYI_ADRES" />
    <property name="BayiIl" column="BAYI_IL" />
    <property name="BayiBolge" column="BAYI_BOLGE" />
    <property name="BayiBolgeAd" column="BAYI_BOLGE_AD" />
    <property name="BayiTemsilcisi" column="BAYI_TEMSILCISI" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="BayiInaktifTarihi" column="BAYI_INAKTIF_TARIHI" />
    <property name="GeciciBitti" column="GECICI_BITTI" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>