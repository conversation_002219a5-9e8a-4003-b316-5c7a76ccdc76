﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiKesinHesapTalepDetay : EntityBase, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string BayiAd { get; set; }
        public virtual string BayiKod { get; set; }
        public virtual string DepoKod { get; set; }
        public virtual string BayiAdres { get; set; }
        public virtual string BayiIl { get; set; }
        public virtual long BayiBolge { get; set; }
        public virtual string BayiBolgeAd { get; set; }
        public virtual long BayiTemsilcisi { get; set; }
        public virtual string Aciklama { get; set; }
        public virtual DateTime BayiInaktifTarihi { get; set; }
        public virtual string GeciciBitti { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}