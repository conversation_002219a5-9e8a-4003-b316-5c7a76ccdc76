﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiKesinHesapTalepOnayDetay : EntityBase, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string <PERSON>cikla<PERSON> { get; set; }
        public virtual string Borc { get; set; }
        public virtual string Alacak { get; set; }
        public virtual string Dosya { get; set; }
        public virtual string Onay { get; set; }
        public virtual string KesmeBelge { get; set; }
        public virtual string KesmeFatura { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}