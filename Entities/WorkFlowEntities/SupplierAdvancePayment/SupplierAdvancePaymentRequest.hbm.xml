﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SupplierAdvancePaymentRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SUPPLIER_ADV_PAY_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="TalepTipi" column="TALEP_TIPI" />
    <property name="OdemeSekli" column="ODEME_SEKLI" />
    <property name="Banka" column="BANKA" />
    <property name="Iban" column="IBAN" />
    <property name="OdemeTarihi" column="ODEME_TARIHI" />
    <property name="TeminatAciklama" column="TEMINAT_ACIKLAMA" />
    <property name="HakedisTutar" column="HAKEDIS_TUTAR" />
    <property name="SenetHakedis" column="SENET_HAKEDIS" />
    <property name="TaksitSayisi" column="TAKSIT_SAYISI" />
    <property name="BolgeID" column="BOLGE_ID" />
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="Firma" column="FIRMA" />
    <property name="TutarBirim" column="TUTAR_BIRIM" />
    <property name="TahminiTutar" column="TAHMINI_TUTAR" />
    <property name="TahminiTutarBirim" column="TAHMINI_TUTAR_BIRIM" />
    <property name="TeminatVarmi" column="TEMINAT_VAR" />
    <property name="Bayi" column="BAYI" />
    <property name="Alici" column="ALICI" />
    <property name="TalepTutar" column="TALEP_TUTAR" />
    <property name="TalepTutarBirim" column="TALEP_TUTAR_BIRIM" />
    <property name="TalepNedeni" column="TALEP_NEDENI" />
	<property name="UnvanDetay" column="UNVAN_DETAY" />
	<property name="VergiNo" column="VERGI_NO" />
  </class>
</hibernate-mapping>