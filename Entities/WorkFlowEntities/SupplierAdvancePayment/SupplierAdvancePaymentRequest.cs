﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SupplierAdvancePaymentRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long TalepTipi { get; set; }
        public virtual long OdemeSekli { get; set; }
        public virtual string Banka { get; set; }
        public virtual string Iban { get; set; }
        public virtual DateTime OdemeTarihi { get; set; }
        public virtual string TeminatAciklama { get; set; }
        public virtual decimal HakedisTutar { get; set; }
        public virtual long SenetHakedis { get; set; }
        public virtual long TaksitSayisi { get; set; }
        public virtual long BolgeID { get; set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string Firma { get; set; }
        public virtual string TutarBirim { get; set; }
        public virtual decimal TahminiTutar { get; set; }
        public virtual string TahminiTutarBirim { get; set; }
        public virtual long TeminatVarmi { get; set; }
        public virtual string Bayi { get; set; }
        public virtual string Alici { get; set; }
        public virtual decimal TalepTutar { get; set; }
        public virtual string TalepTutarBirim { get; set; }
        public virtual string TalepNedeni { get; set; }
        public virtual string UnvanDetay { get; set; }
        public virtual string VergiNo { get; set; }

        #endregion Entity Properties
    }
}