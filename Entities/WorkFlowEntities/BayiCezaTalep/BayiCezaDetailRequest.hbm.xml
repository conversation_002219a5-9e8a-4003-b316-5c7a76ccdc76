﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BayiCezaDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BAYI_CEZA_DETAIL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="CezaTipi" column="CEZA_TIPI" />
    <property name="UyeNo" column="UYE_NO" />
    <property name="UyeAdSoyad" column="UYE_AD_SOYAD" />
    <property name="TahsilEdilenUcret" column="TAHSIL_EDILEN_UCRET" />
    <property name="TahsilEdilmesiGerekenUcret" column="TAHSIL_EDILMESI_GEREKEN_UCRET" />
    <property name="KesilecekCezaTutar" column="KESILECEK_CEZA_TUTAR" />
    <property name="TahsilEdilenParaBirim" column="TAHSIL_EDILEN_PB" />
    <property name="TahsilEdilmesiGerekenParaBirim" column="TAHSIL_EDILMESI_GEREKEN_PB" />
    <property name="CezaParaBirim" column="KESILECEK_CEZA_PB" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="DokumanLink" column="DOKUMAN_LINK" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="CezaTipiId" column="CEZA_TIPI_ID" />
  </class>
</hibernate-mapping>