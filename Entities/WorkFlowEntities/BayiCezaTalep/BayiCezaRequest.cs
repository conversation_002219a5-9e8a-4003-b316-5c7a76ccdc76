﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiCezaRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual string Il { get; set; }
        public virtual string <PERSON>ce { get; set; }
        public virtual string <PERSON>halle { get; set; }
        public virtual string Bayi<PERSON>odu { get; set; }
        public virtual string BayiAdi { get; set; }
        public virtual string VergiNo { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string FATURA_NO { get; set; }
        public virtual string FATURA_ACIKLAMA { get; set; }
        #endregion Entity Properties
    }
}