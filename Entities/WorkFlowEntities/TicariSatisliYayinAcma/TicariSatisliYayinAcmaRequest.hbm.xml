<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="TicariSatisliYayinAcmaRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_TICARI_YAYIN_ACMA" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="AccountNumber" column="ACCOUNTNUMBER" />
    <property name="ProspectNumber" column="PROSPECTNUMBER" />
    <property name="Ad" column="AD" />
    <property name="Soyad" column="SOYAD" />
    <property name="AdresUlke" column="ADRESULKE" />
    <property name="AdresIl" column="ADRESIL" />
    <property name="AdresIlce" column="ADRESILCE" />
    <property name="AdresDetay" column="ADRESDETAY" />
    <property name="AdresUAVTId" column="ADRESUAVTID" />
    <property name="AdresGPSLokasyon" column="ADRESGPS_LOKASYON" />
    <property name="BayiKodu" column="BAYIKODU" />
    <property name="BayiAdi" column="BAYIADI" />
    <property name="BayiPersonelKodu" column="BAYIPERSONELKODU" />
    <property name="BayiAdresIl" column="BAYIADRESIL" />
    <property name="BayiAdresIlce" column="BAYIADRESILCE" />
    <property name="BayiBolgeKodu" column="BAYIBOLGEKODU" />
    <property name="BayiBolgeAdi" column="BAYIBOLGEADI" />
    <property name="BayiTemsilciDbsKodu" column="BAYITEMSILCIDBSKODU" />
    <property name="IrisKayitId" column="IRISKAYITID" />
    <property name="UyduTipi" column="UYDUTIPI" />
    <property name="TicariGrupKod" column="TICARIGRUPKOD" />
    <property name="TicariGrupAdi" column="TICARIGRUPADI" />
    <property name="OdemeTipi" column="ODEMETIPI" />
    <property name="OdemeTipiDescr" column="ODEMETIPIDESCR" />
    <property name="SimpleOfferId" column="SIMPLEOFFERID" />
    <property name="SimpleOfferIdDescr" column="SIMPLEOFFERIDDESCR" />
    <property name="BundleOfferId" column="BUNDLEOFFERID" />
    <property name="BundleOfferIdDescr" column="BUNDLEOFFERIDDESCR" />
    <property name="Frekans" column="FREKANS" />
    <property name="FrekansDescr" column="FREKANSDESCR" />
    <property name="IndirimOrani" column="INDIRIMORANI2" />
    <property name="IndirimOraniDegeri" column="INDIRIMORANIDEGERI" />
    <property name="ListeFiyati" column="LISTEFIYATI" />
    <property name="IndirimUygulanmisFiyat" column="INDIRIMUYGULANMISFIYAT" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="Offerbusinessinterid" column="OFFERBUSINESSINTERID" />
    <property name="Alterationbusinessinterid" column="ALTERATIONBUSINESSINTERID" />
    <property name="ServiceAccountId" column="SERVICEACCOUNTID" />
    <property name="DbsoutletLocation" column="DBSOUTLETLOCATION" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>