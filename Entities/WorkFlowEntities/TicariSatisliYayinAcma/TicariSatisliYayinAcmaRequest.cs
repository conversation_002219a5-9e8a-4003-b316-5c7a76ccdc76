using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class TicariSatisliYayinAcmaRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long WorkFlowInstanceId { get; set; }
        public virtual long AccountNumber { get; set; }
        public virtual long ProspectNumber { get; set; }
        public virtual string Ad { get; set; }
        public virtual string Soyad { get; set; }
        public virtual string AdresUlke { get; set; }
        public virtual string AdresIl { get; set; }
        public virtual string AdresIlce { get; set; }
        public virtual string AdresDetay { get; set; }
        public virtual long AdresUAVTId { get; set; }
        public virtual string AdresGPSLokasyon { get; set; }
        public virtual string BayiKodu { get; set; }
        public virtual string BayiAdi { get; set; }
        public virtual string BayiPersonelKodu { get; set; }
        public virtual string BayiAdresIl { get; set; }
        public virtual string BayiAdresIlce { get; set; }
        public virtual string BayiBolgeKodu { get; set; }
        public virtual string BayiBolgeAdi { get; set; }
        public virtual string BayiTemsilciDbsKodu { get; set; }
        public virtual long IrisKayitId { get; set; }
        public virtual string UyduTipi { get; set; }
        public virtual string TicariGrupKod { get; set; }
        public virtual string TicariGrupAdi { get; set; }
        public virtual string OdemeTipi { get; set; }
        public virtual string OdemeTipiDescr { get; set; }
        public virtual long SimpleOfferId { get; set; }
        public virtual string SimpleOfferIdDescr { get; set; }
        public virtual long BundleOfferId { get; set; }
        public virtual string BundleOfferIdDescr { get; set; }
        public virtual string Frekans { get; set; }
        public virtual string FrekansDescr { get; set; }
        public virtual string IndirimOrani { get; set; }
        public virtual decimal IndirimOraniDegeri { get; set; }
        public virtual decimal ListeFiyati { get; set; }
        public virtual decimal IndirimUygulanmisFiyat { get; set; }
        public virtual string Aciklama { get; set; }
        public virtual long Offerbusinessinterid { get; set; }
        public virtual long Alterationbusinessinterid { get; set; }
        public virtual long ServiceAccountId { get; set; }
        public virtual string DbsoutletLocation { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}