﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobLeavingFormRequest : EntityBase, IEntity
    {
        #region Entity Properties
        //public long RequestId
        //{
        //    get
        //    {
        //        throw new NotImplementedException();
        //    }
        //}
        public virtual long RequestId { get; set; }
        public virtual string FullName { get; set; }
        public virtual DateTime LeavingDate { get; set; }
        public virtual string Division { get; set; }
        public virtual string Department { get; set; }
        public virtual string Unit { get; set; }
        public virtual long ManagerId { get; set; }
        public virtual string Location { get; set; }
        public virtual long PersonalTypeId { get; set; }
        public virtual string Floor { get; set; }
        public virtual long DonePoldy { get; set; }
        public virtual long DoneInsuranceQuit { get; set; }
        public virtual long DoneIntranetQuit { get; set; }
        public virtual long DoneSodexoQuit { get; set; }
        public virtual long DoneLifeInsuranceQuit { get; set; }
        public virtual long TakenIdCard { get; set; }
        public virtual long TakenCompanyCar { get; set; }
        public virtual long TakenCarryMatic { get; set; }
        public virtual long TakenGSMCard { get; set; }
        public virtual long TakenMobilePhone { get; set; }
        public virtual long IsEveryThingOk { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual long SalaryAdvanceExist { get; set; }  // Maaş Avansı
        public virtual decimal SalaryAdvanceAmount { get; set; }
        public virtual long JobAdvanceExist { get; set; }// İş Avansı
        public virtual decimal JobAdvanceAmount { get; set; }
        public virtual long LoanRePaymentExist { get; set; }// Kredi Geri Ödemesi
        public virtual decimal LoadRePaymentAmount { get; set; }
        public virtual long CostPaymentExist { get; set; }// Masraf Geri Ödemesi
        public virtual decimal CostPaymentAmount { get; set; }
        public virtual long EmployeeDiscountRemoved { get; set; }
        public virtual long TestDevicesTracked { get; set; }

        public virtual long TakenTechComputer { get; set; }
        public virtual long TakenTechDeskPhone { get; set; }
        public virtual long TakenTechMailAccount { get; set; }
        public virtual long TakenTechUsername { get; set; }
        public virtual long TakenTechOutlook { get; set; }
        public virtual long TakenTechOtherStaff { get; set; }
        public virtual string TakenTechOtherStaffDesc { get; set; }
        public virtual long TakenTechBrokenProduct { get; set; }
        public virtual string TakenTechBrokenProductDesc { get; set; }

        public virtual long TakenMaterial { get; set; }

        public virtual long HayatSigortasiCikisi { get; set; }
        public virtual long BireyselEmeklilikCikisi { get; set; }
        public virtual long YakaKartiGeriAlindi { get; set; }

        #endregion Entity Properties
    }
}