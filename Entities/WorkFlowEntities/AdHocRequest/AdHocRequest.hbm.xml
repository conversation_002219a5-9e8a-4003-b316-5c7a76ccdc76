<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="AdHocRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ADHOC_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="DESCRIPTION" column="DESCRIPTION" />
    <property name="HUKUK" column="HUKUK" />
    <property name="FILE_PATH" column="FILE_PATH" />
    <property name="YETKI_BITIS" column="YETKI_BITIS" />
    <property name="RAPOR_VERI" column="RAPOR_VERI" />
    <property name="RAPOR_VERI_FIRMA" column="RAPOR_VERI_FIRMA" />
    <property name="SEGMENT" column="SEGMENT" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>