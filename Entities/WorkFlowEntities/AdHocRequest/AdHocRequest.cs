using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class AdHocRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string DESCRIPTION { get; set; }
        public virtual string HUKUK { get; set; }
        public virtual string FILE_PATH { get; set; }
        public virtual DateTime YETKI_BITIS { get; set; }
        public virtual string RAPOR_VERI { get; set; }
        public virtual string RAPOR_VERI_FIRMA { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        public virtual string YETKI { get; set; }
        public virtual string SEGMENT { get; set; }

        #endregion Entity Properties
    }
}