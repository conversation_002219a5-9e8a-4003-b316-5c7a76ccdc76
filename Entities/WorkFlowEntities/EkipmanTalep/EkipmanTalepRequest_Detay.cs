﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EkipmanTalepRequest_Detay : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string Name_Surname { get; set; }
        public virtual string Bolge { get; set; }
        public virtual string Il { get; set; }
        public virtual string Yts_Adi { get; set; }
        public virtual string Depo_Kodu { get; set; }
        public virtual long Ekipman_Secimi { get; set; }
        public virtual string Ekipman_Turu { get; set; }
        public virtual long Ekipman_Adet { get; set; }
        public virtual string Aciklama { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
    }
}
