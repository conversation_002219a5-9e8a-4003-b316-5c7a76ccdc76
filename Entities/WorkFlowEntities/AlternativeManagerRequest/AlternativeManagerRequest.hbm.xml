﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="AlternativeManagerRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ALT_MANAGER_REQUEST" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="REQUEST_ID">
			<generator class="trigger-identity"></generator>
		</id>
		<property name="RequestOwnerId" column="REQUEST_OWNER_ID" />
		<property name="PersonnelId" column="PERSONNEL_ID" />
		<property name="PersonnelRealManagerId" column="PERSONNEL_REAL_MANAGER_ID" />
		<property name="PersonnelDelegatedNewManagerId" column="DELEGATED_NEW_MANAGER_ID" />
		<property name="StartDate" column="START_DATE" />
		<property name="EndDate" column="END_DATE" />
		<property name="RequestComment" column="REQUEST_COMMENT" />
		<property name="WorkFlowDefIds" column="WORKFLOW_DEF_IDS" />
		<property name="Created" column="CREATED" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
		<property name="VersionID" column="VERSION_ID" />
	</class>
</hibernate-mapping>


