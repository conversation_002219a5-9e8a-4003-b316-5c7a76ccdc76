<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="Digiturk.Workflow.Digiflow.Entities.SupplierOperationEndReminderMailDetail, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SUPPOPER_END_REMINDER_DET" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="WorkflowId" column="WORKFLOW_ID" />
    <property name="RequestedBy" column="REQUESTED_BY" />
    <property name="ClosingDate" column="CLOSING_DATE" />
    <property name="Description" column="DESCRIPTION" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="RequestedByName" column="REQUESTED_BY_NAME" />
  </class>
</hibernate-mapping>