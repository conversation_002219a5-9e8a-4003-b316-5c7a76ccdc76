﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{ABC3DDA4-168E-4378-84BB-7CA3B8C56D90}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Entities</RootNamespace>
    <AssemblyName>Entities</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Entity_Base">
      <HintPath>\\dtl1iis3\Deployment\Entity_Base.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AracTakipSistemi\ATS_ARAC_MARKASI.cs" />
    <Compile Include="AracTakipSistemi\ATS_ARAC_MODELI.cs" />
    <Compile Include="AracTakipSistemi\ATS_ARAC_PLAKASI.cs" />
    <Compile Include="AracTakipSistemi\ATS_ARAC_RENGI.cs" />
    <Compile Include="AracTakipSistemi\ATS_ARAC_SINIFI.cs" />
    <Compile Include="AracTakipSistemi\ATS_ARAC_YILI.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_ANASAYFA_DUYURU.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_ANASAYFA_SOLSAG_SLIDE.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE .cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_ANA_SOL_MENU.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_HR_MEDIA_SLIDE.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_INDIRIM_FIRST.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_INDIRIM_KAT.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_LINKLER.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_MENU_NAME.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_MENU_TYPE.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_SLIDER_KAT.cs" />
    <Compile Include="DigiportAdmin\DIGIPORT_ADMIN_SLIDER_OPTIONS.cs" />
    <Compile Include="DigiportAnket\DIGIPORT_ANKET_DOMAIN.cs" />
    <Compile Include="DigiportAnket\DIGIPORT_ANKET_DOMAIN_USERS.cs" />
    <Compile Include="EkipmanTalepFormu\DF_EKIPMAN_EXCEL_DOSYA.cs" />
    <Compile Include="EkipmanTalepFormu\DF_EKIPMAN_EXCEL_DOSYA_VERI.cs" />
    <Compile Include="EkipmanTalepFormu\MalzemeTanim.cs" />
    <Compile Include="EkipmanTalepFormu\Malzeme_Tanimlama.cs" />
    <Compile Include="EkipmanTalepFormu\YtsTanimlama.cs" />
    <Compile Include="KurumsalKirtasiye\KRT_DEPO.cs" />
    <Compile Include="KurumsalKirtasiye\KRT_FIRMA.cs" />
    <Compile Include="KurumsalKirtasiye\KRT_STOK_DETAY.cs" />
    <Compile Include="KurumsalKirtasiye\KRT_STOK_GRUP.cs" />
    <Compile Include="Muhaberat\MHBRT_REGISTRY_ITEM_TYPE.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TestMate\TMATE_DOMAINS_ADMINS.cs" />
    <Compile Include="TestMate\TMATE_PROJECTS.cs" />
    <Compile Include="TestMate\TMATE_PROJECT_USERS.cs" />
    <Compile Include="TestMate\TMATE_STATUS.cs" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment i.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>