﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Muhaberat
{
    public class MHBRT_REGISTRY_ITEM_TYPE : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public string ITEM_TYPE { get; set; }
        public string OFFICIAL { get; set; }
        public string INOFFICIAL { get; set; }
        public string IS_ACTIVE { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> UPDATED { get; set; }
        public System.Nullable<decimal> UPDATED_BY { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.MHBRT_REGISTRY_ITEM_TYPE (ITEM_TYPE,OFFICIAL,INOFFICIAL,IS_ACTIVE,CREATED,CREATED_BY,UPDATED,UPDATED_BY) values (:ITEM_TYPE,:OFFICIAL,:INOFFICIAL,:IS_ACTIVE,:CREATED,:CREATED_BY,:UPDATED,:UPDATED_BY)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.MHBRT_REGISTRY_ITEM_TYPE set  ITEM_TYPE=:ITEM_TYPE,OFFICIAL=:OFFICIAL,INOFFICIAL=:INOFFICIAL,IS_ACTIVE=:IS_ACTIVE,CREATED=:CREATED,CREATED_BY=:CREATED_BY,UPDATED=:UPDATED,UPDATED_BY=:UPDATED_BY where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.MHBRT_REGISTRY_ITEM_TYPE  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.MHBRT_REGISTRY_ITEM_TYPE  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
