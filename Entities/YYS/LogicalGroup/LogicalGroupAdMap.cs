﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities.YYS.LogicalGroup
{
    public class LogicalGroupAdMap
    {
        public virtual long YYYSAdMemberId { get; set; }        
        public virtual long LogicalGroupId { get; set; }
        public virtual string AdDomain { get; set; }
        public virtual string AdGroup { get; set; }
        public virtual DateTime LastSenkDate { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime? LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }
        
    }
}
