﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="LogicalGroupMember, Digiturk.Workflow.Digiflow.Entities" table="YYS_LOGICAL_GROUP_MEMBERS" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="LOGICAL_GROUP_MEMBER_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="LogicalGroupMemberTypeId"  column="LOGICAL_GROUP_MEMBER_TYPE_ID" type="long" />
    <property name="LogicalGroupId"  column="LOGICAL_GROUP_ID" type="long" />
    <property name="Content" column="CONTENT" type="string" />
    <property name="Description" column="DESCRIPTION" type="string" />
    <property name="LoginId" column="LOGIN_ID" type="long" />
    <property name="FullName" column="FULLNAME" type="string" />
    <property name="Email" column="EMAIL" type="string" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionId" column="VERSION_ID" type="long" />
    <!--<property name="IsAdTransfer" column="IS_ADTRANSFER" type="long" />-->
    <property name="YYS_LG_AD_MEMBERS_ID" column="YYS_LG_AD_MEMBERS_ID" type="long" />
  </class>
</hibernate-mapping>