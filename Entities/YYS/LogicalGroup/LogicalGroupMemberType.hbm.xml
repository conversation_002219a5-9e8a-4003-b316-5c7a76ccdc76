﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="LogicalGroupMemberType, Digiturk.Workflow.Digiflow.Entities" table="YYS_LOGICAL_GROUP_MEMBER_TYPES" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="LOGICAL_GROUP_MEMBER_TYPE_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="Description" column="DESCRIPTION" type="string" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionId" column="VERSION_ID" type="long" />
    <property name="DescriptionTR" column="Description_TR" type="string" />
  </class>
</hibernate-mapping>