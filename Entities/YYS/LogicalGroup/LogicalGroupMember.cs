﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class LogicalGroupMember : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long LogicalGroupMemberTypeId { get; set; }
        public virtual long LogicalGroupId { get; set; }
        public virtual string Content { get; set; }
        public virtual string Description { get; set; }
        public virtual long LoginId { get; set; }
        public virtual string FullName { get; set; }
        public virtual string Email { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }
        public virtual long YYS_LG_AD_MEMBERS_ID { get; set; }

        //public virtual long IsAdTransfer { get; set; }

        #endregion Entity Properties
    }
}