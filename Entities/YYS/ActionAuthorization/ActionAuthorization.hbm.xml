﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ActionAuthorization, Digiturk.Workflow.Digiflow.Entities" table="YYS_ACTION_AUTHORIZATION" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ACTION_AUTHORIZATION_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="WfDefId"  column="WF_DEF_ID" type="long" />
    <property name="StateDefId"  column="STATE_DEF_ID" type="long" />
    <property name="ActionId"  column="ACTION_ID" type="long" />
    <property name="SourceId"  column="SOURCE_ID" type="long" />
    <property name="ToGroupId"  column="TO_GROUP_ID" type="long" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionId" column="VERSION_ID" type="long" />
    <property name="IsActive" column="IS_ACTIVE" type="long" />
  </class>
</hibernate-mapping>