﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    /// <summary>
    /// Bir iş akışının belirli bir state inde kullanıcının hangi action ı yapabileceğinin tanımlanır
    /// </summary>
    public class ActionAuthorization : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long WfDefId { get; set; }
        public virtual long StateDefId { get; set; }
        public virtual long ActionId { get; set; }
        public virtual long SourceId { get; set; }
        public virtual long ToGroupId { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }
        public virtual long IsActive { get; set; }

        #endregion Entity Properties
    }
}