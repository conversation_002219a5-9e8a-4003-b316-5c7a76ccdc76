﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    /// <summary>
    /// Rapor <PERSON>p - Kurum<PERSON> Uygulam<PERSON> talep vs admin ekranlarının uygulamalarının tutulduğu entity
    /// </summary>
    public class RaporTalepAdmin : EntityBase, IEntity
    {
        #region Entities

        public virtual long RequestId { get; set; }
        public virtual string STRTIPI { get; set; }
        public virtual string STRTURU { get; set; }
        public virtual string STRUYGULAMA { get; set; }
        public virtual string STRGOSTER { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }

        #endregion Entities
    }
}