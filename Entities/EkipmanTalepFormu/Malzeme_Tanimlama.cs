﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities
{
    public class Malzeme_Tanimlama : Entity_Base.EntityBase
    {

        public string MALZEME_TURU { get; set; }
        public string MALZEME_KODU { get; set; }
        public string MALZEME_ADI { get; set; }
        public string MALZEME_ACIKLAMA { get; set; }
        public System.Nullable<decimal> DURUM { get; set; }
        public DateTime KAYIT_TARIHI { get; set; }
        public DateTime DEGISTIRME_TARIHI { get; set; }
        public System.Nullable<decimal> KAY<PERSON>DEN { get; set; }
        public System.Nullable<decimal> DEGISTIREN { get; set; }

        public override string DELETE_SQL()
        {
            string Insert_SQL = "Delete from DT_WORKFLOW.DF_EKIPMAN_MALZEME_TANIMLAMA where ID=:ID ";
            return Insert_SQL;
        }

        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DF_EKIPMAN_MALZEME_TANIMLAMA (MALZEME_TURU,MALZEME_KODU,MALZEME_ADI,MALZEME_ACIKLAMA,DURUM,KAYIT_TARIHI,DEGISTIRME_TARIHI,KAYDEDEN,DEGISTIREN) values (:MALZEME_TURU,:MALZEME_KODU,:MALZEME_ADI,:MALZEME_ACIKLAMA,:DURUM,:KAYIT_TARIHI,:DEGISTIRME_TARIHI,:KAYDEDEN,:DEGISTIREN)";
            return Insert_SQL;
        }

        public override string SELECT_SQL()
        {
            string Insert_SQL = "select * from DT_WORKFLOW.DF_EKIPMAN_MALZEME_TANIMLAMA where ID=:ID";
            return Insert_SQL;
        }

        public override string UPDATE_SQL()
        {
            string Insert_SQL = "update DT_WORKFLOW.DF_EKIPMAN_MALZEME_TANIMLAMA set MALZEME_TURU=:MALZEME_TURU,MALZEME_KODU=:MALZEME_KODU,MALZEME_ADI=:MALZEME_ADI,MALZEME_ACIKLAMA=:MALZEME_ACIKLAMA,DURUM=:DURUM,KAYIT_TARIHI=:KAYIT_TARIHI,DEGISTIRME_TARIHI=:DEGISTIRME_TARIHI,KAYDEDEN=:KAYDEDEN,DEGISTIREN=:DEGISTIREN where ID=:ID";
            return Insert_SQL;
        }
    }
}
