﻿///
///Generated by KB Entity Generator 
///17.10.2022 15:14:52
///
using System;
namespace Entities.EkipmanTalepFormu
{
    public class DF_EKIPMAN_EXCEL_DOSYA_VERI : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal DOSYA_ID { get; set; }
        public int BAYI_ID { get; set; }
        public int MALZEME_ID { get; set; }

        public System.Nullable<decimal> ORTALAMA { get; set; }
        public System.Nullable<decimal> DEPO_ADET { get; set; }
        public string CREATED_BY { get; set; }
        public System.Nullable<DateTime> CREATED { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA_VERI (DOSYA_ID,BAYI_ID,MALZEME_ID,ORTALAMA,DEPO_ADET,CREATED_BY,CREATED) values (:DOSYA_ID,:BAYI_ID,:MALZEME_ID,:ORTALAMA,:DEPO_ADET,:CREATED_BY,:CREATED)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA_VERI set  DOSYA_ID=:DOSYA_ID,BAYI_ID=:BAYI_ID,MALZEME_ID=:MALZEME_ID,ORTALAMA=:ORTALAMA,DEPO_ADET=:DEPO_ADET,CREATED_BY=:CREATED_BY,CREATED=:CREATED where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA_VERI  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA_VERI  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
