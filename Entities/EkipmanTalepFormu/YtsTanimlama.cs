﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entities.EkipmanTalepFormu
{
    public class YtsTanimlama : Entity_Base.EntityBase
    {

        public long ID { get; set; } // OTOMATİK ARTAN ID
        public long YtsID { get; set; } // YTS'IN KENDI ID'SI
        public string YtsKodu { get; set; }
        public string YtsAdı { get; set; }
        public string Bolge { get; set; }
        public string Il { get; set; }
        public string DepoKodu { get; set; }
        public string EkrandakiDurumu { get; set; }


        public override string DELETE_SQL()
        {
            throw new NotImplementedException();
        }

        public override string INSERT_SQL()
        {
            throw new NotImplementedException();
        }

        public override string SELECT_SQL()
        {
            throw new NotImplementedException();
        }

        public override string UPDATE_SQL()
        {
            throw new NotImplementedException();
        }
    }
}
