﻿///
///Generated by KB Entity Generator 
///17.10.2022 15:13:59
///
using System;
namespace Entities.EkipmanTalepFormu
{
    public class DF_EKIPMAN_EXCEL_DOSYA : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public string DOSYA_ADI_LINK { get; set; }
        public string CREATED_BY { get; set; }
        public System.Nullable<DateTime> CREATED { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA (DOSYA_ADI_LINK,CREATED_BY,CREATED) values (:DOSYA_ADI_LINK,:CREATED_BY,:CREATED)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA set  DOSYA_ADI_LINK=:DOSYA_ADI_LINK,CREATED_BY=:CREATED_BY,CREATED=:CREATED where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DF_EKIPMAN_EXCEL_DOSYA  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
