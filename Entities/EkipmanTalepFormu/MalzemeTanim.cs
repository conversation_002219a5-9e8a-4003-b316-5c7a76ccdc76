﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entities.EkipmanTalepFormu
{
    public class MalzemeTanim : Entity_Base.EntityBase
    {
        public long ID { get; set; }
        public long MalzemeID { get; set; }
        public override string DELETE_SQL()
        {
            throw new NotImplementedException();
        }

        public override string INSERT_SQL()
        {
            throw new NotImplementedException();
        }

        public override string SELECT_SQL()
        {
            throw new NotImplementedException();
        }

        public override string UPDATE_SQL()
        {
            throw new NotImplementedException();
        }
    }
}
