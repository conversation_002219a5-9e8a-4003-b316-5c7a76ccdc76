﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{2332BA40-BC4C-41DD-83CC-7B2060C089FE}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.Entities</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.Entities</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="AdminPanel\RaporTalepAdmin.cs" />
    <Compile Include="Common\InsteadOfUser.cs" />
    <Compile Include="Common\WorkflowFileUpload.cs" />
    <Compile Include="Enums\WorkflowHistoryActionType.cs" />
    <Compile Include="History\WorkflowHistoryLog.cs" />
    <Compile Include="Settings\Languages.cs" />
    <Compile Include="Settings\UserLangSettings.cs" />
    <Compile Include="WorkFlowEntities\ActivityRequest\ActivityDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\ActivityRequest\ActivityDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\ActivityRequest\ActivityRequest.cs" />
    <Compile Include="WorkFlowEntities\ActivityRequest\ActivityAnketDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\AdHocRequest\AdHocRequest.cs" />
    <Compile Include="WorkFlowEntities\AlternativeManagerRequest\AlternativeManagerRequest.cs" />
    <Compile Include="WorkFlowEntities\AracTakipRequest\AracTakipRequest.cs" />
    <Compile Include="WorkFlowEntities\AydinlatmaMetniRizaRequest\AydinlatmaMetniRizaDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\AydinlatmaMetniRizaRequest\AydinlatmaMetniRizaRequest.cs" />
    <Compile Include="WorkFlowEntities\BayiBelgeOnayRequest\BayiBelgeOnayRequest.cs" />
    <Compile Include="WorkFlowEntities\BayiBelgeOnayRequest\BayiBelgeOnayDetayRequest.cs" />
    <Compile Include="WorkFlowEntities\BayiFotografRequest\BayiFotografRequest.cs" />
    <Compile Include="WorkFlowEntities\BayiKesinHesap1\BayiKesinHesap.cs" />
    <Compile Include="WorkFlowEntities\BayiKesinHesap1\BayiKesinHesapDetay.cs" />
    <Compile Include="WorkFlowEntities\BayiKesinHesap1\BayiKesinHesapDetay2.cs" />
    <Compile Include="WorkFlowEntities\BiFikrimVar\BiFikrimVarDocDetail.cs" />
    <Compile Include="WorkFlowEntities\ConflictsofInterest\ConflictsofInterestRequest.cs" />
    <Compile Include="WorkFlowEntities\ConflictsofInterest\ConflictsofInterestDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\EkipmanTalepFormuEntity\EkipmanTalepFormu.cs" />
    <Compile Include="WorkFlowEntities\EkipmanTalepFormuEntity\EkipmanTalepFormuDetay.cs" />
    <Compile Include="WorkFlowEntities\EkipmanTalep\EkipmanTalepRequest_Detay.cs" />
    <Compile Include="WorkFlowEntities\EmployeeAppreciation\EmployeeAppreciation.cs" />
    <Compile Include="WorkFlowEntities\EmployeeAppreciation\EmployeeAppreciationDocDetail.cs" />
    <Compile Include="WorkFlowEntities\EmployeeThank\EmployeeThank.cs" />
    <Compile Include="WorkFlowEntities\EmployeRequest\EmployeeIcIlanDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\EmployeRequest\EmployeeDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\IdeaEvaluation\IdeaEvaluation.cs" />
    <Compile Include="WorkFlowEntities\IdeaEvaluation\IdeaEvaluationComment.cs" />
    <Compile Include="WorkFlowEntities\IdeaEvaluation\IdeaEvaluationDocDetail.cs" />
    <Compile Include="WorkFlowEntities\SupplierAdvancePayment\SupplierAdvancePaymentDocDetail.cs" />
    <Compile Include="WorkFlowEntities\SupplierOperationEndReminder\SupplierOperationEndReminderMail.cs" />
    <Compile Include="WorkFlowEntities\SupplierOperationEndReminder\SupplierOperationEndReminderMailDetail.cs" />
    <Compile Include="WorkFlowEntities\Installation\InstallationDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormNew.cs" />
    <Compile Include="WorkFlowEntities\JobQuitForm\JobQuitFormDetail.cs" />
    <Compile Include="WorkFlowEntities\JobQuitForm\JobQuitFormRequest.cs" />
    <Compile Include="WorkFlowEntities\MalzemeCikisFormu\MalzemeCikisEntity.cs" />
    <Compile Include="WorkFlowEntities\MalzemeCikisFormu\MalzemeCikisEntityDetay.cs" />
    <Compile Include="WorkFlowEntities\PowerBI\PowerBIRequest.cs" />
    <Compile Include="WorkFlowEntities\EvdenUzaktanCalisma\EvdenUzaktanCalismaDetail.cs" />
    <Compile Include="WorkFlowEntities\EvdenUzaktanCalisma\EvdenUzaktanCalisma.cs" />
    <Compile Include="WorkFlowEntities\HurdaIhaleRequest\HurdaIhaleRequest.cs" />
    <Compile Include="WorkFlowEntities\OTTUcretsizYayinEndReminderMail\OTTUcretsizYayinEndReminderMail.cs" />
    <Compile Include="WorkFlowEntities\OTTUcretsizYayinEndReminderMail\OTTUcretsizYayinEndReminderMailDetail.cs" />
    <Compile Include="WorkFlowEntities\InstallationEndReminder\InstallationEndReminderMailDetail.cs" />
    <Compile Include="WorkFlowEntities\InstallationEndReminder\InstallationEndReminderMail.cs" />
    <Compile Include="WorkFlowEntities\OTTUcretsizYayin\OTTUcretsizYayinFormuDetay.cs" />
    <Compile Include="WorkFlowEntities\OTTUcretsizYayin\OTTUcretsizYayinFormu.cs" />
    <Compile Include="WorkFlowEntities\PaymentRequest\PaymentRequestSozlesmeDocDetail.cs" />
    <Compile Include="WorkFlowEntities\ResmiCalismaFormu\ResmiCalismaDetay.cs" />
    <Compile Include="WorkFlowEntities\ResmiCalismaFormu\ResmiCalisma.cs" />
    <Compile Include="WorkFlowEntities\ReturnRequest\ReturnDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\RotaMacroDelRequest\RotaMacroDelDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\RotaMacroDelRequest\RotaMacroDelRequest.cs" />
    <Compile Include="WorkFlowEntities\BiFikrimVar\BiFikrimVar.cs" />
    <Compile Include="WorkFlowEntities\SatinAlmaSurecleriOnayRequest\SatinAlmaSurecleriOnayRequest.cs" />
    <Compile Include="WorkFlowEntities\SatinAlmaSurecleriOnayRequest\SatinAlmaSurecleriOnayRequest_Detay.cs" />
    <Compile Include="WorkFlowEntities\SatisCiroHesaplamaRequest\SatisCiroHesaplamaRequest.cs" />
    <Compile Include="WorkFlowEntities\SatisKurulumAnaIslemTipiTanim\SatisKurulumAnaIslemTipiTanim.cs" />
    <Compile Include="WorkFlowEntities\BayiCezaTalep\BayiCezaDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\BayiCezaTalep\BayiCezaRequest.cs" />
    <Compile Include="WorkFlowEntities\BayiCezaTalep\BayiCezaDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\BillRequest\BillRequest.cs" />
    <Compile Include="WorkFlowEntities\BillRequest\BillRequestDocDetail.cs" />
    <Compile Include="WorkFlowEntities\BriefRequest\BriefDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\BriefRequest\BriefDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\BriefRequest\BriefRequest.cs" />
    <Compile Include="WorkFlowEntities\CampaignRequest\CampaignDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\CampaignRequest\CampaignCommitment.cs" />
    <Compile Include="WorkFlowEntities\CampaignRequest\CampaignMemo.cs" />
    <Compile Include="WorkFlowEntities\CampaignRequest\CampaignRequest.cs" />
    <Compile Include="WorkFlowEntities\CampaignRequest\CampaignOfferlist.cs" />
    <Compile Include="WorkFlowEntities\CampaignRequest\CampaignFile.cs" />
    <Compile Include="WorkFlowEntities\ChannelRequest\ChannelRequest.cs" />
    <Compile Include="WorkFlowEntities\ChannelRequest\ChannelRequestDetail.cs" />
    <Compile Include="WorkFlowEntities\ConsultantJobEntranceFormRequest\ConsultantJobEntDetailFormRequest.cs" />
    <Compile Include="WorkFlowEntities\ConsultantJobEntranceFormRequest\ConsultantJobEntranceFormRequest.cs" />
    <Compile Include="WorkFlowEntities\SatisKurulumAltIslemTipiTanim\SatisKurulumAltIslemTipiTanim.cs" />
    <Compile Include="WorkFlowEntities\HavaMuhalefetiForm\HavaMuhalefetiFormDetail.cs" />
    <Compile Include="WorkFlowEntities\HavaMuhalefetiForm\HavaMuhalefetiForm.cs" />
    <Compile Include="WorkFlowEntities\PvrLnbHakedisRequest\PvrLnbHakedisRequest.cs" />
    <Compile Include="WorkFlowEntities\Qlikview\QlikviewTalep.cs" />
    <Compile Include="WorkFlowEntities\Qlikview\QlikviewTalepDetail.cs" />
    <Compile Include="WorkFlowEntities\CorporateCommunicationRequest\CorporateCommunicationDetail.cs" />
    <Compile Include="WorkFlowEntities\CorporateCommunicationRequest\CorporateCommunication.cs" />
    <Compile Include="WorkFlowEntities\CorporateCommunicationRequest\CorporateCommunicationDetailDoc.cs" />
    <Compile Include="WorkFlowEntities\DBSRequest\DBSDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\EkipmanTalep\EkipmanTalepRequest.cs" />
    <Compile Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesDetailAcil.cs" />
    <Compile Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesDetailChild.cs" />
    <Compile Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesDetailTahsil.cs" />
    <Compile Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesInfoRequest.cs" />
    <Compile Include="WorkFlowEntities\ExpenseRequest\ExpenseParkDetail.cs" />
    <Compile Include="WorkFlowEntities\Eyk\EykTalep.cs" />
    <Compile Include="WorkFlowEntities\Eyk\EykTalepDetail.cs" />
    <Compile Include="WorkFlowEntities\GuestServiceRequest\GuestServiceRequest.cs" />
    <Compile Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormOkulInfo.cs" />
    <Compile Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormCocuk.cs" />
    <Compile Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormAcilDurum.cs" />
    <Compile Include="WorkFlowEntities\MacroRequest\MacroDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\MacroRequest\MacroDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\MacroRequest\MacroRaporDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\OldPurchaseUseRequest\OldPurchaseUseRequest.cs" />
    <Compile Include="WorkFlowEntities\OldPurchaseUseRequest\OldPurchaseUseRequestDetail.cs" />
    <Compile Include="WorkFlowEntities\OperationalWorkRequest\OperationalWorkRequest.cs" />
    <Compile Include="WorkFlowEntities\PaymentRequest\PaymentRequestDocDetail.cs" />
    <Compile Include="WorkFlowEntities\PrfHedefDeger\PrfHedefDegerRequest.cs" />
    <Compile Include="WorkFlowEntities\PrfHedefRevizyon\PrfHedefRevizyonRequest.cs" />
    <Compile Include="WorkFlowEntities\PrfHedef\PrfHedefRequest.cs" />
    <Compile Include="WorkFlowEntities\ProcedureRequest\ProcedureRequest.cs" />
    <Compile Include="WorkFlowEntities\ProcedureRequest\ProcedureRequestDetail.cs" />
    <Compile Include="WorkFlowEntities\Purchase\PurchaseButceDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\Purchase\PurchaseArgeDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\Purchase\PurchaseDetailFileRequest.cs" />
    <Compile Include="WorkFlowEntities\Purchase\PurchaseOrderDetaileFileRequest.cs" />
    <Compile Include="WorkFlowEntities\Purchase\PurchaseOrderDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\ReturnRequest\ReturnRequest.cs" />
    <Compile Include="WorkFlowEntities\ReturnRequest\ReturnDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\RotaRequest\RaporGizliIcerik.cs" />
    <Compile Include="WorkFlowEntities\RotaRequest\RotaDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\RotaRequest\RotaDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\RotaRequest\RotaRaporDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\SatisIptalChurnRequest\SatisIptalChurnRequest.cs" />
    <Compile Include="WorkFlowEntities\SatisKurulumDegerlendirme\SatisKurulumDegerlendirme.cs" />
    <Compile Include="WorkFlowEntities\SavcilikRequest\SavcilikRequest.cs" />
    <Compile Include="WorkFlowEntities\SmsTalep\SmsTalep.cs" />
    <Compile Include="WorkFlowEntities\MakroUygTalep\MakroUygTalep.cs" />
    <Compile Include="WorkFlowEntities\MakroUygTalep\MakroUygTalepDetail.cs" />
    <Compile Include="WorkFlowEntities\KurumsalTalep\KurumsalTalep.cs" />
    <Compile Include="WorkFlowEntities\KurumsalTalep\KurumsalTalepDetail.cs" />
    <Compile Include="WorkFlowEntities\RaporTalep\RaporTalep.cs" />
    <Compile Include="WorkFlowEntities\RaporTalep\RaporTalepDetail.cs" />
    <Compile Include="WorkFlowEntities\ServicePersonnelJobEntranceFormRequest\ServPersJobEntDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\ServicePersonnelJobEntranceFormRequest\ServPersJobEntranceRequest.cs" />
    <Compile Include="WorkFlowEntities\SodexhoRequest\SodexhoDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\SodexhoRequest\SodexhoRequest.cs" />
    <Compile Include="WorkFlowEntities\StationeryRequest\StationeryAddressDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\SupplierAdvancePayment\SupplierAdvancePaymentRequest.cs" />
    <Compile Include="WorkFlowEntities\SatisKurulumDegerlendirme\SatisKurulumDegerlendirmeDoc.cs" />
    <Compile Include="WorkFlowEntities\SupplierOperation\SupplierOperationDocDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\SupplierOperation\SupplierOperationApproveDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\SupplierOperation\SupplierOperationDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\TeminatTeslimTalep\TeminatTeslimTalep.cs" />
    <Compile Include="WorkFlowEntities\TeminatTeslimTalep\TeminatTeslimTalepDetail.cs" />
    <Compile Include="WorkFlowEntities\TicariFiyatIstisnaRequest\TicariFiyatIstisnaDetail.cs" />
    <Compile Include="WorkFlowEntities\TicariFiyatIstisnaRequest\TicariFiyatIstisnaRequest.cs" />
    <Compile Include="WorkFlowEntities\BoScheduleRequest\BoScheduleRequest.cs" />
    <Compile Include="WorkFlowEntities\TicariSatisliYayinAcma\TicariSatisliYayinAcmaRequest.cs" />
    <Compile Include="WorkFlowEntities\TicariUyeIade\TicariUyeIadeRequest.cs" />
    <Compile Include="WorkFlowEntities\TicariUyeYetkilendirme\TicariUyeYetkilendirmeRequest.cs" />
    <Compile Include="WorkFlowEntities\TicariGrupDegisiklik\TicariGrupDegisiklikRequest.cs" />
    <Compile Include="WorkFlowEntities\TSHakedisBirimFiyatOnaySüreci\TSHakedisBirimFiyatDokuman.cs" />
    <Compile Include="WorkFlowEntities\TSHakedisBirimFiyatOnaySüreci\TSHakedisBirimFiyatOnaySüreci.cs" />
    <Compile Include="WorkFlowEntities\TSHakedisBirimFiyatOnaySüreci\TSHakedisBirimFiyatOnaySüreciDetail.cs" />
    <Compile Include="WorkFlowEntities\TsMhzDuyuruRequest\TsMhzDuyuruRequest.cs" />
    <Compile Include="WorkFlowEntities\UcrestsizUyelikTalep\UcretsizUyelikTalep.cs" />
    <Compile Include="WorkFlowEntities\Vacation\LeaveVardiyaDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\YurtDisiUcretsizPaketFormu\YurtDisiUcretsizPaketFormu.cs" />
    <Compile Include="YYS\ActionAuthorization\ActionAuthorizationType.cs" />
    <Compile Include="YYS\Enumerations.cs" />
    <Compile Include="YYS\HrUser.cs" />
    <Compile Include="YYS\ActionAuthorization\ActionAuthorization.cs" />
    <Compile Include="YYS\LogicalGroup\LogicalGroup.cs" />
    <Compile Include="YYS\LogicalGroup\LogicalGroupAdMap.cs" />
    <Compile Include="YYS\LogicalGroup\LogicalGroupMember.cs" />
    <Compile Include="YYS\LogicalGroup\LogicalGroupMemberType.cs" />
    <Compile Include="YYS\StateAuthorization\StateAuthorization.cs" />
    <Compile Include="YYS\Workflow.cs" />
    <Compile Include="YYS\WorkflowAdminWorkflow\WorkflowAdminWorkflow.cs" />
    <Compile Include="YYS\WFAdmin\WorkFlowAdmin.cs" />
    <Compile Include="YYS\WorkflowState.cs" />
    <Compile Include="WorkFlowEntities\BillReturnApproveRequest\BillReturnApproveRequestRequest.cs" />
    <Compile Include="WorkFlowEntities\CarRequestFormRequest\CarRequestFormRequest.cs" />
    <Compile Include="WorkFlowEntities\CIPRequest\CIPRequest.cs" />
    <Compile Include="WorkFlowEntities\CorporateApps\CorporateApps.cs" />
    <Compile Include="WorkFlowEntities\DBSRequest\DBSRequest.cs" />
    <Compile Include="WorkFlowEntities\ExpenseRequest\ExpenseDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\ExpenseRequest\ExpenseFormRequest.cs" />
    <Compile Include="WorkFlowEntities\ExpenseRequest\ExpenseReprDetail.cs" />
    <Compile Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormKartvizit.cs" />
    <Compile Include="WorkFlowEntities\JobEntranceForm\JobEntranceForm.cs" />
    <Compile Include="WorkFlowEntities\JobLeavingFormRequest\JobLeavingFormRequest.cs" />
    <Compile Include="WorkFlowEntities\NetsisReporttRequest\NetsisReporttDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\NetsisReporttRequest\NetsisReporttRequest.cs" />
    <Compile Include="WorkFlowEntities\NetsisRequest\NetsisDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\NetsisRequest\NetsisRequest.cs" />
    <Compile Include="WorkFlowEntities\OldPurchaseRequest\OldPurchaseRequest.cs" />
    <Compile Include="WorkFlowEntities\ProjeTalep\ProjeTalep.cs" />
    <Compile Include="WorkFlowEntities\ProjeTalep\ProjeTalepDetail.cs" />
    <Compile Include="WorkFlowEntities\PurchaseForPayment\PurchaseForPaymentRequest.cs" />
    <Compile Include="WorkFlowEntities\StationeryRequest\StationeryDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\StationeryRequest\StationeryRequest.cs" />
    <Compile Include="WorkFlowEntities\Installation\InstallationDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\JumpToStateRequest\JumpToStateRequest.cs" />
    <Compile Include="WorkFlowEntities\MonitoringRequest\MonitoringRequest.cs" />
    <Compile Include="WorkFlowEntities\PaymentRequest\PaymentDetails.cs" />
    <Compile Include="WorkFlowEntities\PaymentRequest\PaymentDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\PaymentRequest\PaymentRequest.cs" />
    <Compile Include="WorkFlowEntities\Advanced\AdvanceFinalRequest.cs" />
    <Compile Include="WorkFlowEntities\BusinessObject\BusinessObjectRequestRequest.cs" />
    <Compile Include="WorkFlowEntities\Contract\ContractsRequest.cs" />
    <Compile Include="WorkFlowEntities\Education\EducationDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\EntitesBase\IDetailEntity.cs" />
    <Compile Include="WorkFlowEntities\EntitesBase\IEntity.cs" />
    <Compile Include="WorkFlowEntities\Advanced\AdvanceRequest.cs" />
    <Compile Include="WorkFlowEntities\GSMRequest\GSMRequest.cs" />
    <Compile Include="WorkFlowEntities\Education\EducationRequest.cs" />
    <Compile Include="WorkFlowEntities\Delegation\DelegationRequest.cs" />
    <Compile Include="WorkFlowEntities\EmployeRequest\EmployeeRequest.cs" />
    <Compile Include="WorkFlowEntities\Installation\InstallationRequest.cs" />
    <Compile Include="WorkFlowEntities\MacroRequest\MacroRequest.cs" />
    <Compile Include="WorkFlowEntities\NotAllowIntervention\NotAllowInterventionRequest.cs" />
    <Compile Include="WorkFlowEntities\OfContract\OfContactRequest.cs" />
    <Compile Include="WorkFlowEntities\Purchase\PurchaseDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\Purchase\PurchaseRequest.cs" />
    <Compile Include="WorkFlowEntities\RotaRequest\RotaRequest.cs" />
    <Compile Include="WorkFlowEntities\SampleRequest\SampleDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\SampleRequest\SampleRequest.cs" />
    <Compile Include="WorkFlowEntities\SaperionRequest\SaperionRequest.cs" />
    <Compile Include="WorkFlowEntities\SupplierCheckBillRequest\SupplierCheckBillRequestRequest.cs" />
    <Compile Include="WorkFlowEntities\SupplierOperation\SupplierOperationRequest.cs" />
    <Compile Include="WorkFlowEntities\Tally\TallyDayDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\Tally\TallyPhoneDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\Test.cs" />
    <Compile Include="WorkFlowEntities\Transmisyon\TransmisyonDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\Transmisyon\TransmisyonRequest.cs" />
    <Compile Include="WorkFlowEntities\TravelRequest\TravelDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\TravelRequest\TravelRequest.cs" />
    <Compile Include="WorkFlowEntities\Vacation\LeaveDetailRequest.cs" />
    <Compile Include="WorkFlowEntities\Vacation\LeaveRequest.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="WorkFlowEntities\Tally\TallyRequest.cs" />
    <Compile Include="Common\WorkflowPermission.cs" />
    <Compile Include="Common\WorkflowPermissionType.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\DBSRequest\DBSDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\EmployeRequest\EmployeeRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Common\WorkflowPermissionType.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Common\WorkflowPermission.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Delegation\DelegationRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Education\EducationRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\GSMRequest\GSMRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Advanced\AdvanceRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Tally\TallyDayDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Tally\TallyPhoneDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Vacation\LeaveRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Tally\TallyRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Advanced\AdvanceFinalRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Education\EducationDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Vacation\LeaveDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Test\" />
    <Folder Include="WorkFlowEntities\JobRequestStart\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BusinessObject\BusinessObjectRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="YYS\LogicalGroup\LogicalGroupMemberType.hbm.xml" />
    <EmbeddedResource Include="YYS\ActionAuthorization\ActionAuthorization.hbm.xml" />
    <EmbeddedResource Include="YYS\LogicalGroup\LogicalGroupMember.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="YYS\LogicalGroup\LogicalGroup.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierOperation\SupplierOperationRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\MacroRequest\MacroRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\NotAllowIntervention\NotAllowInterventionRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\OfContract\OfContactRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\RotaRequest\RotaRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="YYS\WFAdmin\WorkFlowAdmin.hbm.xml" />
    <EmbeddedResource Include="YYS\WorkflowAdminWorkflow\WorkflowAdminWorkflow.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Installation\InstallationDetailRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="WorkFlowEntities\JumpToStateRequest\JumpToStateRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\MonitoringRequest\MonitoringRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="WorkFlowEntities\PaymentRequest\PaymentDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\PaymentRequest\PaymentRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Purchase\PurchaseDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Purchase\PurchaseRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Contract\ContractsRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Installation\InstallationRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PaymentRequest\PaymentDetails.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SaperionRequest\SaperionRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SampleRequest\SampleDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\SampleRequest\SampleRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\StationeryRequest\StationeryRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\StationeryRequest\StationeryDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\CorporateApps\CorporateApps.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\TravelRequest\TravelDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\TravelRequest\TravelRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\NetsisRequest\NetsisDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\NetsisRequest\NetsisRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BillReturnApproveRequest\BillReturnApproveRequestRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\CarRequestFormRequest\CarRequestFormRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\JobEntranceForm\JobEntranceForm.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="WorkFlowEntities\JobLeavingFormRequest\JobLeavingFormRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="WorkFlowEntities\SupplierCheckBillRequest\SupplierCheckBillRequestRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ExpenseRequest\ExpenseFormRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ExpenseRequest\ExpenseDetailRequest..hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ExpenseRequest\ExpenseReprDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OldPurchaseRequest\OldPurchaseRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\PurchaseForPayment\PurchaseForPaymentRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ProjeTalep\ProjeTalep.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ProjeTalep\ProjeTalepDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\DBSRequest\DBSRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\CIPRequest\CIPRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormKartvizit.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\NetsisReporttRequest\NetsisReporttDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\NetsisReporttRequest\NetsisReporttRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Transmisyon\TransmisyonDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\Transmisyon\TransmisyonRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="AdminPanel\RaporTalepAdmin.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\RaporTalep\RaporTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\RaporTalep\RaporTalepDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\KurumsalTalep\KurumsalTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\KurumsalTalep\KurumsalTalepDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\MakroUygTalep\MakroUygTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\MakroUygTalep\MakroUygTalepDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ServicePersonnelJobEntranceFormRequest\ServPersJobEntDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ServicePersonnelJobEntranceFormRequest\ServPersJobEntranceRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ConsultantJobEntranceFormRequest\ConsultantJobEntDetailFormRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ConsultantJobEntranceFormRequest\ConsultantJobEntranceFormRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SmsTalep\SmsTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\CampaignRequest\CampaignDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\CampaignRequest\CampaignMemo.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\CampaignRequest\CampaignRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\CampaignRequest\CampaignCommitment.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierOperation\SupplierOperationDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\CampaignRequest\CampaignOfferlist.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\CampaignRequest\CampaignFile.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PrfHedef\PrfHedefRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OperationalWorkRequest\OperationalWorkRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="History\WorkflowHistoryLog.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PrfHedefRevizyon\PrfHedefRevizyonRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PrfHedefDeger\PrfHedefDegerRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ChannelRequest\ChannelRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ChannelRequest\ChannelRequestDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Eyk\EykTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Eyk\EykTalepDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormAcilDurum.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormCocuk.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesInfoRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesDetailChild.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesDetailAcil.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ProcedureRequest\ProcedureRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ProcedureRequest\ProcedureRequestDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierAdvancePayment\SupplierAdvancePaymentRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeesInfoRequest\EmployeesDetailTahsil.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormOkulInfo.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Vacation\LeaveVardiyaDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BillRequest\BillRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\CorporateCommunicationRequest\CorporateCommunication.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\CorporateCommunicationRequest\CorporateCommunicationDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BriefRequest\BriefDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\BriefRequest\BriefDocDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\BriefRequest\BriefRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ActivityRequest\ActivityDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ActivityRequest\ActivityDocDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ActivityRequest\ActivityRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\CorporateCommunicationRequest\CorporateCommunicationDetailDoc.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ActivityRequest\ActivityAnketDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierOperation\SupplierOperationApproveDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\GuestServiceRequest\GuestServiceRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ReturnRequest\ReturnRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\TeminatTeslimTalep\TeminatTeslimTalepDetail.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\TeminatTeslimTalep\TeminatTeslimTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ReturnRequest\ReturnDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ExpenseRequest\ExpenseParkDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BillRequest\BillRequestDocDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OldPurchaseUseRequest\OldPurchaseUseRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\StationeryRequest\StationeryAddressDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OldPurchaseUseRequest\OldPurchaseUseRequestDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Purchase\PurchaseArgeDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Purchase\PurchaseButceDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BayiCezaTalep\BayiCezaDocDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BayiCezaTalep\BayiCezaRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BayiCezaTalep\BayiCezaDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\RotaRequest\RotaDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\RotaRequest\RotaDocDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\RotaRequest\RaporGizliIcerik.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\AdHocRequest\AdHocRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\MacroRequest\MacroRaporDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\MacroRequest\MacroDocDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\MacroRequest\MacroDetailRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\RotaRequest\RotaRaporDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatisIptalChurnRequest\SatisIptalChurnRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SavcilikRequest\SavcilikRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PaymentRequest\PaymentRequestDocDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Purchase\PurchaseOrderDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Purchase\PurchaseDetailFileRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\EkipmanTalepFormuEntity\EkipmanTalepFormu.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\EkipmanTalepFormuEntity\EkipmanTalepFormuDetay.hbm.xml" />
    <None Include="WorkFlowEntities\Purchase\PurchaseOrderDetailFileRequest.hbm" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EkipmanTalep\EkipmanTalepRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Qlikview\QlikviewTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Qlikview\QlikviewTalepDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\UcrestsizUyelikTalep\UcretsizUyelikTalep.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\HavaMuhalefetiForm\HavaMuhalefetiFormDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\HavaMuhalefetiForm\HavaMuhalefetiForm.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PvrLnbHakedisRequest\PvrLnbHakedisRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\TicariGrupDegisiklik\TicariGrupDegisiklikRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\TicariFiyatIstisnaRequest\TicariFiyatIstisnaRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\TicariFiyatIstisnaRequest\TicariFiyatIstisnaDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatisKurulumDegerlendirme\SatisKurulumDegerlendirme.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatisKurulumDegerlendirme\SatisKurulumDegerlendirmeDoc.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatisKurulumAnaIslemTipiTanim\SatisKurulumAnaIslemTipiTanim.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatisKurulumAltIslemTipiTanim\SatisKurulumAltIslemTipiTanim.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\TsMhzDuyuruRequest\TsMhzDuyuruRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\AydinlatmaMetniRizaRequest\AydinlatmaMetniRizaDetailRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\AydinlatmaMetniRizaRequest\AydinlatmaMetniRizaRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\YurtDisiUcretsizPaketFormu\YurtDisiUcretsizPaketFormu.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\HurdaIhaleRequest\HurdaIhaleRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EvdenUzaktanCalisma\EvdenUzaktanCalismaDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EvdenUzaktanCalisma\EvdenUzaktanCalisma.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\TicariUyeYetkilendirme\TicariUyeYetkilendirmeRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\RotaMacroDelRequest\RotaMacroDelRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\RotaMacroDelRequest\RotaMacroDelDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SodexhoRequest\SodexhoDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SodexhoRequest\SodexhoRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\InstallationEndReminder\InstallationEndReminderMail.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\InstallationEndReminder\InstallationEndReminderMailDetail.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\TicariUyeIade\TicariUyeIadeRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BoScheduleRequest\BoScheduleRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OTTUcretsizYayin\OTTUcretsizYayinFormu.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OTTUcretsizYayin\OTTUcretsizYayinFormuDetay.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OTTUcretsizYayinEndReminderMail\OTTUcretsizYayinEndReminderMail.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\OTTUcretsizYayinEndReminderMail\OTTUcretsizYayinEndReminderMailDetail.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ReturnRequest\ReturnDocDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierOperation\SupplierOperationDocDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="System" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PaymentRequest\PaymentRequestSozlesmeDocDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\TicariSatisliYayinAcma\TicariSatisliYayinAcmaRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\PowerBI\PowerBIRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeRequest\EmployeeDocDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\AracTakipRequest\AracTakipRequest.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Common\WorkflowFileUpload.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Common\InsteadOfUser.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BayiKesinHesap1\BayiKesinHesap.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\BayiKesinHesap1\BayiKesinHesapDetay.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\BayiKesinHesap1\BayiKesinHesapDetay2.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\JobEntranceForm\JobEntranceFormNew.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EkipmanTalep\EkipmanTalepRequest_Detay.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\TSHakedisBirimFiyatOnaySüreci\TSHakedisBirimFiyatOnaySüreci.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\TSHakedisBirimFiyatOnaySüreci\TSHakedisBirimFiyatDokuman.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\TSHakedisBirimFiyatOnaySüreci\TSHakedisBirimFiyatOnaySüreciDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Settings\UserLangSettings.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ConflictsofInterest\ConflictsofInterestRequest.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\ConflictsofInterest\ConflictsofInterestDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatisCiroHesaplamaRequest\SatisCiroHesaplamaRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ResmiCalismaFormu\ResmiCalismaDetay.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\ResmiCalismaFormu\ResmiCalisma.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeRequest\EmployeeIcIlanDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\MalzemeCikisFormu\MalzemeCikisEntityDetay.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\MalzemeCikisFormu\MalzemeCikisEntity.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\Installation\InstallationDocDetailRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BiFikrimVar\BiFikrimVar.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BiFikrimVar\BiFikrimVarDocDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\JobQuitForm\JobQuitFormDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\JobQuitForm\JobQuitFormRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BayiFotografRequest\BayiFotografRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\AlternativeManagerRequest\AlternativeManagerRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatinAlmaSurecleriOnayRequest\SatinAlmaSurecleriOnayRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\IdeaEvaluation\IdeaEvaluation.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\IdeaEvaluation\IdeaEvaluationComment.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\IdeaEvaluation\IdeaEvaluationDocDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeeAppreciation\EmployeeAppreciation.hbm.xml" />
    <EmbeddedResource Include="WorkFlowEntities\EmployeeAppreciation\EmployeeAppreciationDocDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SatinAlmaSurecleriOnayRequest\SatinAlmaSurecleriOnayRequest_Detay.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\EmployeeThank\EmployeeThank.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierOperationEndReminder\SupplierOperationEndReminderMail.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierOperationEndReminder\SupplierOperationEndReminderMailDetail.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\SupplierAdvancePayment\SupplierAdvancePaymentDocDetail.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BayiBelgeOnayRequest\BayiBelgeOnayRequest.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="WorkFlowEntities\BayiBelgeOnayRequest\BayiBelgeOnayDetayRequest.hbm.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <PostBuildEvent>COPY /Y "$(TargetPath)" \\dtl1iis3\Deployment</PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked" if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"
attrib -r c:\TFS\DigiflowPM\Digiturk.Workflow.Digiflow.Entities\*.* /s</PreBuildEvent>
  </PropertyGroup>
</Project>