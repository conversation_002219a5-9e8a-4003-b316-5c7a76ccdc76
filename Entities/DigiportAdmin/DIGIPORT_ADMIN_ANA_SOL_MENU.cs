﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DigiportAdmin
{
    public class DIGIPORT_ADMIN_ANASAYFA_SOL_MENU : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal MENU_NAME_ID { get; set; }
        public string CONTENT { get; set; }
        public string TARGET_HEADLINE { get; set; }
        public string TARGET_LINK { get; set; }
        public decimal CLICK_ACTION { get; set; }
        public System.Nullable<decimal> POPUP_WIDTH { get; set; }
        public System.Nullable<decimal> POPUP_HEIGHT { get; set; }
        public decimal ORDER_NO { get; set; }
        public string ACTIVE { get; set; }
        public string DELETED { get; set; }
        public string TARGET_CONTENT { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOL_MENU (MENU_NAME_ID,CONTENT,TARGET_LINK,CLICK_ACTION,POPUP_WIDTH,POPUP_HEIGHT,ORDER_NO,ACTIVE,DELETED,TARGET_CONTENT,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY,TARGET_HEADLINE) values (:MENU_NAME_ID,:CONTENT,:TARGET_LINK,:CLICK_ACTION,:POPUP_WIDTH,:POPUP_HEIGHT,:ORDER_NO,:ACTIVE,:DELETED,:TARGET_CONTENT,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY,:TARGET_HEADLINE)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOL_MENU set  MENU_NAME_ID=:MENU_NAME_ID,CONTENT=:CONTENT,TARGET_LINK=:TARGET_LINK,CLICK_ACTION=:CLICK_ACTION,POPUP_WIDTH=:POPUP_WIDTH,POPUP_HEIGHT=:POPUP_HEIGHT,ORDER_NO=:ORDER_NO,ACTIVE=:ACTIVE,DELETED=:DELETED,TARGET_CONTENT=:TARGET_CONTENT,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY,TARGET_HEADLINE=:TARGET_HEADLINE where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOL_MENU  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOL_MENU  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}

