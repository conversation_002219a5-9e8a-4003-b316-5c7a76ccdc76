﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DigiportAdmin
{
    public class DIGIPORT_ADMIN_INDIRIM_KAT : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public string KATEGORI_ADI { get; set; }
        public string KATEGORI_ADI_EN { get; set; }
        public string AKTIF { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_KAT (KATEGORI_ADI,KATEGORI_ADI_EN,AKTIF,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY) values (:KATEGORI_ADI,:KATEGORI_ADI_EN,:AKTIF,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_KAT set  KATEGORI_ADI=:KATEGORI_ADI,KATEGORI_ADI_EN=:KATEGORI_ADI_EN,AKTIF=:AKTIF,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_KAT  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_KAT  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
