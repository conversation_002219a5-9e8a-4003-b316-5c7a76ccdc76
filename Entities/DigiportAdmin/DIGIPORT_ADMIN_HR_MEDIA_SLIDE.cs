﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DigiportAdmin
{
    public class DIGIPORT_ADMIN_HR_MEDIA_SLIDE : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal MENU_NAME_ID { get; set; }
        public string SLIDE_NAME { get; set; }
        public string SLIDE_IMAGE_PATH { get; set; }
        public decimal SLIDE_CLICK_ACTION { get; set; }
        public string SLIDE_TARGET_LINK { get; set; }
        public string SLIDE_TARGET_CONTENT { get; set; }
        public System.Nullable<decimal> SLIDE_POPUP_WIDTH { get; set; }
        public System.Nullable<decimal> SLIDE_POPUP_HEIGHT { get; set; }
        public string ACTIVE { get; set; }
        public decimal ORDER_NO { get; set; }
        public DateTime VALID_DATE_START { get; set; }
        public DateTime VALID_DATE_END { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        public string SLIDE_TARGET_HEADLINE { get; set; }
        public string DELETED { get; set; }
        public string THUMBNAIL_IMAGE_PATH { get; set; }
        public System.Nullable<decimal> CATEGORY { get; set; }
        public string HRAPP_SLIDE_IMAGE_PATH { get; set; }
        public System.Nullable<decimal> HRAPP_SLIDE_CLICK_ACTION { get; set; }
        public string HRAPP_SLIDE_TARGET_LINK { get; set; }
        public string HRAPP_SLIDE_TARGET_CONTENT { get; set; }
        public string HRAPP_SLIDE_TARGET_HEADLINE { get; set; }
        public string HRAPP_ENABLED { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ADMIN_HR_MEDIA_SLIDE (MENU_NAME_ID,SLIDE_NAME,SLIDE_IMAGE_PATH,SLIDE_CLICK_ACTION,SLIDE_TARGET_LINK,SLIDE_TARGET_CONTENT,SLIDE_POPUP_WIDTH,SLIDE_POPUP_HEIGHT,ACTIVE,ORDER_NO,VALID_DATE_START,VALID_DATE_END,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY,SLIDE_TARGET_HEADLINE,DELETED,THUMBNAIL_IMAGE_PATH,CATEGORY,HRAPP_SLIDE_IMAGE_PATH,HRAPP_SLIDE_CLICK_ACTION,HRAPP_SLIDE_TARGET_LINK,HRAPP_SLIDE_TARGET_CONTENT,HRAPP_SLIDE_TARGET_HEADLINE,HRAPP_ENABLED) values (:MENU_NAME_ID,:SLIDE_NAME,:SLIDE_IMAGE_PATH,:SLIDE_CLICK_ACTION,:SLIDE_TARGET_LINK,:SLIDE_TARGET_CONTENT,:SLIDE_POPUP_WIDTH,:SLIDE_POPUP_HEIGHT,:ACTIVE,:ORDER_NO,:VALID_DATE_START,:VALID_DATE_END,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY,:SLIDE_TARGET_HEADLINE,:DELETED,:THUMBNAIL_IMAGE_PATH,:CATEGORY,:HRAPP_SLIDE_IMAGE_PATH,:HRAPP_SLIDE_CLICK_ACTION,:HRAPP_SLIDE_TARGET_LINK,:HRAPP_SLIDE_TARGET_CONTENT,:HRAPP_SLIDE_TARGET_HEADLINE,:HRAPP_ENABLED)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ADMIN_HR_MEDIA_SLIDE set  MENU_NAME_ID=:MENU_NAME_ID,SLIDE_NAME=:SLIDE_NAME,SLIDE_IMAGE_PATH=:SLIDE_IMAGE_PATH,SLIDE_CLICK_ACTION=:SLIDE_CLICK_ACTION,SLIDE_TARGET_LINK=:SLIDE_TARGET_LINK,SLIDE_TARGET_CONTENT=:SLIDE_TARGET_CONTENT,SLIDE_POPUP_WIDTH=:SLIDE_POPUP_WIDTH,SLIDE_POPUP_HEIGHT=:SLIDE_POPUP_HEIGHT,ACTIVE=:ACTIVE,ORDER_NO=:ORDER_NO,VALID_DATE_START=:VALID_DATE_START,VALID_DATE_END=:VALID_DATE_END,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY,SLIDE_TARGET_HEADLINE=:SLIDE_TARGET_HEADLINE,DELETED=:DELETED,THUMBNAIL_IMAGE_PATH=:THUMBNAIL_IMAGE_PATH,CATEGORY=:CATEGORY,HRAPP_SLIDE_IMAGE_PATH=:HRAPP_SLIDE_IMAGE_PATH,HRAPP_SLIDE_CLICK_ACTION=:HRAPP_SLIDE_CLICK_ACTION,HRAPP_SLIDE_TARGET_LINK=:HRAPP_SLIDE_TARGET_LINK,HRAPP_SLIDE_TARGET_CONTENT=:HRAPP_SLIDE_TARGET_CONTENT,HRAPP_SLIDE_TARGET_HEADLINE=:HRAPP_SLIDE_TARGET_HEADLINE,HRAPP_ENABLED=:HRAPP_ENABLED where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ADMIN_HR_MEDIA_SLIDE  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DIGIPORT_ADMIN_HR_MEDIA_SLIDE  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
