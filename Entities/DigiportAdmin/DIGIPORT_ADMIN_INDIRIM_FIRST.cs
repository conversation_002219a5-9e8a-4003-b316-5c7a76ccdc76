﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DigiportAdmin
{
    public class DIGIPORT_ADMIN_INDIRIM_FIRST : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal MENU_NAME_ID { get; set; }
        public string BASLIK { get; set; }
        public decimal KATEGORI { get; set; }
        public string KURUM_ADI { get; set; }
        public string AVANTAJ_KOSULLARI { get; set; }
        public System.Nullable<decimal> INDIRIM_ORANI { get; set; }
        public DateTime BASLANGIC_TARIHI { get; set; }
        public DateTime BITIS_TARIHI { get; set; }
        public string LOKASYON { get; set; }
        public string ADRES { get; set; }
        public string TELEFON { get; set; }
        public string HTML_ICERIK { get; set; }
        public string TARGET_LINK { get; set; }
        public decimal CLICK_ACTION { get; set; }
        public System.Nullable<decimal> POPUP_WIDTH { get; set; }
        public System.Nullable<decimal> POPUP_HEIGHT { get; set; }
        public decimal ORDER_NO { get; set; }
        public string ACTIVE { get; set; }
        public string DELETED { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        public string HTML_BASLIK { get; set; }
        public string HRAPP_LOGO_IMAGE_PATH { get; set; }
        public System.Nullable<decimal> HRAPP_CLICK_ACTION { get; set; }
        public string HRAPP_TARGET_LINK { get; set; }
        public string HRAPP_TARGET_CONTENT { get; set; }
        public string HRAPP_TARGET_HEADLINE { get; set; }
        public string HRAPP_ENABLED { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_FIRST (MENU_NAME_ID,BASLIK,KATEGORI,KURUM_ADI,AVANTAJ_KOSULLARI,INDIRIM_ORANI,BASLANGIC_TARIHI,BITIS_TARIHI,LOKASYON,ADRES,TELEFON,HTML_ICERIK,TARGET_LINK,CLICK_ACTION,POPUP_WIDTH,POPUP_HEIGHT,ORDER_NO,ACTIVE,DELETED,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY,HTML_BASLIK,HRAPP_LOGO_IMAGE_PATH,HRAPP_CLICK_ACTION,HRAPP_TARGET_LINK,HRAPP_TARGET_CONTENT,HRAPP_TARGET_HEADLINE,HRAPP_ENABLED) values (:MENU_NAME_ID,:BASLIK,:KATEGORI,:KURUM_ADI,:AVANTAJ_KOSULLARI,:INDIRIM_ORANI,:BASLANGIC_TARIHI,:BITIS_TARIHI,:LOKASYON,:ADRES,:TELEFON,:HTML_ICERIK,:TARGET_LINK,:CLICK_ACTION,:POPUP_WIDTH,:POPUP_HEIGHT,:ORDER_NO,:ACTIVE,:DELETED,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY,:HTML_BASLIK,:HRAPP_LOGO_IMAGE_PATH,:HRAPP_CLICK_ACTION,:HRAPP_TARGET_LINK,:HRAPP_TARGET_CONTENT,:HRAPP_TARGET_HEADLINE,:HRAPP_ENABLED)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_FIRST set  MENU_NAME_ID=:MENU_NAME_ID,BASLIK=:BASLIK,KATEGORI=:KATEGORI,KURUM_ADI=:KURUM_ADI,AVANTAJ_KOSULLARI=:AVANTAJ_KOSULLARI,INDIRIM_ORANI=:INDIRIM_ORANI,BASLANGIC_TARIHI=:BASLANGIC_TARIHI,BITIS_TARIHI=:BITIS_TARIHI,LOKASYON=:LOKASYON,ADRES=:ADRES,TELEFON=:TELEFON,HTML_ICERIK=:HTML_ICERIK,TARGET_LINK=:TARGET_LINK,CLICK_ACTION=:CLICK_ACTION,POPUP_WIDTH=:POPUP_WIDTH,POPUP_HEIGHT=:POPUP_HEIGHT,ORDER_NO=:ORDER_NO,ACTIVE=:ACTIVE,DELETED=:DELETED,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY,HTML_BASLIK=:HTML_BASLIK,HRAPP_LOGO_IMAGE_PATH=:HRAPP_LOGO_IMAGE_PATH,HRAPP_CLICK_ACTION=:HRAPP_CLICK_ACTION,HRAPP_TARGET_LINK=:HRAPP_TARGET_LINK,HRAPP_TARGET_CONTENT=:HRAPP_TARGET_CONTENT,HRAPP_TARGET_HEADLINE=:HRAPP_TARGET_HEADLINE,HRAPP_ENABLED=:HRAPP_ENABLED where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_FIRST  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DIGIPORT_ADMIN_INDIRIM_FIRST  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
