﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DigiportAdmin
{
    public class DIGIPORT_ADMIN_ANASAYFA_SOLSAG_SLIDE : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal MENU_NAME_ID { get; set; }
        public string SLIDE_NAME { get; set; }
        public string SLIDE_IMAGE_PATH { get; set; }
        public decimal SLIDE_CLICK_ACTION { get; set; }
        public string SLIDE_TARGET_LINK { get; set; }
        public string SLIDE_TARGET_CONTENT { get; set; }
        public string SLIDE_TARGET_HEADLINE { get; set; }
        public System.Nullable<decimal> SLIDE_POPUP_WIDTH { get; set; }
        public System.Nullable<decimal> SLIDE_POPUP_HEIGHT { get; set; }
        public string ACTIVE { get; set; }
        public string DELETED { get; set; }
        public decimal ORDER_NO { get; set; }
        public DateTime VALID_DATE_START { get; set; }
        public DateTime VALID_DATE_END { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOLSAG_SLIDE (MENU_NAME_ID,SLIDE_NAME,SLIDE_IMAGE_PATH,SLIDE_CLICK_ACTION,SLIDE_TARGET_LINK,SLIDE_TARGET_CONTENT,SLIDE_POPUP_WIDTH,SLIDE_POPUP_HEIGHT,ACTIVE,DELETED,ORDER_NO,VALID_DATE_START,VALID_DATE_END,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY,SLIDE_TARGET_HEADLINE) values (:MENU_NAME_ID,:SLIDE_NAME,:SLIDE_IMAGE_PATH,:SLIDE_CLICK_ACTION,:SLIDE_TARGET_LINK,:SLIDE_TARGET_CONTENT,:SLIDE_POPUP_WIDTH,:SLIDE_POPUP_HEIGHT,:ACTIVE,:DELETED,:ORDER_NO,:VALID_DATE_START,:VALID_DATE_END,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY,:SLIDE_TARGET_HEADLINE)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOLSAG_SLIDE set  MENU_NAME_ID=:MENU_NAME_ID,SLIDE_NAME=:SLIDE_NAME,SLIDE_IMAGE_PATH=:SLIDE_IMAGE_PATH,SLIDE_CLICK_ACTION=:SLIDE_CLICK_ACTION,SLIDE_TARGET_LINK=:SLIDE_TARGET_LINK,SLIDE_TARGET_CONTENT=:SLIDE_TARGET_CONTENT,SLIDE_POPUP_WIDTH=:SLIDE_POPUP_WIDTH,SLIDE_POPUP_HEIGHT=:SLIDE_POPUP_HEIGHT,ACTIVE=:ACTIVE,DELETED=:DELETED,ORDER_NO=:ORDER_NO,VALID_DATE_START=:VALID_DATE_START,VALID_DATE_END=:VALID_DATE_END,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY,SLIDE_TARGET_HEADLINE=:SLIDE_TARGET_HEADLINE where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOLSAG_SLIDE  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "update  DT_WORKFLOW.DIGIPORT_ADMIN_ANASAYFA_SOLSAG_SLIDE set ACTIVE='0' where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
