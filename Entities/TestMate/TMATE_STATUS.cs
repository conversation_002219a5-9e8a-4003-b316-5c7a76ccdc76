﻿///
///Generated by KB Entity Generator 
///13.06.2023 09:17:22
///
using System;
namespace Entities
{
    public class TMATE_STATUS : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal STATUS_TYPE { get; set; }
        public string STATUS_NAME_TR { get; set; }
        public string STATUS_NAME_EN { get; set; }
        public string AKTIF { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> UPDATED { get; set; }
        public System.Nullable<decimal> UPDATED_BY { get; set; }
        public decimal PROJECT_ID { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.TMATE_STATUS (STATUS_TYPE,STATUS_NAME_TR,STATUS_NAME_EN,AKTIF,CREATED,CREATED_BY,UPDATED,UPDATED_BY,PROJECT_ID) values (:STATUS_TYPE,:STATUS_NAME_TR,:STATUS_NAME_EN,:AKTIF,:CREATED,:CREATED_BY,:UPDATED,:UPDATED_BY,:PROJECT_ID)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.TMATE_STATUS set  STATUS_TYPE=:STATUS_TYPE,STATUS_NAME_TR=:STATUS_NAME_TR,STATUS_NAME_EN=:STATUS_NAME_EN,AKTIF=:AKTIF,CREATED=:CREATED,CREATED_BY=:CREATED_BY,UPDATED=:UPDATED,UPDATED_BY=:UPDATED_BY,PROJECT_ID=:PROJECT_ID where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.TMATE_STATUS  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.TMATE_STATUS  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
