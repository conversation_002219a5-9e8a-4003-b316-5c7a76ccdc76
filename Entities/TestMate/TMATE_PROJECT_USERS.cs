﻿///
///Generated by KB Entity Generator 
///14.06.2023 16:09:05
///
using System;
namespace Entities
{
    public class TMATE_PROJECT_USERS : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public System.Nullable<decimal> DOMAIN_ID { get; set; }
        public System.Nullable<decimal> PROJECT_ID { get; set; }
        public string YAZMA_YETKISI { get; set; }
        public string RAPORLAMA_YETKISI { get; set; }
        public System.Nullable<DateTime> CREATED { get; set; }
        public System.Nullable<decimal> CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        public string AKTIF { get; set; }
        public string AD_GROUP { get; set; }
        public string AD_GROUP_TYPE { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.TMATE_PROJECT_USERS (DOMAIN_ID,PROJECT_ID,YAZMA_YETKISI,RAPORLAMA_YETKISI,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY,AKTIF,AD_GROUP,AD_GROUP_TYPE) values (:DOMAIN_ID,:PROJECT_ID,:YAZMA_YETKISI,:RAPORLAMA_YETKISI,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY,:AKTIF,:AD_GROUP,:AD_GROUP_TYPE)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.TMATE_PROJECT_USERS set  DOMAIN_ID=:DOMAIN_ID,PROJECT_ID=:PROJECT_ID,YAZMA_YETKISI=:YAZMA_YETKISI,RAPORLAMA_YETKISI=:RAPORLAMA_YETKISI,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY,AKTIF=:AKTIF,AD_GROUP=:AD_GROUP,AD_GROUP_TYPE=:AD_GROUP_TYPE where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.TMATE_PROJECT_USERS  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.TMATE_PROJECT_USERS  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
