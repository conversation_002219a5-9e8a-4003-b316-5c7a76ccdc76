﻿///
///Generated by KB Entity Generator 
///06.06.2023 14:34:58
///
using System;
namespace Entities
{
    public class TMATE_PROJECTS : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public System.Nullable<decimal> DOMAIN_ID { get; set; }
        public string PROJECT_NAME { get; set; }
        public string NAME_SURNAME { get; set; }
        public System.Nullable<DateTime> CREATED { get; set; }
        public System.Nullable<decimal> CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        public string AKTIF { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.TMATE_PROJECTS (DOMAIN_ID,PROJECT_NAME,NAME_SURNAME,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY,AKTIF) values (:DOMAIN_ID,:PROJECT_NAME,:NAME_SURNAME,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY,:AKTIF)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.TMATE_PROJECTS set  DOMAIN_ID=:DOMAIN_ID,PROJECT_NAME=:PROJECT_NAME,NAME_SURNAME=:NAME_SURNAME,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY,AKTIF=:AKTIF where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.TMATE_PROJECTS  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.TMATE_PROJECTS  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
