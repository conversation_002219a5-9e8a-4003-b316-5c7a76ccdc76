﻿///
///Generated by KB Entity Generator 
///04.05.2023 11:10:15
///
using System;
namespace Entities
{
    public class TMATE_DOMAINS_ADMINS : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public string DOMAIN_NAME { get; set; }
        public string AD_GROUP_NAME { get; set; }
        public string AKTIF { get; set; }
        public System.Nullable<DateTime> CREATED { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> CREATED_BY { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        public string AD_GROUP_TYPE { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.TMATE_DOMAINS_ADMINS (DOMAIN_NAME,AD_GROUP_NAME,AKTIF,CREATED,LAST_UPDATED,CREATED_BY,LAST_UPDATED_BY,AD_GROUP_TYPE) values (:DOMAIN_NAME,:AD_GROUP_NAME,:AKTIF,:CREATED,:LAST_UPDATED,:CREATED_BY,:LAST_UPDATED_BY,:AD_GROUP_TYPE)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.TMATE_DOMAINS_ADMINS set  DOMAIN_NAME=:DOMAIN_NAME,AD_GROUP_NAME=:AD_GROUP_NAME,AKTIF=:AKTIF,CREATED=:CREATED,LAST_UPDATED=:LAST_UPDATED,CREATED_BY=:CREATED_BY,LAST_UPDATED_BY=:LAST_UPDATED_BY,AD_GROUP_TYPE=:AD_GROUP_TYPE where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.TMATE_DOMAINS_ADMINS  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.TMATE_DOMAINS_ADMINS  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
