﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="WorkflowFileUpload, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_FILE_UPLOAD" schema="DT_WORKFLOW">
    <id name="Id" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="WorkflowInstanceId" column="WF_INSTANCE_ID" />
    <property name="StateInstanceId" column="STATE_INSTANCE_ID" />
    <property name="UploadLoginId" column="UPLOAD_LOGIN_ID" />
    <property name="UploadFilePath" column="UPLOAD_FILE" />
    <property name="UploadComment" column="UPLOAD_FILE_COMMENT" />
    <property name="UploadDate" column="UPLOAD_DATE" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
  </class>
</hibernate-mapping>