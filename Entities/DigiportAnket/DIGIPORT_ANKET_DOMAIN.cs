﻿///
///Generated by KB Entity Generator 
///09.12.2024 14:15:21
///
using System;
namespace Entities
{
    public class DIGIPORT_ANKET_DOMAIN : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public string DOMAIN { get; set; }
        public System.Nullable<decimal> KAYDEDEN { get; set; }
        public System.Nullable<DateTime> KAYIT_TARIHI { get; set; }
        public System.Nullable<decimal> <PERSON><PERSON><PERSON>IR<PERSON> { get; set; }
        public System.Nullable<DateTime> DEGISTIRME_TARIHI { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN (DOMAIN,KAYDEDEN,KAYIT_TARIHI,DEGISTIREN,DEGISTIRME_TARIHI) values (:DOMAIN,:KAYDEDEN,:KAYIT_TARIHI,:DEGISTIREN,:DEGISTIRME_TARIHI)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN set  DOMAIN=:DOMAIN,KAYDEDEN=:KAYDEDEN,KAYIT_TARIHI=:KAYIT_TARIHI,DEGISTIREN=:DEGISTIREN,DEGISTIRME_TARIHI=:DEGISTIRME_TARIHI where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
