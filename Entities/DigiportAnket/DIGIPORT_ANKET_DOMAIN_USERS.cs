﻿///
///Generated by KB Entity Generator 
///09.12.2024 14:56:37
///
using System;
namespace Entities
{
    public class DIGIPORT_ANKET_DOMAIN_USERS : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public System.Nullable<decimal> DOMAIN_ID { get; set; }
        public System.Nullable<decimal> DOMAIN_USER_LOGIN_ID { get; set; }
        public System.Nullable<decimal> KAYDEDEN { get; set; }
        public System.Nullable<DateTime> KAYIT_TARIHI { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN_USERS (DOMAIN_ID,DOMAIN_USER_LOGIN_ID,KAYDEDEN,KAYIT_TARIHI) values (:DOMAIN_ID,:DOMAIN_USER_LOGIN_ID,:KAYDEDEN,:KAYIT_TARIHI)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN_USERS set  DOMAIN_ID=:DOMAIN_ID,DOMAIN_USER_LOGIN_ID=:DOMAIN_USER_LOGIN_ID,KAYDEDEN=:KAYDEDEN,KAYIT_TARIHI=:KAYIT_TARIHI where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN_USERS  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DIGIPORT_ANKET_DOMAIN_USERS  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
