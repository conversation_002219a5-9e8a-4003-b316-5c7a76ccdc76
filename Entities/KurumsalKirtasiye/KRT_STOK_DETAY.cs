﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

///
///Generated by KB Entity Generator 
///20.02.2023 08:43:21

namespace Entities
{
    public class KRT_STOK_DETAY : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal STOK_GRUP_ID { get; set; }
        public string STOK_ADI { get; set; }
        public string STOK_KODU { get; set; }
        public decimal BIRIM_FIYAT { get; set; }
        public string PARA_BIRIMI { get; set; }
        public decimal SENE { get; set; }
        public string AKTIF { get; set; }
        public decimal CREATED_BY { get; set; }
        public DateTime CREATED { get; set; }
        public System.Nullable<decimal> UPDATED_BY { get; set; }
        public System.Nullable<DateTime> UPDATED { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.KRT_STOK_DETAY (STOK_GRUP_ID,STOK_ADI,STOK_KODU,BIRIM_FIYAT,PARA_BIRIMI,SENE,AKTIF,CREATED_BY,CREATED,UPDATED_BY,UPDATED) values (:STOK_GRUP_ID,:STOK_ADI,:STOK_KODU,:BIRIM_FIYAT,:PARA_BIRIMI,:SENE,:AKTIF,:CREATED_BY,:CREATED,:UPDATED_BY,:UPDATED)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.KRT_STOK_DETAY set  STOK_GRUP_ID=:STOK_GRUP_ID,STOK_ADI=:STOK_ADI,STOK_KODU=:STOK_KODU,BIRIM_FIYAT=:BIRIM_FIYAT,PARA_BIRIMI=:PARA_BIRIMI,SENE=:SENE,AKTIF=:AKTIF,CREATED_BY=:CREATED_BY,CREATED=:CREATED,UPDATED_BY=:UPDATED_BY,UPDATED=:UPDATED where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.KRT_STOK_DETAY  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "update DT_WORKFLOW.KRT_STOK_DETAY set AKTIF=0  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
