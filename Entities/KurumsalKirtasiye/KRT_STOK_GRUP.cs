﻿using Entity_Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

///
///Generated by KB Entity Generator 
///17.02.2023 14:03:04
namespace Entities
{
    public class KRT_STOK_GRUP : EntityBase
    {
        #region Entity Değerleri
        public string STOK_GRUP_ADI { get; set; }
        public string AKTIF { get; set; }
        public string TALEP_FORMUNDA_GOZUKUR { get; set; }
        public string OZEL_GRUP { get; set; }
        public decimal CREATED_BY { get; set; }
        public DateTime CREATED { get; set; }
        public System.Nullable<decimal> UPDATED_BY { get; set; }
        public System.Nullable<DateTime> UPDATED { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.KRT_STOK_GRUP (STOK_GRUP_ADI,TALEP_FORMUNDA_GOZUKUR,OZEL_GRUP,AKTIF,CREATED_BY,CREATED,UPDATED_BY,UPDATED) values (:STOK_GRUP_ADI,:TALEP_FORMUNDA_GOZUKUR,:OZEL_GRUP,:AKTIF,:CREATED_BY,:CREATED,:UPDATED_BY,:UPDATED)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.KRT_STOK_GRUP set  STOK_GRUP_ADI=:STOK_GRUP_ADI,TALEP_FORMUNDA_GOZUKUR=:TALEP_FORMUNDA_GOZUKUR,OZEL_GRUP=:OZEL_GRUP,AKTIF=:AKTIF,CREATED_BY=:CREATED_BY,CREATED=:CREATED,UPDATED_BY=:UPDATED_BY,UPDATED=:UPDATED where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.KRT_STOK_GRUP  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "update DT_WORKFLOW.KRT_STOK_GRUP set AKTIF=0  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}

